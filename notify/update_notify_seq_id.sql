-- 会员相关表的序列更新
SELECT set_sequence_next_value('t_system_message', 't_system_message_id_seq');

-- 通知相关表的序列更新

-- 系统消息表序列更新
SELECT set_sequence_next_value('t_system_message', 't_system_message_id_seq');
SELECT set_sequence_next_value('t_system_message_user', 't_system_message_user_id_seq');

-- 互动消息表序列更新
SELECT set_sequence_next_value('t_active_message', 't_active_message_id_seq');
SELECT set_sequence_next_value('t_active_message_user', 't_active_message_user_id_seq');
