package com.gw.notify.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 异步任务线程池配置
 */
@Configuration
@EnableAsync
public class AsyncConfig {

    /**
     * 核心线程数，默认为系统可用处理器数量
     */
    @Value("${async.executor.thread.core-pool-size:4}")
    private int corePoolSize;

    /**
     * 最大线程数，默认为10
     */
    @Value("${async.executor.thread.max-pool-size:10}")
    private int maxPoolSize;

    /**
     * 任务队列容量，默认为50
     */
    @Value("${async.executor.thread.queue-capacity:50}")
    private int queueCapacity;

    /**
     * 线程池维护线程所允许的空闲时间，默认为60秒
     */
    @Value("${async.executor.thread.keep-alive-seconds:60}")
    private int keepAliveSeconds;

    /**
     * 线程名称前缀
     */
    @Value("${async.executor.thread.name-prefix:async-service-}")
    private String threadNamePrefix;

    /**
     * 配置线程池
     *
     * @return Executor 线程池执行器
     */
    @Bean(name = "taskExecutor")
    public Executor taskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        // 核心线程数
        executor.setCorePoolSize(corePoolSize);
        // 最大线程数
        executor.setMaxPoolSize(maxPoolSize);
        // 队列大小
        executor.setQueueCapacity(queueCapacity);
        // 线程最大空闲时间
        executor.setKeepAliveSeconds(keepAliveSeconds);
        // 线程名前缀
        executor.setThreadNamePrefix(threadNamePrefix);

        // 当线程池达到最大大小且队列已满时，拒绝任务的策略
        // CALLER_RUNS：不在新线程中执行任务，而是由调用者所在的线程来执行
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());

        // 等待所有任务结束后再关闭线程池
        executor.setWaitForTasksToCompleteOnShutdown(true);
        // 等待时间（默认为0，此时立即停止），并没等待xx秒后强制停止
        executor.setAwaitTerminationSeconds(60);

        // 初始化线程池
        executor.initialize();
        return executor;
    }
}