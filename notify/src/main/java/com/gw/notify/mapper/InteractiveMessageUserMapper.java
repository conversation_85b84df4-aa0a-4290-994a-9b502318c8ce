package com.gw.notify.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gw.notify.entity.InteractiveMessageUserEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;
import java.util.Optional;

/**
 * 互动消息用户关联表 Mapper 接口
 */
@Mapper
public interface InteractiveMessageUserMapper extends BaseMapper<InteractiveMessageUserEntity> {

    /**
     * 将指定用户的某条消息标记为已读
     *
     * @param messageId 消息ID
     * @param username  用户名
     * @return 影响行数
     */
    @Update("UPDATE t_active_message_user SET status = 2 WHERE message_id = #{messageId} AND username = #{username} AND status = 1")
    int markAsRead(@Param("messageId") Long messageId, @Param("username") String username);

    @Select("SELECT * FROM t_active_message_user WHERE message_id = #{messageId} AND username = #{username} Limit 1")
    Optional<InteractiveMessageUserEntity> findByMessageIdAndUsername(@Param("messageId") Long messageId, @Param("username") String username);

    /**
     * 根据消息ID删除所有用户关联记录
     *
     * @param messageId 消息ID
     * @return 影响行数
     */
    @Update("UPDATE t_active_message_user SET deleted = 1 WHERE message_id = #{messageId}")
    int deleteByMessageId(@Param("messageId") Long messageId);

    @Select("SELECT * FROM t_active_message_user WHERE username = #{username} ORDER BY create_time DESC")
    List<InteractiveMessageUserEntity> pageByUsernameOrderByCreateTimeDesc(@Param("username") String username);

    @Update("UPDATE t_active_message_user SET status = 2 WHERE username = #{username}")
    void markAllAsReadByUsername(@Param("username") String username);
} 