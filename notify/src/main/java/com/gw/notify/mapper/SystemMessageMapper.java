package com.gw.notify.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gw.notify.dto.SystemMessageQueryDTO;
import com.gw.notify.entity.SystemMessageEntity;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Optional;

/**
 * 系统消息数据访问接口
 */
public interface SystemMessageMapper extends BaseMapper<SystemMessageEntity> {
    /**
     * 根据ID查询消息
     *
     * @param id 消息ID
     * @return 消息实体
     */
    @Select("SELECT * FROM t_system_message WHERE id = #{id} AND deleted = 0")
    Optional<SystemMessageEntity> findById(@Param("id") Long id);

    /**
     * 查询关联特定智能体的系统消息
     *
     * @param agentId 智能体ID
     * @return 系统消息列表
     */
    @Select("SELECT * FROM t_system_message WHERE associated_with_agent = 1 AND agent_id = #{agentId} AND deleted = 0 ORDER BY create_time DESC")
    List<SystemMessageEntity> findMessagesByAgentId(@Param("agentId") Long agentId);

    /**
     * 根据ID查询消息
     *
     * @param id 消息ID
     * @return 消息实体
     */
    @Select("SELECT * FROM t_system_message WHERE id = #{id} AND deleted = 0")
    SystemMessageEntity findValidById(@Param("id") Long id);

    @Select("SELECT * FROM t_system_message WHERE id IN (#{ids}) AND deleted = 0")
    List<SystemMessageEntity> findAllByIds(@Param("ids") List<Long> ids);

    List<SystemMessageEntity> pageByUsername(SystemMessageQueryDTO query);

    List<SystemMessageEntity> page(SystemMessageQueryDTO query);

    List<SystemMessageEntity> pagePublic(SystemMessageQueryDTO query);
} 