package com.gw.notify.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 互动消息VO
 */
@Data
@Schema(description = "互动消息详情")
public class InteractiveMessageVO {

    /**
     * 消息ID
     */
    @Schema(description = "消息ID")
    private Long id;

    /**
     * 消息内容
     */
    @Schema(description = "消息内容")
    private String content;

    /**
     * 消息类型：1-普通消息，2-重要消息，3-紧急消息
     */
    @Schema(description = "消息类型：1-普通消息，2-重要消息，3-紧急消息")
    private Integer level;

    /**
     * 是否关联智能体：0-否，1-是
     */
    @Schema(description = "是否关联智能体：0-否，1-是")
    private Integer associatedWithAgent;

    /**
     * 关联的智能体ID
     */
    @Schema(description = "关联的智能体ID")
    private Long agentId;

    /**
     * 关联的智能体名称
     */
    @Schema(description = "关联的智能体名称")
    private String agentName;

    /**
     * 消息有效期开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Schema(description = "消息有效期开始时间")
    @JsonIgnore
    private LocalDate validStartDate;

    /**
     * 消息有效期结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Schema(description = "消息有效期结束时间")
    @JsonIgnore
    private LocalDate validEndDate;

    /**
     * 消息状态：1-未读，2-已读
     */
    @Schema(description = "消息状态：1-未读，2-已读")
    private Integer status;

    /**
     * 消息创建者
     */
    @Schema(description = "消息创建者")
    @JsonIgnore
    private String creatorName;

    @Schema(description = "消息创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
} 