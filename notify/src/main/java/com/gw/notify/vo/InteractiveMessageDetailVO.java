package com.gw.notify.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 互动消息详情VO
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "互动消息详细信息")
public class InteractiveMessageDetailVO extends InteractiveMessageVO {

    /**
     * 目标用户名列表
     */
    @Schema(description = "目标用户名列表")
    private List<String> targetUsernames;
} 