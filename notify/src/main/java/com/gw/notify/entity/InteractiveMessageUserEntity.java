package com.gw.notify.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

import static com.baomidou.mybatisplus.annotation.IdType.AUTO;

/**
 * 系统消息用户关联实体
 */
@Data
@TableName(value = "t_active_message_user", autoResultMap = true)
public class InteractiveMessageUserEntity { // Extend BaseEntity if needed
    @TableId(type = AUTO)
    private Long id;
    /**
     * 关联的消息ID (Foreign Key to t_system_message)
     */
    @TableField("message_id")
    private Long messageId;

    /**
     * 关联的用户名
     */
    @TableField("username")
    private String username;

    /**
     * 消息状态：1-未读，2-已读
     * Using Integer to align with SystemMessageEntity status type
     */
    @TableField("status")
    private Integer status = 1;
    private LocalDateTime createTime = LocalDateTime.now();

} 