package com.gw.notify.controller;

import com.github.pagehelper.PageInfo;
import com.gw.common.dto.ItemIdDTO;
import com.gw.common.dto.PageBaseRequest;
import com.gw.common.dto.ResponseResult;
import com.gw.common.exception.BusinessException;
import com.gw.common.notify.dto.InteractiveMessageCreateDTO;
import com.gw.common.notify.dto.InteractiveNotifySubmitDTO;
import com.gw.common.user.constant.UserCommonCacheConstant;
import com.gw.common.user.context.UserContextUtil;
import com.gw.common.user.service.UserProxyService;
import com.gw.common.user.vo.UserBaseContentVo;
import com.gw.common.vo.PageBaseContentVo;
import com.gw.common.vo.PaginationVo;
import com.gw.notify.config.CacheProperties;
import com.gw.notify.dto.InteractiveMessageQueryDTO;
import com.gw.notify.dto.InteractiveMessageUpdateDTO;
import com.gw.notify.entity.InteractiveMessageEntity;
import com.gw.notify.entity.InteractiveMessageUserEntity;
import com.gw.notify.mapper.ModelMapperConvert;
import com.gw.notify.service.InteractiveMessageService;
import com.gw.notify.service.InteractiveMessageUserService;
import com.gw.notify.vo.InteractiveMessageDetailVO;
import com.gw.notify.vo.InteractiveMessageVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.modelmapper.ModelMapper;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static com.gw.common.exception.BusinessExceptionCode.FAIL_CODE;

/**
 * 互动消息控制器
 */
@RestController
@RequestMapping("/api/v1/notify/interactive_message")
@RequiredArgsConstructor
@Tag(name = "互动消息管理", description = "互动消息相关API")
@Log4j2
public class InteractiveMessageController {

    private final InteractiveMessageService interactiveMessageService;
    private final InteractiveMessageUserService interactiveMessageUserService;
    private final UserProxyService userProxyService;
    private final CacheProperties cacheProperties;

    /**
     * 填充互动消息信息
     */
    private void fillInteractiveMessage(InteractiveMessageCreateDTO req, InteractiveMessageEntity entity) {
        if (req == null || entity == null) {
            throw new IllegalArgumentException("Request or entity cannot be null");
        }
        if (req.getValidStartDate() == null) {
            req.setValidStartDate(LocalDate.now());
        }
        if (req.getValidEndDate() == null) {
            req.setValidEndDate(LocalDate.MAX);
        }
        String username = UserContextUtil.getCurrentUsername();
        ModelMapper modelMapper = ModelMapperConvert.getNotifyModelMapper();
        entity.setUsernamesFromList(req.getTargetUsernames());
        // 基本字段映射
        modelMapper.map(req, entity);

        // 检查智能体关联
        if (req.getAssociatedWithAgent() == 1 && req.getAgentId() == null) {
            throw new BusinessException(FAIL_CODE.getCode(), "关联智能体时，智能体ID不能为空");
        }

        // 检查有效期
        if (req.getValidStartDate().isAfter(req.getValidEndDate())) {
            throw new BusinessException(FAIL_CODE.getCode(), "开始时间不能晚于结束时间");
        }
        List<InteractiveMessageUserEntity> users = new ArrayList<>();
        
        Map<String, UserBaseContentVo> userMap = userProxyService.findAllUserMap(cacheProperties.getCacheName(UserCommonCacheConstant.USER_BASE_MAP_CACHE_KEY));
        if (req.getTargetUsernames() == null || req.getTargetUsernames().isEmpty()) {
            for (UserBaseContentVo userBaseContentVo : userMap.values()) {
                InteractiveMessageUserEntity userEntity = new InteractiveMessageUserEntity();
                userEntity.setMessageId(entity.getId());
                userEntity.setUsername(userBaseContentVo.getUsername());
                userEntity.setStatus(1);
                users.add(userEntity);
            }
        } else {
            for (String username1 : req.getTargetUsernames()) {
                if (userMap.get(username1) == null) {
                    throw new BusinessException(FAIL_CODE.getCode(), username1 + " 用户不存在");
                }
                InteractiveMessageUserEntity userEntity = new InteractiveMessageUserEntity();
                userEntity.setMessageId(entity.getId());
                userEntity.setUsername(username1);
                userEntity.setStatus(1);
                users.add(userEntity);
            }
        }
        entity.setUsers(users);
        // 设置更新人和更新时间
        entity.setUpdater(username);
        entity.setUpdateTime(LocalDateTime.now());
    }

    /**
     * 创建互动消息
     */
    @Operation(summary = "创建互动消息", description = "创建一条新的互动消息")
    @PostMapping("")
    public ResponseResult<?> createInteractiveMessage(@RequestBody @Valid InteractiveMessageCreateDTO req) {
        String username = UserContextUtil.getCurrentUsername();
        InteractiveMessageEntity entity = new InteractiveMessageEntity();
        fillInteractiveMessage(req, entity);
        entity.setCreator(username);
        interactiveMessageService.createMessage(entity);
        return ResponseResult.success(null);
    }

    @Operation(summary = "服务自动创建互动消息", description = "服务创建一条新的互动消息")
    @PostMapping("auto_create")
    public ResponseResult<?> autoCreateInteractiveMessage(@RequestBody @Valid InteractiveNotifySubmitDTO req) {
        log.info("autoCreateInteractiveMessage req:{}", req);
        InteractiveMessageEntity entity = new InteractiveMessageEntity();
        fillInteractiveMessage(req, entity);
        entity.setCreator(req.getUsername());
        interactiveMessageService.createMessage(entity);
        return ResponseResult.success(null);
    }

    /**
     * 更新互动消息
     */
    @Operation(summary = "更新互动消息", description = "更新互动消息信息")
    @PostMapping("update")
    public ResponseResult<?> updateInteractiveMessage(@RequestBody @Valid InteractiveMessageUpdateDTO req) {
        String username = UserContextUtil.getCurrentUsername();
        InteractiveMessageEntity entity = interactiveMessageService.findById(req.getId());

        if (!entity.getCreator().equals(username)) {
            return ResponseResult.failure(FAIL_CODE.getCode(), "无权限操作");
        }

        // 映射更新字段
        ModelMapper modelMapper = ModelMapperConvert.getNotifyModelMapper();
        modelMapper.map(req, entity);

        // 检查智能体关联
        if (req.getAssociatedWithAgent() == 1 && req.getAgentId() == null) {
            return ResponseResult.failure(FAIL_CODE.getCode(), "关联智能体时，智能体ID不能为空");
        }

        // 检查有效期
        if (req.getValidStartTime().isAfter(req.getValidEndTime())) {
            return ResponseResult.failure(FAIL_CODE.getCode(), "开始时间不能晚于结束时间");
        }

        entity.setUpdater(username);
        entity.setUpdateTime(LocalDateTime.now());
        interactiveMessageService.updateMessage(entity);
        return ResponseResult.success(null);
    }

    /**
     * 标记消息为已读
     */
    @Operation(summary = "标记消息为已读", description = "将互动消息标记为已读")
    @PostMapping("my_mark_read")
    public ResponseResult<?> markMessageAsRead(@RequestBody @Valid ItemIdDTO req) {
        String username = UserContextUtil.getCurrentUsername();

        interactiveMessageUserService.markAsRead(req.getId(), username);
        return ResponseResult.success(null);
    }

    /**
     * 标记所有消息为已读
     */
    @Operation(summary = "标记我的所有消息为已读", description = "标记我的所有消息为已读")
    @PostMapping("my_mark_all_read")
    public ResponseResult<?> markAllMessageAsRead(@RequestBody @Valid ItemIdDTO req) {
        String username = UserContextUtil.getCurrentUsername();
        interactiveMessageUserService.markAllAsReadByUsername(username);
        return ResponseResult.success(null);
    }

    /**
     * 删除互动消息
     */
    @Operation(summary = "删除互动消息", description = "删除指定的互动消息")
    @PostMapping("delete")
    public ResponseResult<?> deleteInteractiveMessage(@RequestBody @Valid ItemIdDTO req) {
        String username = UserContextUtil.getCurrentUsername();
        InteractiveMessageEntity entity = interactiveMessageService.findById(req.getId());
        entity.setUpdater(username);
        entity.setUpdateTime(LocalDateTime.now());
        interactiveMessageService.deleteMessage(entity);
        return ResponseResult.success(null);
    }

    /**
     * 获取互动消息详情
     */
    @Operation(summary = "获取我的互动消息详情", description = "获取我的互动消息详情")
    @PostMapping("get")
    public ResponseResult<InteractiveMessageVO> getInteractiveMessageDetail(@RequestBody @Valid ItemIdDTO req) {
        String username = UserContextUtil.getCurrentUsername();
        boolean ret = interactiveMessageUserService.existsByMessageIdAndUsername(req.getId(), username);
        if (!ret) {
            return ResponseResult.failure(FAIL_CODE.getCode(), "无权限查看");
        }
        InteractiveMessageEntity entity = interactiveMessageService.findById(req.getId());
        return ResponseResult.success(convertToVO(entity));
    }

    /**
     * 获取当前用户的有效互动消息
     */
    @Operation(summary = "获取当前用户的有效互动消息", description = "获取当前登录用户的有效互动消息")
    @PostMapping("/my_valid")
    public ResponseResult<PageBaseContentVo<InteractiveMessageVO>> getMyValidMessages(@RequestBody PageBaseRequest<InteractiveMessageQueryDTO> params) {
        String username = UserContextUtil.getCurrentUsername();
        InteractiveMessageQueryDTO filter = params.getFilter();
        if (filter == null) {
            filter = new InteractiveMessageQueryDTO();
        }
        filter.setUsername(username);
        PageInfo<InteractiveMessageEntity> page = interactiveMessageService.pageValidMessagesByUsername(
                params.getCurrent(),
                params.getPageSize(), filter);
        List<InteractiveMessageEntity> entities = page.getList();
        if (entities.isEmpty()) {
            return ResponseResult.success(new PageBaseContentVo<>(Collections.emptyList(), new PaginationVo(page.getTotal(), page.getPageNum(), page.getPageSize())));
        }

        List<InteractiveMessageVO> vos = entities.stream()
                .map(this::convertToVO)
                .toList();
        return ResponseResult.success(new PageBaseContentVo<>(vos, new PaginationVo(page.getTotal(), page.getPageNum(), page.getPageSize())));
    }

    /**
     * 分页获取互动消息
     */
    @Operation(summary = "分页获取互动消息", description = "分页获取互动消息")
    @PostMapping("/page")
    public ResponseResult<PageBaseContentVo<InteractiveMessageDetailVO>> pageInteractiveMessages(
            @RequestBody @Valid PageBaseRequest<InteractiveMessageQueryDTO> params) {
        InteractiveMessageQueryDTO filter = params.getFilter();
        if (filter == null) {
            filter = new InteractiveMessageQueryDTO();
        }

        PageInfo<InteractiveMessageEntity> page = interactiveMessageService.pageMessages(
                params.getCurrent(),
                params.getPageSize(),
                filter
        );

        List<InteractiveMessageEntity> entities = page.getList();
        if (entities.isEmpty()) {
            return ResponseResult.success(new PageBaseContentVo<>(Collections.emptyList(), new PaginationVo(page.getTotal(), page.getPageNum(), page.getPageSize())));
        }

        List<InteractiveMessageDetailVO> vos = entities.stream()
                .map(this::convertToDetailVO)
                .toList();
        return ResponseResult.success(new PageBaseContentVo<>(vos, new PaginationVo(page.getTotal(), page.getPageNum(), page.getPageSize())));
    }

    private InteractiveMessageVO convertToVO(InteractiveMessageEntity entity) {
        if (entity == null) {
            return null;
        }

        ModelMapper modelMapper = ModelMapperConvert.getNotifyModelMapper();
        return modelMapper.map(entity, InteractiveMessageVO.class);
    }

    private InteractiveMessageDetailVO convertToDetailVO(InteractiveMessageEntity entity) {
        if (entity == null) {
            return null;
        }

        ModelMapper modelMapper = ModelMapperConvert.getNotifyModelMapper();
        InteractiveMessageDetailVO vo = modelMapper.map(entity, InteractiveMessageDetailVO.class);
        vo.setTargetUsernames(entity.getUsernamesToList());
        return vo;
    }
} 