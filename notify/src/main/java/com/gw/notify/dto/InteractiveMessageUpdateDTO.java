package com.gw.notify.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;

/**
 * 互动消息更新DTO
 */
@Data
@Schema(description = "互动消息更新请求")
public class InteractiveMessageUpdateDTO {
    /**
     * 消息ID
     */
    @NotNull(message = "消息ID不能为空")
    @Schema(description = "消息ID")
    private Long id;

    /**
     * 消息内容
     */
    @Schema(description = "消息内容")
    private String content;

    /**
     * 消息等级：1-普通消息，2-重要消息，3-紧急消息
     */
    @Schema(description = "消息等级：1-普通消息，2-重要消息，3-紧急消息")
    private Integer level;

    /**
     * 是否关联智能体：0-否，1-是
     */
    @Schema(description = "是否关联智能体：0-否，1-是")
    private Integer associatedWithAgent;

    /**
     * 关联的智能体ID
     */
    @Schema(description = "关联的智能体ID")
    private Long agentId;

    /**
     * 消息有效期开始时间
     */
    @Schema(description = "消息有效期开始时间")
    private LocalDate validStartTime;

    /**
     * 消息有效期结束时间
     */
    @Schema(description = "消息有效期结束时间")
    private LocalDate validEndTime;

    /**
     * 目标用户名列表
     */
    @Schema(description = "目标用户名列表")
    private List<String> targetUsernames;
} 