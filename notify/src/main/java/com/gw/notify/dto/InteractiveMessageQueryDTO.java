package com.gw.notify.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 互动消息查询DTO
 */
@Data
@Schema(description = "互动消息查询条件")
public class InteractiveMessageQueryDTO {
    @JsonIgnore
    private String username;

    /**
     * 是否关联智能体：0-否，1-是
     */
    @Schema(description = "是否关联智能体：0-否，1-是")
    private Integer associatedWithAgent;

    /**
     * 关联的智能体ID
     */
    @Schema(description = "关联的智能体ID")
    private Long agentId;

    /**
     * 消息状态：1-未读，2-已读 (用于用户过滤)
     */
    @Schema(description = "消息状态：1-未读，2-已读 (用于用户过滤)")
    private Integer status;
} 