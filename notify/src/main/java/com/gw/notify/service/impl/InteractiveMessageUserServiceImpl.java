package com.gw.notify.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.gw.notify.entity.InteractiveMessageUserEntity;
import com.gw.notify.mapper.InteractiveMessageUserMapper;
import com.gw.notify.service.InteractiveMessageUserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * 互动消息用户关联服务实现
 */
@Service
@RequiredArgsConstructor
@Log4j2
public class InteractiveMessageUserServiceImpl extends ServiceImpl<InteractiveMessageUserMapper, InteractiveMessageUserEntity> implements InteractiveMessageUserService {

    @Override
    public void markAsRead(Long messageId, String username) {
        this.baseMapper.markAsRead(messageId, username);
    }

    @Override
    public void markAllAsReadByUsername(String username) {
        this.baseMapper.markAllAsReadByUsername(username);
    }

    @Override
    public boolean existsByMessageIdAndUsername(Long messageId, String username) {
        Optional<InteractiveMessageUserEntity> entity = this.baseMapper.findByMessageIdAndUsername(messageId, username);
        return entity.isPresent();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByMessageId(Long messageId) {
        this.baseMapper.deleteByMessageId(messageId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insertBatch(List<InteractiveMessageUserEntity> entityList) {
        this.saveBatch(entityList);
    }

    @Override
    public PageInfo<InteractiveMessageUserEntity> pageByUsername(int pageNum, int pageSize, String username) {
        PageHelper.startPage(pageNum, pageSize);
        List<InteractiveMessageUserEntity> list = this.baseMapper.pageByUsernameOrderByCreateTimeDesc(username);
        return new PageInfo<>(list);
    }
} 