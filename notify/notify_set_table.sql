-- 系统消息表
-- 删除已存在的表和序列
DROP TABLE IF EXISTS "public"."t_system_message";
DROP SEQUENCE IF EXISTS t_system_message_id_seq;

-- 创建序列
CREATE SEQUENCE t_system_message_id_seq START 1 CACHE 1;

-- 创建表
CREATE TABLE t_system_message
(
    -- BaseEntity字段
    id                    BIGINT PRIMARY KEY DEFAULT NEXTVAL('t_system_message_id_seq'::regclass),
    create_time           TIMESTAMP          DEFAULT CURRENT_TIMESTAMP,
    update_time           TIMESTAMP          DEFAULT CURRENT_TIMESTAMP,
    deleted               INTEGER            DEFAULT 0,
    creator               VARCHAR(255),
    updater               VA<PERSON>HAR(255),

    -- SystemMessageEntity特有字段
    content               TEXT    NOT NULL,
    target_users          TEXT,
    level                 INTEGER NOT NULL, -- Renamed from type
    associated_with_agent INTEGER            DEFAULT 0,
    agent_id              BIGINT,
    valid_start_date      DATE    NOT NULL, -- Changed type to DATE
    valid_end_date        DATE    NOT NULL  -- Changed type to DATE
    -- Removed status and target_username as they moved/are not directly stored
);

-- 添加表注释
COMMENT ON TABLE t_system_message IS '系统消息表';
COMMENT ON COLUMN t_system_message.id IS '主键ID';
COMMENT ON COLUMN t_system_message.create_time IS '创建时间';
COMMENT ON COLUMN t_system_message.update_time IS '更新时间';
COMMENT ON COLUMN t_system_message.deleted IS '删除标记(0:未删除,1:已删除)';
COMMENT ON COLUMN t_system_message.creator IS '创建者';
COMMENT ON COLUMN t_system_message.updater IS '更新者';
COMMENT ON COLUMN t_system_message.content IS '消息内容';
COMMENT ON COLUMN t_system_message.level IS '消息等级：1-普通消息，2-重要消息，3-紧急消息';
COMMENT ON COLUMN t_system_message.associated_with_agent IS '是否关联智能体：0-否，1-是';
COMMENT ON COLUMN t_system_message.agent_id IS '关联的智能体ID';
COMMENT ON COLUMN t_system_message.valid_start_date IS '消息有效期开始日期';
COMMENT ON COLUMN t_system_message.valid_end_date IS '消息有效期结束日期';

-- 创建索引
-- Removed idx_system_message_user as target_username is removed
CREATE INDEX idx_system_message_agent ON t_system_message (agent_id) WHERE deleted = 0 AND associated_with_agent = 1;
CREATE INDEX idx_system_message_valid_date ON t_system_message (valid_start_date, valid_end_date) WHERE deleted = 0;
-- Renamed index

-- 系统消息用户关联表
-- 删除已存在的表和序列
DROP TABLE IF EXISTS "public"."t_system_message_user";
DROP SEQUENCE IF EXISTS t_system_message_user_id_seq;

-- 创建序列
CREATE SEQUENCE t_system_message_user_id_seq START 1 CACHE 1;

-- 创建表
CREATE TABLE t_system_message_user
(
    id          BIGINT PRIMARY KEY DEFAULT NEXTVAL('t_system_message_user_id_seq'::regclass),
    message_id  BIGINT       NOT NULL,
    username    VARCHAR(255) NOT NULL,
    status      INTEGER            DEFAULT 1, -- 1:未读, 2:已读
    create_time TIMESTAMP          DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_system_message FOREIGN KEY (message_id) REFERENCES t_system_message (id) ON DELETE CASCADE
);

-- 添加表注释
COMMENT ON TABLE t_system_message_user IS '系统消息用户关联表';
COMMENT ON COLUMN t_system_message_user.id IS '主键ID';
COMMENT ON COLUMN t_system_message_user.message_id IS '关联的消息ID (外键指向 t_system_message.id)';
COMMENT ON COLUMN t_system_message_user.username IS '关联的用户名';
COMMENT ON COLUMN t_system_message_user.status IS '消息状态：1-未读，2-已读';
COMMENT ON COLUMN t_system_message_user.create_time IS '记录创建时间';

-- 创建索引
CREATE INDEX idx_system_message_user_username ON t_system_message_user (username);
CREATE INDEX idx_system_message_user_message_id ON t_system_message_user (message_id);
CREATE INDEX idx_system_message_user_status ON t_system_message_user (status);

-- 互动消息表
-- 删除已存在的表和序列
DROP TABLE IF EXISTS "public"."t_active_message";
DROP SEQUENCE IF EXISTS t_active_message_id_seq;

-- 创建序列
CREATE SEQUENCE t_active_message_id_seq START 1 CACHE 1;

-- 创建表
CREATE TABLE t_active_message
(
    -- BaseEntity字段
    id                    BIGINT PRIMARY KEY DEFAULT NEXTVAL('t_active_message_id_seq'::regclass),
    create_time           TIMESTAMP          DEFAULT CURRENT_TIMESTAMP,
    update_time           TIMESTAMP          DEFAULT CURRENT_TIMESTAMP,
    deleted               INTEGER            DEFAULT 0,
    creator               VARCHAR(255),
    updater               VARCHAR(255),

    -- InteractiveMessageEntity特有字段
    content               TEXT    NOT NULL,
    target_users          TEXT,
    type                  INTEGER            DEFAULT 0, -- 消息类型：1-智能体收藏，2-智能体点赞，3-智能体评论，4-智能体使用，8-会话评论，9-会话点赞
    session_id            VARCHAR(255),                 -- 会话ID
    level                 INTEGER NOT NULL,             -- 消息等级：1-普通消息，2-重要消息，3-紧急消息
    associated_with_agent INTEGER            DEFAULT 0,
    agent_id              BIGINT,
    valid_start_date      DATE    NOT NULL,
    valid_end_date        DATE    NOT NULL
);

-- 添加表注释
COMMENT ON TABLE t_active_message IS '互动消息表';
COMMENT ON COLUMN t_active_message.id IS '主键ID';
COMMENT ON COLUMN t_active_message.create_time IS '创建时间';
COMMENT ON COLUMN t_active_message.update_time IS '更新时间';
COMMENT ON COLUMN t_active_message.deleted IS '删除标记(0:未删除,1:已删除)';
COMMENT ON COLUMN t_active_message.creator IS '创建者';
COMMENT ON COLUMN t_active_message.updater IS '更新者';
COMMENT ON COLUMN t_active_message.content IS '消息内容';
COMMENT ON COLUMN t_active_message.target_users IS '目标用户（逗号分隔的用户名列表）';
COMMENT ON COLUMN t_active_message.type IS '消息类型：1-智能体收藏，2-智能体点赞，3-智能体评论，4-智能体使用，8-会话评论，9-会话点赞';
COMMENT ON COLUMN t_active_message.session_id IS '会话ID';
COMMENT ON COLUMN t_active_message.level IS '消息等级：1-普通消息，2-重要消息，3-紧急消息';
COMMENT ON COLUMN t_active_message.associated_with_agent IS '是否关联智能体：0-否，1-是';
COMMENT ON COLUMN t_active_message.agent_id IS '关联的智能体ID';
COMMENT ON COLUMN t_active_message.valid_start_date IS '消息有效期开始日期';
COMMENT ON COLUMN t_active_message.valid_end_date IS '消息有效期结束日期';

-- 创建索引
CREATE INDEX idx_active_message_agent ON t_active_message (agent_id) WHERE deleted = 0 AND associated_with_agent = 1;
CREATE INDEX idx_active_message_type ON t_active_message (type) WHERE deleted = 0;
CREATE INDEX idx_active_message_session ON t_active_message (session_id) WHERE deleted = 0 AND session_id IS NOT NULL;
CREATE INDEX idx_active_message_valid_date ON t_active_message (valid_start_date, valid_end_date) WHERE deleted = 0;

-- 互动消息用户关联表
-- 删除已存在的表和序列
DROP TABLE IF EXISTS "public"."t_active_message_user";
DROP SEQUENCE IF EXISTS t_active_message_user_id_seq;

-- 创建序列
CREATE SEQUENCE t_active_message_user_id_seq START 1 CACHE 1;

-- 创建表
CREATE TABLE t_active_message_user
(
    id          BIGINT PRIMARY KEY DEFAULT NEXTVAL('t_active_message_user_id_seq'::regclass),
    message_id  BIGINT       NOT NULL,
    username    VARCHAR(255) NOT NULL,
    status      INTEGER            DEFAULT 1, -- 1:未读, 2:已读
    create_time TIMESTAMP          DEFAULT CURRENT_TIMESTAMP,
    deleted     INTEGER            DEFAULT 0,
    CONSTRAINT fk_active_message FOREIGN KEY (message_id) REFERENCES t_active_message (id) ON DELETE CASCADE
);

-- 添加表注释
COMMENT ON TABLE t_active_message_user IS '互动消息用户关联表';
COMMENT ON COLUMN t_active_message_user.id IS '主键ID';
COMMENT ON COLUMN t_active_message_user.message_id IS '关联的消息ID (外键指向 t_active_message.id)';
COMMENT ON COLUMN t_active_message_user.username IS '关联的用户名';
COMMENT ON COLUMN t_active_message_user.status IS '消息状态：1-未读，2-已读';
COMMENT ON COLUMN t_active_message_user.create_time IS '记录创建时间';
COMMENT ON COLUMN t_active_message_user.deleted IS '删除标记(0:未删除,1:已删除)';

-- 创建索引
CREATE INDEX idx_active_message_user_username ON t_active_message_user (username) WHERE deleted = 0;
CREATE INDEX idx_active_message_user_message_id ON t_active_message_user (message_id) WHERE deleted = 0;
CREATE INDEX idx_active_message_user_status ON t_active_message_user (status) WHERE deleted = 0;
