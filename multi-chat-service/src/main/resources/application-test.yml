# 测试环境配置 - 压缩任务禁用状态
history:
  compress:
    # 禁用压缩任务以测试监控控制器的兼容性
    enabled: false

    # 保留其他配置用于测试
    schedule-interval: 30000
    compress-threshold: 50
    max-sessions-per-batch: 5
    concurrent-threads: 2
    ai-request-timeout: 30000

    max-retry-count: 3
    retry-interval: 5000

    # 启用监控指标（即使任务禁用）
    metrics-enabled: true

    # 启用详细日志便于调试
    verbose-logging: true

# 日志配置
logging:
  level:
    com.gw.multi.chat.config: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%logger{50}] - %msg%n"
