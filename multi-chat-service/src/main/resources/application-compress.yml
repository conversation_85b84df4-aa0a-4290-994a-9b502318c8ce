# 历史记录压缩任务配置示例
# 针对复杂提示词和大量内容优化的超时配置
history:
  compress:
    # 是否启用压缩任务
    enabled: false

    # 调度间隔（毫秒）- 改为5分钟，避免频繁执行
    schedule-interval: 1200000

    # 压缩阈值 - 消息数量超过此值才进行压缩
    compress-threshold: 100

    # 单次处理的最大会话数
    max-sessions-per-batch: 10

    # 并发处理线程数
    concurrent-threads: 3

    # AI压缩请求超时时间（毫秒）- 增加到120秒以处理复杂提示词
    ai-request-timeout: 120000

    # 单次压缩处理的最大消息数量 - 超过此数量将进行分批处理
    max-messages-per-batch: 500

    # 分批处理时的重叠消息数量 - 用于保持上下文连贯性
    batch-overlap-size: 20

    # 每个任务的额外超时时间（毫秒）- 增加到30秒以适应更长的处理时间
    per-task-extra-timeout: 30000

    # 最大总超时时间（毫秒）- 增加到10分钟以防止复杂任务超时
    max-total-timeout: 600000

    # 最大重试次数
    max-retry-count: 0

    # 重试间隔（毫秒）
    retry-interval: 5000

    # 是否启用监控指标
    metrics-enabled: true

    # 压缩记录保留天数
    retention-days: 90

    # 批量查询大小
    batch-query-size: 1000

    # 是否启用缓存
    cache-enabled: true

    # 缓存过期时间（秒）
    cache-expire-seconds: 300

    # 是否启用详细日志
    verbose-logging: false

    # 内存监控配置（参照chat-service）
    # 内存阈值百分比
    memory-threshold-percent: 80.0
    # 强制GC阈值百分比
    force-gc-threshold-percent: 85.0
    # 内存检查间隔
    memory-check-interval: 10

# Spring Boot Actuator 监控配置（可选）
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: when-authorized
    metrics:
      enabled: true

# 日志配置
logging:
  level:
    com.gw.multi.chat.task.HandleHistoryCompressTask: INFO
    com.gw.multi.chat.util.DistributedLockUtil: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%logger{50}] - %msg%n"
