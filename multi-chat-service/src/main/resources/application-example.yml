# 多智能体聊天服务配置示例
multi-chat:
  agent:
    # 角色名称相似度匹配最小阈值
    # 当精确匹配失败时，使用相似度匹配的最小阈值
    # 范围：0.0-1.0，数值越高匹配越严格
    # 0.3：宽松匹配，容易匹配到相似名称
    # 0.5：中等匹配，平衡准确性和容错性（推荐）
    # 0.7：严格匹配，只匹配高度相似的名称
    name-similarity-threshold: 0.5
    
    # 其他配置...
    max-conversation-rounds: 2
    enable-auto-rounds: true
    round-trigger-threshold: 0.7
    max-mentions-per-round: 2
  
  websocket:
    max-concurrent-agent-responses: 3
