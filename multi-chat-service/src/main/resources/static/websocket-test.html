<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    <title>WebSocket连接测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
        }

        .log {
            border: 1px solid #ccc;
            height: 400px;
            overflow-y: scroll;
            padding: 10px;
            margin: 10px 0;
        }

        .controls {
            margin: 10px 0;
        }

        button {
            margin: 5px;
            padding: 10px 15px;
        }

        input {
            margin: 5px;
            padding: 5px;
        }

        .status {
            padding: 5px;
            margin: 5px 0;
            border-radius: 3px;
        }

        .connected {
            background-color: #d4edda;
            color: #155724;
        }

        .disconnected {
            background-color: #f8d7da;
            color: #721c24;
        }

        .connecting {
            background-color: #fff3cd;
            color: #856404;
        }
    </style>
</head>
<body>
<div class="container">
    <h1>多智能体群聊WebSocket测试</h1>

    <div class="controls">
        <label>用户名: <input id="username" type="text" value="testuser"/></label>
        <label>故事ID: <input id="storyId" type="text" value="1"/></label>
        <label>场景ID: <input id="sceneId" type="text" value="1"/></label>
    </div>

    <div class="controls">
        <button onclick="connect()">连接</button>
        <button onclick="disconnect()">断开</button>
        <button onclick="clearLog()">清空日志</button>
    </div>

    <div class="status disconnected" id="status">未连接</div>

    <div class="controls">
        <label>消息: <input id="message" type="text"
                            value='{"command":"SEND_MESSAGE","data":{"content":"Hello"},"sequence":1}'/></label>
        <button onclick="sendMessage()">发送消息</button>
    </div>

    <div class="controls">
        <button onclick="sendHeartbeat()">发送心跳</button>
        <button onclick="joinChat()">加入群聊</button>
        <button onclick="leaveChat()">离开群聊</button>
    </div>

    <div class="log" id="log"></div>
</div>

<script>
    let ws = null;
    let messageSequence = 1;

    function connect() {
        const username = document.getElementById('username').value;
        const storyId = document.getElementById('storyId').value;
        const sceneId = document.getElementById('sceneId').value;

        if (!username || !storyId) {
            alert('请输入用户名和故事ID');
            return;
        }

        const wsUrl = `ws://localhost:8084/ws/multi/chat/message?username=${username}&storyId=${storyId}&sceneId=${sceneId}`;
        updateStatus('连接中...', 'connecting');
        log('尝试连接: ' + wsUrl);

        ws = new WebSocket(wsUrl);

        ws.onopen = function (event) {
            updateStatus('已连接', 'connected');
            log('WebSocket连接已建立');
        };

        ws.onmessage = function (event) {
            log('收到消息: ' + event.data);
            try {
                const data = JSON.parse(event.data);
                log('解析后的消息: ' + JSON.stringify(data, null, 2));
            } catch (e) {
                log('消息解析失败: ' + e.message);
            }
        };

        ws.onclose = function (event) {
            updateStatus('连接已关闭', 'disconnected');
            log('WebSocket连接已关闭. Code: ' + event.code + ', Reason: ' + event.reason);
        };

        ws.onerror = function (error) {
            updateStatus('连接错误', 'disconnected');
            log('WebSocket错误: ' + error);
            console.error('WebSocket error:', error);
        };
    }

    function disconnect() {
        if (ws) {
            ws.close();
            ws = null;
        }
    }

    function sendMessage() {
        if (!ws || ws.readyState !== WebSocket.OPEN) {
            alert('WebSocket未连接');
            return;
        }

        const messageText = document.getElementById('message').value;
        try {
            const message = JSON.parse(messageText);
            message.sequence = messageSequence++;
            const messageStr = JSON.stringify(message);
            ws.send(messageStr);
            log('发送消息: ' + messageStr);
        } catch (e) {
            alert('消息格式错误: ' + e.message);
        }
    }

    function sendHeartbeat() {
        if (!ws || ws.readyState !== WebSocket.OPEN) {
            alert('WebSocket未连接');
            return;
        }

        const heartbeat = {
            command: "HEARTBEAT",
            sequence: messageSequence++,
            data: {}
        };

        const messageStr = JSON.stringify(heartbeat);
        ws.send(messageStr);
        log('发送心跳: ' + messageStr);
    }

    function joinChat() {
        if (!ws || ws.readyState !== WebSocket.OPEN) {
            alert('WebSocket未连接');
            return;
        }

        const joinMessage = {
            command: "JOIN_CHAT",
            sequence: messageSequence++,
            data: {
                action: "join"
            }
        };

        const messageStr = JSON.stringify(joinMessage);
        ws.send(messageStr);
        log('发送加入群聊: ' + messageStr);
    }

    function leaveChat() {
        if (!ws || ws.readyState !== WebSocket.OPEN) {
            alert('WebSocket未连接');
            return;
        }

        const leaveMessage = {
            command: "LEAVE_CHAT",
            sequence: messageSequence++,
            data: {
                action: "leave"
            }
        };

        const messageStr = JSON.stringify(leaveMessage);
        ws.send(messageStr);
        log('发送离开群聊: ' + messageStr);
    }

    function updateStatus(text, className) {
        const status = document.getElementById('status');
        status.textContent = text;
        status.className = 'status ' + className;
    }

    function log(message) {
        const logDiv = document.getElementById('log');
        const timestamp = new Date().toLocaleTimeString();
        logDiv.innerHTML += `<div>[${timestamp}] ${message}</div>`;
        logDiv.scrollTop = logDiv.scrollHeight;
    }

    function clearLog() {
        document.getElementById('log').innerHTML = '';
    }

    // 页面关闭时断开连接
    window.addEventListener('beforeunload', function () {
        if (ws) {
            ws.close();
        }
    });
</script>
</body>
</html> 