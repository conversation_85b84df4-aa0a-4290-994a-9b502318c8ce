<!DOCTYPE html>
<html>
<head>
    <title>多智能体群聊测试 - 标准格式</title>
    <meta charset="UTF-8">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }

        .container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .form-group {
            margin-bottom: 15px;
        }

        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }

        input, button, select, textarea {
            padding: 8px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }

        button {
            background: #007bff;
            color: white;
            border: none;
            cursor: pointer;
        }

        button:hover {
            background: #0056b3;
        }

        button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }

        #messages {
            border: 1px solid #ccc;
            height: 400px;
            overflow-y: auto;
            padding: 10px;
            margin-bottom: 10px;
            background: #fafafa;
            font-family: monospace;
            font-size: 12px;
        }

        .message {
            margin-bottom: 10px;
            padding: 8px;
            border-radius: 4px;
        }

        .request {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
        }

        .response {
            background: #f3e5f5;
            border-left: 4px solid #9c27b0;
        }

        .error {
            background: #ffebee;
            border-left: 4px solid #f44336;
        }

        .controls {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .message-format {
            background: #e8f5e8;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }

        .format-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: #2e7d32;
        }

        pre {
            background: #f8f8f8;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
<div class="container">
    <h1>多智能体群聊测试 - 标准格式</h1>

    <div class="message-format">
        <div class="format-title">📋 消息格式说明</div>
        <p><strong>请求格式 (WebSocketPayload):</strong></p>
        <pre>{
  "command": "SEND_MESSAGE",
  "sequence": 1,
  "data": {
    "content": "消息内容",
    "replyToMessageId": "回复的消息ID(可选)"
  }
}</pre>

        <p><strong>响应格式 (MultiChatWsMsgRspDTO):</strong></p>
        <pre>{
  "cmd": "MESSAGE_ACK",
  "code": 200,
  "error": "",
  "msg": {
    "messageId": "msg_xxx",
    "timestamp": 1703123456789
  }
}</pre>
    </div>

    <div class="controls">
        <div>
            <div class="form-group">
                <label>用户名:</label>
                <input id="username" type="text" value="testuser">
            </div>

            <div class="form-group">
                <label>故事ID:</label>
                <input id="storyId" type="number" value="1">
            </div>

            <div class="form-group">
                <label>场景ID:</label>
                <input id="sceneId" type="number" value="1">
            </div>

            <button id="connectBtn" onclick="connect()">连接</button>
            <button disabled id="disconnectBtn" onclick="disconnect()">断开</button>
            <span id="status">未连接</span>
        </div>

        <div>
            <div class="form-group">
                <label>命令类型:</label>
                <select id="commandType">
                    <option value="SEND_MESSAGE">发送消息</option>
                    <option value="JOIN_CHAT">加入群聊</option>
                    <option value="LEAVE_CHAT">离开群聊</option>
                    <option value="CHANGE_SCENE">切换场景</option>
                    <option value="HEARTBEAT">心跳</option>
                </select>
            </div>

            <div class="form-group">
                <label>消息内容:</label>
                <textarea id="messageContent" placeholder="输入消息内容..." rows="3" style="width: 100%;"></textarea>
            </div>

            <button onclick="sendMessage()">发送消息</button>
            <button onclick="clearMessages()">清空日志</button>
        </div>
    </div>

    <div id="messages"></div>
</div>

<script>
    let websocket = null;
    let sequenceNumber = 1;

    function connect() {
        const username = document.getElementById('username').value;
        const storyId = document.getElementById('storyId').value;
        const sceneId = document.getElementById('sceneId').value;

        if (!username || !storyId) {
            alert('请填写用户名和故事ID');
            return;
        }

        const wsUrl = `ws://localhost:8084/ws/multi-chat?username=${username}&storyId=${storyId}&sceneId=${sceneId}`;

        try {
            websocket = new WebSocket(wsUrl);

            websocket.onopen = function () {
                updateStatus('已连接', 'green');
                document.getElementById('connectBtn').disabled = true;
                document.getElementById('disconnectBtn').disabled = false;
                addMessage('系统', '连接成功', 'response');
            };

            websocket.onmessage = function (event) {
                try {
                    const response = JSON.parse(event.data);
                    addMessage('响应', JSON.stringify(response, null, 2), 'response');
                } catch (e) {
                    addMessage('响应', event.data, 'response');
                }
            };

            websocket.onclose = function () {
                updateStatus('连接关闭', 'red');
                document.getElementById('connectBtn').disabled = false;
                document.getElementById('disconnectBtn').disabled = true;
                addMessage('系统', '连接已关闭', 'error');
            };

            websocket.onerror = function (error) {
                updateStatus('连接错误', 'red');
                addMessage('系统', '连接错误: ' + error, 'error');
            };

        } catch (error) {
            alert('连接失败: ' + error.message);
        }
    }

    function disconnect() {
        if (websocket) {
            websocket.close();
            websocket = null;
        }
    }

    function sendMessage() {
        if (!websocket || websocket.readyState !== WebSocket.OPEN) {
            alert('请先连接WebSocket');
            return;
        }

        const commandType = document.getElementById('commandType').value;
        const content = document.getElementById('messageContent').value;

        // 构建标准的WebSocketPayload格式消息
        const payload = {
            command: commandType,
            sequence: sequenceNumber++,
            data: buildMessageData(commandType, content)
        };

        try {
            const messageJson = JSON.stringify(payload);
            websocket.send(messageJson);
            addMessage('请求', messageJson, 'request');
        } catch (error) {
            addMessage('错误', '发送失败: ' + error.message, 'error');
        }
    }

    function buildMessageData(commandType, content) {
        const data = {};

        switch (commandType) {
            case 'SEND_MESSAGE':
                data.content = content || '这是一条测试消息';
                break;
            case 'CHANGE_SCENE':
                data.sceneId = parseInt(document.getElementById('sceneId').value) + 1;
                data.reason = content || '用户主动切换场景';
                break;
            case 'JOIN_CHAT':
            case 'LEAVE_CHAT':
                data.reason = content || '用户操作';
                break;
            case 'HEARTBEAT':
                // 心跳消息不需要额外数据
                break;
            default:
                data.content = content;
        }

        return data;
    }

    function addMessage(type, content, cssClass) {
        const messages = document.getElementById('messages');
        const messageDiv = document.createElement('div');
        messageDiv.className = 'message ' + cssClass;

        const timestamp = new Date().toLocaleTimeString();
        messageDiv.innerHTML = `
            <strong>[${timestamp}] ${type}:</strong><br>
            <pre>${content}</pre>
        `;

        messages.appendChild(messageDiv);
        messages.scrollTop = messages.scrollHeight;
    }

    function clearMessages() {
        document.getElementById('messages').innerHTML = '';
    }

    function updateStatus(status, color) {
        const statusElement = document.getElementById('status');
        statusElement.textContent = status;
        statusElement.style.color = color;
    }

    // 快捷键支持
    document.addEventListener('keydown', function (event) {
        if (event.ctrlKey && event.key === 'Enter') {
            sendMessage();
        }
    });

    // 页面加载完成后的初始化
    window.onload = function () {
        addMessage('系统', '页面加载完成，请连接WebSocket开始测试', 'response');
        document.getElementById('messageContent').value = '你好，智能体们！这是一条测试消息。';
    };

    // 页面关闭时断开连接
    window.onbeforeunload = function () {
        if (websocket) {
            websocket.close();
        }
    };
</script>
</body>
</html> 