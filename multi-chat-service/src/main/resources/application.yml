server:
  port: 9086

spring:
  application:
    name: multi-chat-service

  # 激活配置文件
  profiles:
    include:
      - compress
      - multi-chat

  # 明确配置使用 WebFlux
  main:
    web-application-type: reactive

  # 排除不需要的自动配置
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration
      - org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration
      - org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration
      # 明确排除传统 web 相关配置
      - org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration
      - org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration
      - org.springframework.boot.autoconfigure.web.servlet.ServletWebServerFactoryAutoConfiguration
      - org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration

    # 数据库配置
  data:
    mongodb:
      uri: mongodb://localhost:27017/multi-chat
      database: multi-chat
      auto-index-creation: true
      timezone: Asia/Shanghai
    redis:
      host: localhost
      port: 6379
      password:
      database: 0
      timeout: 5000ms
      lettuce:
        pool:
          max-active: 100
          max-idle: 20
          min-idle: 5
          max-wait: 2000ms

  # Nacos配置
  cloud:
    nacos:
      discovery:
        server-addr: localhost:8848
    openfeign:
      client:
        config:
          default:
            connectTimeout: 5000
            readTimeout: 10000
      compression:
        request:
          enabled: true
        response:
          enabled: true

# 火山方舟配置
volcanoark:
  api-key: ${VOLCANOARK_API_KEY:b75e2ee2-f24b-40ef-8c04-9df7f797c385} # 请设置环境变量或在此处填入您的API Key
  base-url: ${VOLCANOARK_BASE_URL:https://ark.cn-beijing.volces.com}
  default-model-id: ${VOLCANOARK_MODEL_ID:ep-m-20250608214046-pv8dg}
  connect-timeout: 30000
  read-timeout: 120000
  retry-times: 2
  max-connections: 10
  keep-alive-duration: 60

# 日志配置
logging:
  config: classpath:log4j2.xml
  level:
    com.gw.multi.chat: ${LOG_LEVEL:debug}
    org.springframework.data.mongodb: ${MONGO_LOG_LEVEL:info}
    org.springframework.web.socket: ${WEBSOCKET_LOG_LEVEL:debug}
    org.springframework.web: ${WEB_LOG_LEVEL:debug}
    org.springframework.beans: ${BEAN_LOG_LEVEL:debug}

# 管理端点
management:
  endpoints:
    web:
      exposure:
        include: health,info
  endpoint:
    health:
      show-details: always

# 缓存配置
cache:
  prefix: lingxi
  configs:
    - name: agentBase
      expireAfterWrite: 1h
    - name: agentTag
      expireAfterWrite: 720h
    - name: agentType
      expireAfterWrite: 720h
    - name: agent
      expireAfterWrite: 720h
    - name: coze
      expireAfterWrite: 720h

# SpringDoc 配置
springdoc:
  api-docs:
    enabled: true
    path: /api/v1/multi/chat/v3/api-docs
  swagger-ui:
    enabled: true
    path: /api/v1/multi/chat/swagger-ui/index.html
    config-url: /api/v1/multi/chat/v3/api-docs/swagger-config
    urls:
      - url: /api/v1/multi/chat/v3/api-docs
        name: 故事聊天服务API
  paths-to-match: /api/v1/multi/chat/**
  cache:
    disabled: true

# 多智能体配置已完全移至 application-multi-chat.yml
wx:
  miniapp:
    appid: ${WX_MINIAPP_APPID:wxe96c29e240e54ff0}
    secret: ${WX_MINIAPP_SECRET:7b9a54bc3bf8070c34be5cb184602561}
    access-token-url: https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=APPID&secret=APPSECRET
    msg-sec-check-url: https://api.weixin.qq.com/wxa/msg_sec_check
    default-openid: test-openid # 测试用的OpenID，实际使用时应该传入真实用户的OpenID