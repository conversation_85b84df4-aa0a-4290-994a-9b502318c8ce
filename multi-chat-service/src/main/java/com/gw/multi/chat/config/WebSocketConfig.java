package com.gw.multi.chat.config;

import com.gw.multi.chat.websocket.RefactoredMultiChatWebSocketHandler;
import io.netty.channel.EventLoopGroup;
import io.netty.channel.nio.NioEventLoopGroup;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.reactive.HandlerMapping;
import org.springframework.web.reactive.handler.SimpleUrlHandlerMapping;
import org.springframework.web.reactive.socket.WebSocketMessage;
import org.springframework.web.reactive.socket.server.RequestUpgradeStrategy;
import org.springframework.web.reactive.socket.server.support.WebSocketHandlerAdapter;
import org.springframework.web.reactive.socket.server.upgrade.TomcatRequestUpgradeStrategy;
import reactor.core.publisher.Mono;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 多智能体WebSocket配置 - 响应式模式
 * 参照chat-service的WSConfig设计，优化高并发场景下的性能
 */
@Configuration
@Log4j2
@org.springframework.context.annotation.PropertySource(value = "classpath:websocket.properties", ignoreResourceNotFound = true)
public class WebSocketConfig {

    private static final org.slf4j.Logger log = org.slf4j.LoggerFactory.getLogger(WebSocketConfig.class);

    private final RefactoredMultiChatWebSocketHandler multiChatWebSocketHandler;

    // 配置WebSocket连接池大小
    @Value("${multi-chat.websocket.max-connections:5000}")
    private int maxConnections;

    // 配置WebSocket消息处理线程池大小
    @Value("${multi-chat.websocket.thread-pool.core-size:20}")
    private int threadPoolCoreSize;

    @Value("${multi-chat.websocket.thread-pool.max-size:100}")
    private int threadPoolMaxSize;

    @Value("${multi-chat.websocket.thread-pool.queue-capacity:2000}")
    private int threadPoolQueueCapacity;

    @Value("${multi-chat.websocket.thread-pool.keep-alive-seconds:60}")
    private int threadPoolKeepAliveSeconds;

    // 配置WebSocket会话超时时间（毫秒）
    @Value("${multi-chat.websocket.idle-timeout:3600000}")
    private long idleTimeout;

    public WebSocketConfig(RefactoredMultiChatWebSocketHandler multiChatWebSocketHandler) {
        this.multiChatWebSocketHandler = multiChatWebSocketHandler;
        if (log.isInfoEnabled()) {
            log.info("多智能体WebSocketConfig初始化完成");
        }
    }

    /**
     * 创建WebSocket消息处理线程池
     * 用于异步处理WebSocket消息，提高并发处理能力
     */
    @Bean
    public ThreadPoolExecutor multiChatWebSocketMessageThreadPool() {
        ThreadPoolExecutor executor = new ThreadPoolExecutor(
                threadPoolCoreSize,
                threadPoolMaxSize,
                threadPoolKeepAliveSeconds,
                TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(threadPoolQueueCapacity),
                r -> {
                    Thread t = new Thread(r, "multi-chat-websocket-message-processor-" + r.hashCode());
                    t.setDaemon(true);
                    return t;
                },
                new ThreadPoolExecutor.CallerRunsPolicy() // 当队列满时，由调用线程执行任务
        );

        log.info("多智能体WebSocket消息处理线程池已创建，核心线程数: {}, 最大线程数: {}, 队列容量: {}",
                threadPoolCoreSize, threadPoolMaxSize, threadPoolQueueCapacity);

        return executor;
    }

    /**
     * 创建Netty事件循环组，用于高效处理WebSocket连接
     */
    @Bean
    public EventLoopGroup multiChatWebSocketEventLoopGroup() {
        // 创建一个优化的事件循环组，线程数为处理器核心数的2倍
        int threadCount = Runtime.getRuntime().availableProcessors() * 2;
        EventLoopGroup eventLoopGroup = new NioEventLoopGroup(threadCount);
        log.info("多智能体WebSocket事件循环组已创建，线程数: {}", threadCount);
        return eventLoopGroup;
    }

    /**
     * 使用Tomcat的WebSocket升级策略，与Servlet兼容
     * 优化配置以支持高并发
     */
    @Bean
    public RequestUpgradeStrategy multiChatRequestUpgradeStrategy() {
        TomcatRequestUpgradeStrategy strategy = new TomcatRequestUpgradeStrategy();
        // 设置最大空闲超时时间，增加到60分钟，防止过早断开连接
        strategy.setMaxSessionIdleTimeout(idleTimeout);
        log.info("多智能体WebSocket会话空闲超时时间设置为: {}毫秒", idleTimeout);
        return strategy;
    }

    /**
     * 添加Reactive WebSocket支持
     * 使用共享的ReactiveWebSocketHandler实例以减少对象创建
     */
    @Bean
    public HandlerMapping multiChatWebSocketHandlerMapping() {
        // 创建反应式WebSocket处理器映射
        Map<String, Object> map = new HashMap<>();

        // 创建共享的处理器实例，使用配置的线程池
        ReactiveMultiChatWebSocketHandler sharedHandler = new ReactiveMultiChatWebSocketHandler(
                multiChatWebSocketHandler,
                multiChatWebSocketMessageThreadPool());

        // 配置多智能体WebSocket路径映射，确保与网关路由一致
        map.put("/api/v1/ws/multi/chat/message", sharedHandler);
        map.put("/ws/multi/chat/message", sharedHandler);
        map.put("/multi/chat/message", sharedHandler);
        map.put("/chat/multi/message", sharedHandler);

        SimpleUrlHandlerMapping handlerMapping = new SimpleUrlHandlerMapping();
        handlerMapping.setOrder(1);
        handlerMapping.setUrlMap(map);

        log.info("已注册多智能体Reactive WebSocket处理器映射，支持的路径数量: {}", map.size());
        return handlerMapping;
    }

    /**
     * 添加WebSocketHandlerAdapter
     * 配置优化的WebSocketService
     */
    @Bean
    public WebSocketHandlerAdapter multiChatHandlerAdapter() {
        return new WebSocketHandlerAdapter(multiChatReactiveWebSocketService());
    }

    /**
     * 创建WebSocketService
     * 使用优化的配置
     */
    @Bean
    public org.springframework.web.reactive.socket.server.WebSocketService multiChatReactiveWebSocketService() {

        return new org.springframework.web.reactive.socket.server.support.HandshakeWebSocketService(
                multiChatRequestUpgradeStrategy());
    }

    /**
     * 反应式多智能体WebSocket处理器实现
     * 优化版本：使用线程池处理消息，提高并发性能
     */
    private static class ReactiveMultiChatWebSocketHandler
            implements org.springframework.web.reactive.socket.WebSocketHandler {
        private static final org.slf4j.Logger log = org.slf4j.LoggerFactory.getLogger(ReactiveMultiChatWebSocketHandler.class);
        private final RefactoredMultiChatWebSocketHandler delegateHandler;
        private final ThreadPoolExecutor messageThreadPool;

        public ReactiveMultiChatWebSocketHandler(RefactoredMultiChatWebSocketHandler delegateHandler, ThreadPoolExecutor threadPool) {
            this.delegateHandler = delegateHandler;
            this.messageThreadPool = threadPool;
        }

        @Override
        public Mono<Void> handle(org.springframework.web.reactive.socket.WebSocketSession session) {
            if (log.isInfoEnabled()) {
                log.info("多智能体Reactive WebSocket连接已建立: {}", session.getId());
            }

            // 获取用户信息 - 参照chat-service的方式从headers获取
            final String username = session.getHandshakeInfo().getHeaders().getFirst("X-User-Name") != null
                    ? session.getHandshakeInfo().getHeaders().getFirst("X-User-Name")
                    : "anonymous";
            final String storyId = session.getHandshakeInfo().getHeaders().getFirst("story-id") != null
                    ? session.getHandshakeInfo().getHeaders().getFirst("story-id")
                    : "";
            final String sceneId = session.getHandshakeInfo().getHeaders().getFirst("scene-id") != null
                    ? session.getHandshakeInfo().getHeaders().getFirst("scene-id")
                    : "";
            // 打印所有headers内容
//            session.getHandshakeInfo().getHeaders().forEach((key, values) ->
//                    log.info("Header - {}: {}", key, String.join(",", values)));

            // 注册会话 - storyId和sceneId将在消息中携带
            delegateHandler.registerReactiveSession(username, session,storyId, sceneId);

            // 处理输入消息 - 使用线程池异步处理消息
            Mono<Void> input = session.receive()
                    .map(WebSocketMessage::getPayloadAsText)
                    .doOnNext(message -> {
                        if (log.isDebugEnabled()) {
                            log.debug("多智能体Reactive WebSocket收到消息: {}", message);
                        }

                        // 使用线程池异步处理消息，避免阻塞WebSocket事件循环
                        messageThreadPool.execute(() -> {
                            try {
                                delegateHandler.handleReactiveMessage(session, message);
                            } catch (Exception e) {
                                log.error("多智能体WebSocket session {} 处理消息时出错", session.getId(), e);
                            }
                        });
                    })
                    .doOnError(error -> {
                        log.error("多智能体WebSocket session {} 发生错误", session.getId(), error);
                    })
                    .onErrorResume(error -> {
                        log.warn("多智能体WebSocket session {} 因错误关闭连接", session.getId());
                        return Mono.empty();
                    })
                    .then();

            // 监听会话关闭事件
            Mono<Void> close = session.closeStatus()
                    .doOnNext(status -> {
                        log.info("多智能体Reactive WebSocket连接已关闭，状态: {}", status);
                        delegateHandler.unregisterReactiveSession(username);
                    })
                    .then();

            // 同时订阅两个流，确保消息处理立即开始，而不是等到连接关闭
            return Mono.zip(input, close).then();
        }
    }
} 