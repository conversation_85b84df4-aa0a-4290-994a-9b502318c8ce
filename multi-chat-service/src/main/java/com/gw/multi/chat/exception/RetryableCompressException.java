package com.gw.multi.chat.exception;

/**
 * 可重试的压缩异常
 * 用于网络超时、临时服务不可用等可恢复的错误
 */
public class RetryableCompressException extends CompressTaskException {

    public RetryableCompressException(String message) {
        super(message);
    }

    public RetryableCompressException(String message, Throwable cause) {
        super(message, cause);
    }

    @Override
    public boolean isRetryable() {
        return true;
    }
}
