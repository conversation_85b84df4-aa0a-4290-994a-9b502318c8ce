package com.gw.multi.chat.task;

import com.gw.common.agent.constant.AgentCommonCacheConstant;
import com.gw.common.agent.constant.StoryConstant;
import com.gw.common.agent.service.AgentProxyService;
import com.gw.common.agent.service.AgentStoryProxyService;
import com.gw.common.agent.vo.AgentBaseVO;
import com.gw.common.agent.vo.AgentStoryBaseVO;
import com.gw.common.agent.vo.AgentStorySceneBaseVO;
import com.gw.common.agent.vo.SceneAgentRelationBaseVO;
import com.gw.common.membership.constant.MemberCacheConstant;
import com.gw.common.membership.service.MembershipProxyService;
import com.gw.common.membership.vo.UserMembershipVO;
import com.gw.multi.chat.config.CacheProperties;
import com.gw.multi.chat.config.HistoryCompressConfig;
import com.gw.multi.chat.entity.MultiChatMsg;
import com.gw.multi.chat.entity.MultiChatMsgCompress;
import com.gw.multi.chat.entity.MultiChatSession;
import com.gw.multi.chat.exception.NonRetryableCompressException;
import com.gw.multi.chat.exception.RetryableCompressException;
import com.gw.multi.chat.service.MultiChatMsgCompressService;
import com.gw.multi.chat.service.MultiChatMsgService;
import com.gw.multi.chat.service.MultiChatSessionService;
import com.gw.multi.chat.service.VolcanoArkService;
import com.gw.multi.chat.util.ChatUtils;
import com.gw.multi.chat.util.MemoryMonitor;
import com.gw.multi.chat.vo.AIResponseVO;
import com.gw.multi.chat.vo.ChatContextVO;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

import static com.gw.multi.chat.constant.MultiChatConstant.CHAT_USER_ROLE;

/**
 * 历史记录压缩任务 - 优化版
 * <p>
 * 优化特性：
 * - 支持高并发处理
 * - 完全可配置
 * - 内存监控和优化
 * - 监控指标收集
 * - 优雅错误处理
 * - 批量处理优化
 * - 参照chat-service的CompressTask流程优化
 */
@Component
@RequiredArgsConstructor
public class HandleHistoryCompressTask {

    private static final Logger log = LogManager.getLogger(HandleHistoryCompressTask.class);

    private final MultiChatSessionService chatSessionService;
    private final MultiChatMsgService chatMessageService;
    private final AgentStoryProxyService storyProxyService;
    private final CacheProperties cacheProperties;
    private final MultiChatMsgCompressService compressService;
    private final VolcanoArkService volcanoArkService;
    private final HistoryCompressConfig compressConfig;
    private final MembershipProxyService membershipProxyService;
    private final AgentProxyService agentProxyService;
    // 线程安全控制和性能监控（参照chat-service）
    private final AtomicBoolean isRunning = new AtomicBoolean(false);
    private final AtomicLong totalProcessed = new AtomicLong(0);
    private final AtomicLong totalSkipped = new AtomicLong(0);
    private final AtomicLong totalErrors = new AtomicLong(0);
    private final AtomicLong totalMemoryErrors = new AtomicLong(0);
    private final AtomicInteger activeThreads = new AtomicInteger(0);

    // 原有监控指标保留
    private final AtomicLong processedSessionsTotal = new AtomicLong(0);
    private final AtomicLong compressedScenesTotal = new AtomicLong(0);
    private final AtomicLong failedTasksTotal = new AtomicLong(0);
    private final AtomicInteger currentRunningTasks = new AtomicInteger(0);
    private final AtomicLong retryableFailuresTotal = new AtomicLong(0);
    private final AtomicLong nonRetryableFailuresTotal = new AtomicLong(0);
    private final AtomicLong batchedMessagesTotal = new AtomicLong(0);
    private final AtomicLong splitBatchesTotal = new AtomicLong(0);

    // 线程池管理（参照chat-service）
    private ScheduledExecutorService scheduledExecutor;
    private ExecutorService compressionExecutor;
    private CompletionService<BatchResult> completionService;

    /**
     * 📊 历史记录切割配置
     * 单次压缩处理的最大消息数量，超过此数量将进行分批处理
     * 默认值：200条
     */
    @Value("${history.compress.max-messages-per-batch:400}")
    private int maxMessagesPerBatch;

    /**
     * 📊 历史记录切割重叠配置
     * 分批处理时的重叠消息数量，用于保持上下文连贯性
     * 默认值：20条
     */
    @Value("${history.compress.batch-overlap-size:20}")
    private int batchOverlapSize;

    /**
     * 初始化压缩任务（参照chat-service的init方法）
     */
    @PostConstruct
    public void init() {
        // 验证配置
        validateConfiguration();

        if (!compressConfig.isEnabled()) {
            log.info("历史记录压缩任务已禁用，跳过初始化");
            return;
        }

        log.info("初始化历史记录压缩任务 - 间隔: {} ms, 线程池大小: {}, 超时时间: {}ms",
                compressConfig.getScheduleInterval(), compressConfig.getConcurrentThreads(),
                compressConfig.getAiRequestTimeout());

        log.info("配置摘要: 压缩阈值={}, 批处理大小={}, 单批最大消息数={}, 重叠大小={}",
                compressConfig.getCompressThreshold(), compressConfig.getMaxSessionsPerBatch(),
                maxMessagesPerBatch, batchOverlapSize);

        // 创建调度线程池（单线程，用于定时任务）
        scheduledExecutor = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread thread = new Thread(r, "multi-chat-compress-scheduler-thread");
            thread.setDaemon(true);
            thread.setUncaughtExceptionHandler((t, e) ->
                log.error("压缩调度线程发生未捕获异常", e));
            return thread;
        });

        // 创建压缩处理线程池（多线程，用于并行处理）
        compressionExecutor = Executors.newFixedThreadPool(compressConfig.getConcurrentThreads(), r -> {
            Thread thread = new Thread(r, "multi-chat-compress-worker-" + activeThreads.incrementAndGet());
            thread.setDaemon(true);
            thread.setUncaughtExceptionHandler((t, e) -> {
                log.error("压缩工作线程 {} 发生未捕获异常", t.getName(), e);
                activeThreads.decrementAndGet();
            });
            return thread;
        });

        // 创建完成服务，用于管理并发任务
        completionService = new ExecutorCompletionService<>(compressionExecutor);

        // 延迟启动，避免影响应用启动性能
        scheduledExecutor.schedule(this::startPeriodicCompression, 2, TimeUnit.MINUTES);

        log.info("历史记录压缩任务初始化完成");
    }

    /**
     * 验证配置参数
     */
    private void validateConfiguration() {
        if (compressConfig.getAiRequestTimeout() <= 0) {
            log.warn("AI请求超时时间配置无效: {}ms，使用默认值120000ms",
                    compressConfig.getAiRequestTimeout());
        }

        if (compressConfig.getMaxSessionsPerBatch() <= 0) {
            log.warn("批处理会话数配置无效: {}，使用默认值10",
                    compressConfig.getMaxSessionsPerBatch());
        }

        if (maxMessagesPerBatch <= 0) {
            log.warn("单批最大消息数配置无效: {}，使用默认值200", maxMessagesPerBatch);
            maxMessagesPerBatch = 200;
        }

        if (batchOverlapSize < 0 || batchOverlapSize >= maxMessagesPerBatch) {
            log.warn("批次重叠大小配置无效: {}，使用默认值20", batchOverlapSize);
            batchOverlapSize = 20;
        }

        // 验证超时配置
        if (compressConfig.getPerTaskExtraTimeout() <= 0) {
            log.warn("每任务额外超时时间配置无效: {}ms，使用默认值30000ms",
                    compressConfig.getPerTaskExtraTimeout());
        }

        if (compressConfig.getMaxTotalTimeout() <= compressConfig.getAiRequestTimeout()) {
            log.warn("最大总超时时间配置过小: {}ms，应大于基础超时时间: {}ms",
                    compressConfig.getMaxTotalTimeout(), compressConfig.getAiRequestTimeout());
        }
    }

    /**
     * 启动定期压缩任务（参照chat-service）
     */
    private void startPeriodicCompression() {
        log.info("启动定期历史记录压缩任务，间隔: {} ms", compressConfig.getScheduleInterval());
        scheduledExecutor.scheduleWithFixedDelay(
            this::executeCompressTaskSafely,
            0,
            compressConfig.getScheduleInterval(),
            TimeUnit.MILLISECONDS
        );
    }

    /**
     * 优雅关闭线程池（参照chat-service）
     */
    @PreDestroy
    public void shutdown() {
        log.info("开始关闭历史记录压缩任务线程池");

        if (scheduledExecutor != null) {
            scheduledExecutor.shutdown();
            try {
                if (!scheduledExecutor.awaitTermination(30, TimeUnit.SECONDS)) {
                    log.warn("调度线程池未能在30秒内关闭，强制关闭");
                    scheduledExecutor.shutdownNow();
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                scheduledExecutor.shutdownNow();
            }
        }

        if (compressionExecutor != null) {
            compressionExecutor.shutdown();
            try {
                if (!compressionExecutor.awaitTermination(60, TimeUnit.SECONDS)) {
                    log.warn("压缩线程池未能在60秒内关闭，强制关闭");
                    compressionExecutor.shutdownNow();
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                compressionExecutor.shutdownNow();
            }
        }

        log.info("历史记录压缩任务线程池关闭完成");
    }

    /**
     * 安全执行压缩任务，包含异常处理和性能监控（参照chat-service）
     */
    private void executeCompressTaskSafely() {
        if (!isRunning.compareAndSet(false, true)) {
            log.warn("历史记录压缩任务正在运行中，跳过本次执行");
            return;
        }

        long startTime = System.currentTimeMillis();
        try {
            log.info("开始执行历史记录压缩任务 - 当前统计: 已处理={}, 已跳过={}, 错误={}",
                    totalProcessed.get(), totalSkipped.get(), totalErrors.get());

            executeHistoryCompression();

            long duration = System.currentTimeMillis() - startTime;
            log.info("历史记录压缩任务执行完成，耗时: {}ms", duration);

        } catch (Exception e) {
            totalErrors.incrementAndGet();
            log.error("历史记录压缩任务执行失败", e);
        } finally {
            isRunning.set(false);
        }
    }



    /**
     * 执行历史记录压缩的核心逻辑（参照chat-service优化）
     */
    private void executeHistoryCompression() {
        if (!compressConfig.isEnabled()) {
            log.debug("历史记录压缩任务已禁用，跳过执行");
            return;
        }

        log.info("开始执行历史记录压缩任务，查找所有需要压缩的会话...");
        log.info("配置参数 - 压缩阈值: {}, 批处理大小: {}, 单批最大消息数: {}, 线程池大小: {}",
                compressConfig.getCompressThreshold(), compressConfig.getMaxSessionsPerBatch(),
                maxMessagesPerBatch, compressConfig.getConcurrentThreads());

        int currentBatchProcessed = 0;
        int currentBatchSkipped = 0;
        int currentBatchErrors = 0;
        long startTime = System.currentTimeMillis();

        try {
            // 获取需要压缩的会话列表
            List<MultiChatSession> needCompressSessions = chatSessionService.findNeedCompressSession();

            if (CollectionUtils.isEmpty(needCompressSessions)) {
                log.debug("没有需要压缩的会话");
                return;
            }

            log.info("发现 {} 个需要压缩的会话", needCompressSessions.size());

            // 检查内存使用情况
            checkMemoryBeforeProcessing(needCompressSessions.size());

            // 分批处理会话
            List<List<MultiChatSession>> batches = partitionSessions(needCompressSessions);

            // 使用CompletionService管理并发任务（参照chat-service）
            List<Future<BatchResult>> futures = new ArrayList<>(batches.size());

            try {
                for (int i = 0; i < batches.size(); i++) {
                    final int batchIndex = i;
                    final List<MultiChatSession> batch = batches.get(i);

                    Future<BatchResult> future = completionService.submit(() -> {
                        try {
                            return processBatchOptimized(batchIndex, batch);
                        } catch (OutOfMemoryError e) {
                            log.error("处理批次 {} 时发生内存溢出", batchIndex, e);
                            System.gc();
                            return new BatchResult(0, 0, batch.size()); // 表示处理失败
                        } catch (Exception e) {
                            log.error("处理批次 {} 时发生错误", batchIndex, e);
                            return new BatchResult(0, 0, batch.size()); // 表示处理失败
                        }
                    });
                    futures.add(future);

                    // 每提交5个任务检查一次内存
                    if (i % 5 == 0 && i > 0) {
                        checkMemoryDuringProcessing();
                    }
                }

                // 收集结果
                for (int i = 0; i < futures.size(); i++) {
                    Future<BatchResult> future = futures.get(i);
                    try {
                        // 计算合理的超时时间
                        long baseTimeout = compressConfig.getAiRequestTimeout();
                        long perBatchTimeout = compressConfig.getPerTaskExtraTimeout();
                        long calculatedTimeout = baseTimeout + perBatchTimeout;
                        long totalTimeout = Math.min(calculatedTimeout, compressConfig.getMaxTotalTimeout());

                        BatchResult result = future.get(totalTimeout, TimeUnit.MILLISECONDS);
                        if (result != null) {
                            currentBatchProcessed += result.processed;
                            currentBatchSkipped += result.skipped;
                            currentBatchErrors += result.errors;

                            // 更新全局统计
                            totalProcessed.addAndGet(result.processed);
                            totalSkipped.addAndGet(result.skipped);
                            totalErrors.addAndGet(result.errors);
                        }
                    } catch (TimeoutException e) {
                        log.error("批次处理超时", e);
                        future.cancel(true);
                        currentBatchErrors++;
                        totalErrors.incrementAndGet();
                    } catch (Exception e) {
                        log.error("获取批次处理结果时发生错误", e);
                        currentBatchErrors++;
                        totalErrors.incrementAndGet();
                    }

                    // 清理已完成的Future引用
                    futures.set(i, null);

                    // 每处理10个结果检查一次内存
                    if (i % 10 == 0 && i > 0) {
                        checkMemoryDuringProcessing();
                    }
                }

            } catch (OutOfMemoryError e) {
                log.error("批处理会话时发生内存溢出", e);
                // 取消所有未完成的任务
                for (Future<BatchResult> future : futures) {
                    if (future != null && !future.isDone()) {
                        future.cancel(true);
                    }
                }
                System.gc();
                currentBatchErrors += batches.size();
            } finally {
                // 清理futures引用
                futures.clear();
            }

            long endTime = System.currentTimeMillis();
            log.info("历史记录压缩任务执行完成！本次处理: {} 个会话，跳过: {} 个，错误: {} 个，耗时: {} ms",
                    currentBatchProcessed, currentBatchSkipped, currentBatchErrors, (endTime - startTime));
            log.info("累计统计 - 总处理: {}, 总跳过: {}, 总错误: {}",
                    totalProcessed.get(), totalSkipped.get(), totalErrors.get());

        } catch (Exception e) {
            log.error("执行历史记录压缩任务时发生异常", e);
            totalErrors.incrementAndGet();
        }
    }

    /**
     * 批处理结果类（参照chat-service）
     */
    private record BatchResult(int processed, int skipped, int errors) {
    }

    /**
     * 分批处理会话
     */
    private List<List<MultiChatSession>> partitionSessions(List<MultiChatSession> sessions) {
        int batchSize = compressConfig.getMaxSessionsPerBatch();
        List<List<MultiChatSession>> batches = new ArrayList<>();

        for (int i = 0; i < sessions.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, sessions.size());
            batches.add(sessions.subList(i, endIndex));
        }

        return batches;
    }

    /**
     * 优化的批次处理方法（参照chat-service）
     */
    private BatchResult processBatchOptimized(int batchIndex, List<MultiChatSession> batch) {
        if (batch.isEmpty()) {
            return new BatchResult(0, 0, 0);
        }

        log.info("开始处理批次 {}, 包含 {} 个会话", batchIndex, batch.size());
        long batchStartTime = System.currentTimeMillis();

        // 检查内存使用情况
        checkMemoryBeforeProcessing(batch.size());

        int processed = 0;
        int skipped = 0;
        int errors = 0;

        for (int i = 0; i < batch.size(); i++) {
            MultiChatSession session = batch.get(i);

            // 检查用户会员级别
            String cacheName = cacheProperties.getCacheName(MemberCacheConstant.MEMBERSHIP_BASE_CACHE_KEY + ":" + session.getUsername());
            UserMembershipVO member = membershipProxyService.getMembershipByUsername(cacheName, session.getUsername());
            if (member == null || member.getVipLevel() < 1) {
                if (compressConfig.isVerboseLogging()) {
                    log.debug("用户{}没有会员或会员级别不足，跳过压缩", session.getUsername());
                }
                skipped++;
                continue;
            }

            try {
                boolean result = processSessionOptimized(session);
                if (result) {
                    processed++;
                } else {
                    skipped++;
                }
            } catch (Exception e) {
                log.error("处理会话失败: {}", session.getSessionId(), e);
                errors++;
            }

            // 每处理5个会话检查一次内存
            if (i % 5 == 0 && i > 0) {
                checkMemoryDuringProcessing();
            }
        }

        long batchDuration = System.currentTimeMillis() - batchStartTime;
        log.info("批次 {} 处理完成，耗时: {}ms，处理: {}, 跳过: {}, 错误: {}",
                batchIndex, batchDuration, processed, skipped, errors);

        return new BatchResult(processed, skipped, errors);
    }

    /**
     * 处理前检查内存使用情况（参照chat-service）
     */
    private void checkMemoryBeforeProcessing(int sessionCount) {
        MemoryMonitor.MemoryInfo memoryInfo = MemoryMonitor.getCurrentMemoryInfo();

        if (memoryInfo.getUsagePercent() > 75.0) {
            log.warn("处理{}个会话前内存使用率已达{:.2f}%，建议进行垃圾回收", sessionCount, memoryInfo.getUsagePercent());
            MemoryMonitor.forceGarbageCollection();
        }

        // 估算处理这些会话需要的内存
        long estimatedMemoryNeeded = MemoryMonitor.estimateMemoryForMessages(sessionCount * 100); // 假设每个会话平均100条消息
        if (!MemoryMonitor.hasEnoughMemory(estimatedMemoryNeeded)) {
            log.warn("可用内存可能不足以处理{}个会话，估算需要{}MB",
                    sessionCount, estimatedMemoryNeeded / 1024 / 1024);
        }
    }

    /**
     * 处理过程中检查内存使用情况（参照chat-service）
     */
    private void checkMemoryDuringProcessing() {
        MemoryMonitor.MemoryInfo memoryInfo = MemoryMonitor.getCurrentMemoryInfo();

        if (memoryInfo.getUsagePercent() > 85.0) {
            log.warn("处理过程中内存使用率达到{:.2f}%，强制进行垃圾回收", memoryInfo.getUsagePercent());

            MemoryMonitor.MemoryInfo afterGC = MemoryMonitor.forceGarbageCollection();

            if (afterGC.getUsagePercent() > 92.0) {
                totalMemoryErrors.incrementAndGet();
                log.error("垃圾回收后内存使用率仍然很高: {:.2f}%", afterGC.getUsagePercent());
                throw new RuntimeException("内存使用率过高，停止当前处理");
            }
        }
    }

    /**
     * 优化的会话处理方法（参照chat-service的processCompressionSession）
     */
    private boolean processSessionOptimized(MultiChatSession session) {
        log.debug("开始处理会话: {}", session.getSessionId());

        try {
            // 检查会话是否需要压缩
            if (!needsCompression(session)) {
                log.debug("会话 {} 不需要压缩，跳过处理", session.getSessionId());
                return false;
            }

            String sessionId = session.getSessionId();
            String storyCacheKey = cacheProperties.getCacheName(AgentCommonCacheConstant.AGENT_BASE_STORY_MAP_CACHE_KEY);
            AgentStoryBaseVO story = storyProxyService.getStoryInfo(storyCacheKey, session.getStoryId());

            if (!validateStory(story, sessionId)) {
                return false;
            }

            // 处理会话的所有场景
            Map<Long, MultiChatSession.SceneSession> sceneMap = session.getSceneMap();
            if (sceneMap != null && !sceneMap.isEmpty()) {
                boolean hasProcessed = false;
                for (Map.Entry<Long, MultiChatSession.SceneSession> entry : sceneMap.entrySet()) {
                    try {
                        // 在这里检查单个场景是否需要压缩
                        MultiChatSession.SceneSession sceneSession = entry.getValue();
                        if (!sceneNeedsCompression(sceneSession)) {
                            if (compressConfig.isVerboseLogging()) {
                                log.debug("场景会话 {} 不需要压缩，跳过处理", sceneSession.getSceneSessionId());
                            }
                            continue;
                        }

                        boolean sceneResult = processSceneSessionOptimized(story, session, entry.getKey(), entry.getValue());
                        if (sceneResult) {
                            hasProcessed = true;
                            compressedScenesTotal.incrementAndGet();
                        }
                    } catch (Exception e) {
                        log.error("处理场景会话异常 - 场景ID: {}, 场景会话ID: {}",
                                entry.getKey(), entry.getValue().getSceneSessionId(), e);
                        throw e;
                    }
                }
                return hasProcessed;
            } else {
                log.warn("会话 {} 没有场景数据", sessionId);
                return false;
            }

        } catch (Exception e) {
            log.error("处理会话 {} 时发生错误", session.getSessionId(), e);
            throw e; // 重新抛出异常，让上层处理
        }
    }

    /**
     * 检查会话是否需要压缩（参照chat-service的needsCompression逻辑）
     */
    private boolean needsCompression(MultiChatSession session) {
        if (session.getSceneMap() == null || session.getSceneMap().isEmpty()) {
            return false;
        }

        // 检查是否有任何场景需要压缩
        for (MultiChatSession.SceneSession sceneSession : session.getSceneMap().values()) {
            if (sceneNeedsCompression(sceneSession)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 检查单个场景是否需要压缩
     */
    private boolean sceneNeedsCompression(MultiChatSession.SceneSession sceneSession) {
        Long lstSeqNum = sceneSession.getLstSeqNum();
        Long compressedSeqNum = sceneSession.getCompressedSeqNum();

        // 如果序列号无效，不需要压缩
        if (lstSeqNum == null || lstSeqNum <= 0) {
            return false;
        }

        // 如果从未压缩过，需要压缩
        if (compressedSeqNum == null) {
            compressedSeqNum = 0L;
        }

        // 如果有新消息超过阈值，需要压缩
        return (lstSeqNum - compressedSeqNum) > compressConfig.getCompressThreshold();
    }





    /**
     * 优化的场景会话处理方法（参照chat-service流程）
     */
    private boolean processSceneSessionOptimized(AgentStoryBaseVO story, MultiChatSession session,
                                                Long sceneId, MultiChatSession.SceneSession sceneSession) {
        try {
            String sessionId = session.getSessionId();
            String sceneSessionId = sceneSession.getSceneSessionId();

            if (compressConfig.isVerboseLogging()) {
                log.debug("处理场景压缩 - 会话ID: {}, 场景ID: {}, 场景会话ID: {}",
                        sessionId, sceneId, sceneSessionId);
            }

            String sceneCacheKey = cacheProperties.getCacheName(AgentCommonCacheConstant.AGENT_BASE_STORY_SCENE_KEY);
            AgentStorySceneBaseVO sceneVo = storyProxyService.getStorySceneInfo(sceneId, sceneCacheKey);

            if (sceneVo == null) {
                log.warn("场景 {} 的场景信息不存在或者已经删除", sceneSessionId);
                return false;
            }

            // 执行场景历史记录压缩
            return compressSceneHistoryOptimized(story, session, sceneSession, sceneVo);

        } catch (Exception e) {
            log.error("处理场景会话异常 - 场景ID: {}, 场景会话ID: {}",
                    sceneId, sceneSession.getSceneSessionId(), e);
            throw e;
        }
    }

    /**
     * 验证故事状态
     */
    private boolean validateStory(AgentStoryBaseVO story, String sessionId) {
        if (story == null) {
            log.warn("故事信息不存在或者已经删除 - 会话ID: {}", sessionId);
            return false;
        }

        if (story.getShelfStatus() != StoryConstant.SHELF_ON_STATUS) {
            log.error("故事未上架 - 会话ID: {}, 故事名: {}, 上架状态: {}",
                    sessionId, story.getName(), story.getShelfStatus());
            return false;
        }

        if (story.getStatus() != StoryConstant.STORY_STATUS_PUBLISHED) {
            log.error("故事未发布 - 会话ID: {}, 故事名: {}, 发布状态: {}",
                    sessionId, story.getName(), story.getStatus());
            return false;
        }

        return true;
    }

    /**
     * 优化的场景历史记录压缩方法（参照chat-service流程）
     */
    private boolean compressSceneHistoryOptimized(AgentStoryBaseVO story, MultiChatSession session,
                                                 MultiChatSession.SceneSession sceneSession, AgentStorySceneBaseVO sceneVo) {
        try {
            String sceneSessionId = sceneSession.getSceneSessionId();
            Long sceneId = sceneSession.getSceneId();
            Long storyId = story.getId();

            // 获取或创建压缩记录
            MultiChatMsgCompress compressItem = compressService.findFirstMessage(storyId,
                    session.getSessionId(), sceneId, sceneSessionId);

            Long compressedSeqNum;
            if (compressItem == null) {
                compressedSeqNum = 0L;
                compressItem = createNewCompressItem(story, sceneId, session, sceneSession);
            } else {
                compressedSeqNum = compressItem.getCompressSeqNumber();
                // 修复数据一致性问题：确保使用正确的压缩序号
                if (compressedSeqNum == null) {
                    compressedSeqNum = 0L;
                }
            }

            Long lstSeqNum = sceneSession.getLstSeqNum();

            // 获取需要压缩的消息 - 修复数据一致性问题：使用正确的起始序号
            List<MultiChatMsg> messages = chatMessageService.findAllMessages(
                    session.getStoryId(), sceneId, compressedSeqNum, lstSeqNum);

            if (CollectionUtils.isEmpty(messages)) {
                if (compressConfig.isVerboseLogging()) {
                    log.debug("场景 {} 的历史记录为空，序号范围: {} - {}",
                            sceneVo.getSceneName(), compressedSeqNum, lstSeqNum);
                }
                return false;
            }

            log.info("处理场景 {} 的历史记录压缩 - 消息数量: {}, 序号范围: {} - {}",
                    sceneVo.getSceneName(), messages.size(), compressedSeqNum, lstSeqNum);

            // 📊 检查是否需要分批处理
            if (messages.size() > maxMessagesPerBatch) {
                log.info("场景 {} 的消息数量 {} 超过单批限制 {}，将进行分批压缩",
                        sceneVo.getSceneName(), messages.size(), maxMessagesPerBatch);

                return processMessagesBatchOptimized(messages, story, session, sceneSession, sceneVo, compressItem, lstSeqNum);
            }

            // 流式处理历史消息，避免内存溢出（参照chat-service）
            String finalCompressContent = processHistoryMessagesOptimized(messages, story, compressItem, sceneVo);

            if (finalCompressContent != null && !finalCompressContent.trim().isEmpty()) {
                // 保存压缩结果
                saveCompressedResult(compressItem, finalCompressContent, lstSeqNum, session, sceneSession);

                log.info("场景 {} 的历史记录压缩完成，压缩序号: {}, 消息数量: {}",
                        sceneVo.getSceneName(), lstSeqNum, messages.size());
                return true;
            } else {
                log.warn("场景 {} 的历史记录压缩失败，AI返回空结果", sceneVo.getSceneName());
                return false;
            }

        } catch (Exception e) {
            log.error("压缩场景历史记录异常 - 场景: {}", sceneVo.getSceneName(), e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 优化的历史消息处理方法（参照chat-service的流式处理）
     */
    private String processHistoryMessagesOptimized(List<MultiChatMsg> messages, AgentStoryBaseVO story,
                                                  MultiChatMsgCompress compressItem, AgentStorySceneBaseVO sceneVo) {
        log.debug("开始处理场景 {} 的历史消息，消息数量: {}", sceneVo.getSceneName(), messages.size());

        try {
            // 检查内存使用情况
            checkMemoryBeforeProcessing(messages.size());

            // 构建历史对话内容（优化内存使用）
            String historyContent = buildHistoryContentOptimized(messages, story);

            // 获取现有摘要
            String existingSummary = compressItem.getSummary();
            if (existingSummary != null && !existingSummary.trim().isEmpty()) {
                historyContent = existingSummary + "\n\n" + historyContent;
            }

            // 检查内容长度，避免过长的内容导致AI服务超时
            if (historyContent.length() > 25000) {
                log.warn("历史内容过长 ({} 字符)，进行分段处理", historyContent.length());
                return compressLongContentOptimized(existingSummary, historyContent, story, sceneVo);
            }

            // 调用AI服务进行智能压缩（带超时控制）
            String compressedResult = callAIServiceWithTimeoutOptimized(story, sceneVo, historyContent);

            if (compressedResult != null && !compressedResult.trim().isEmpty()) {
                log.debug("AI压缩完成，原始长度: {}, 压缩后长度: {}",
                        historyContent.length(), compressedResult.length());
                return compressedResult;
            } else {
                log.warn("AI压缩返回空结果，使用简单摘要");
                return createSimpleSummary(existingSummary, messages.size());
            }

        } catch (OutOfMemoryError e) {
            log.error("消息压缩时发生内存溢出，消息数量: {}", messages.size(), e);
            // 尝试释放内存
            System.gc();
            return createSimpleSummary(compressItem.getSummary(), messages.size());
        } catch (Exception e) {
            log.error("AI压缩失败，使用简单摘要: {}", e.getMessage());
            return createSimpleSummary(compressItem.getSummary(), messages.size());
        }
    }

    /**
     * 优化的历史内容构建方法（内存优化版本）
     */
    private String buildHistoryContentOptimized(List<MultiChatMsg> messages, AgentStoryBaseVO story) {
        if (messages.isEmpty()) {
            return "";
        }

        // 预估容量，避免频繁扩容
        int estimatedSize = Math.min(messages.size() * 80, 10000);
        StringBuilder history = new StringBuilder(estimatedSize);
        String myName = story.getMyName();

        try {
            for (int i = 0; i < messages.size(); i++) {
                MultiChatMsg message = messages.get(i);
                String role = message.getRole();
                // 移除content中的心理内容（括号内容）
                String cleanContent = ChatUtils.removeBracketContent(message.getContent());

                if (CHAT_USER_ROLE.equals(role)) {
                    history.append("【").append(myName).append("】：").append(cleanContent).append("\n");
                } else {
                    String agentName = message.getAgentName();
                    history.append("【").append(agentName).append("】：").append(cleanContent).append("\n");
                }

                // 每处理10条消息检查一次内存
                if (i % 10 == 0 && i > 0) {
                    checkMemoryDuringProcessing();
                }

                // 如果内容过长，提前截断避免内存溢出
                if (history.length() > 30000) {
                    log.warn("历史内容长度超过30000字符，提前截断以避免内存问题");
                    break;
                }
            }

            return history.toString();

        } catch (OutOfMemoryError e) {
            log.error("构建历史内容时发生内存溢出", e);
            System.gc();
            throw new RuntimeException("内存不足，无法构建历史内容", e);
        }
    }

    /**
     * 带超时的AI服务调用（参照chat-service）
     */
    private String callAIServiceWithTimeoutOptimized(AgentStoryBaseVO story, AgentStorySceneBaseVO sceneVo, String historyContent) throws Exception {
        try {
            List<ChatContextVO> contexts = buildCompressContexts(story, sceneVo, historyContent);

            // 根据历史内容长度动态计算超时时间
            long dynamicTimeout = calculateDynamicTimeout(
                    historyContent.length(),
                    compressConfig.getAiRequestTimeout()
            );

            log.debug("调用AI服务进行压缩，内容长度: {}, 超时时间: {}ms", historyContent.length(), dynamicTimeout);

            // 这里可以添加超时控制
            AIResponseVO response = volcanoArkService.sendChatMessageTraditional(contexts);

            if (response != null && response.getContexts() != null && !response.getContexts().isEmpty()) {
                return response.getContexts().get(0).getContent();
            }

            return null;
        } catch (Exception e) {
            log.error("AI服务调用失败", e);
            throw e;
        }
    }

    /**
     * 处理过长内容的分段压缩（参照chat-service）
     */
    private String compressLongContentOptimized(String lastCompressContent, String historyContent,
                                               AgentStoryBaseVO story, AgentStorySceneBaseVO sceneVo) {
        // 将长内容分段处理，减小分段大小以降低内存使用
        int maxChunkSize = 15000;
        List<String> chunks = null;

        try {
            chunks = splitContentOptimized(historyContent, maxChunkSize);

            String currentContent = lastCompressContent;
            for (int i = 0; i < chunks.size(); i++) {
                log.debug("处理第 {} 段内容，共 {} 段", i + 1, chunks.size());

                // 在处理每段前检查内存
                checkMemoryDuringProcessing();

                try {
                    String chunk = chunks.get(i);
                    List<ChatContextVO> contexts = buildCompressContexts(story, sceneVo, currentContent + "\n\n" + chunk);
                    AIResponseVO response = volcanoArkService.sendChatMessageTraditional(contexts);

                    if (response != null && response.getContexts() != null && !response.getContexts().isEmpty()) {
                        String chunkResult = response.getContexts().get(0).getContent();
                        if (chunkResult != null && !chunkResult.trim().isEmpty()) {
                            currentContent = chunkResult;
                        }
                    } else {
                        log.warn("第 {} 段压缩失败，保持原内容", i + 1);
                    }

                    // 清理当前段的引用
                    chunks.set(i, null);

                } catch (OutOfMemoryError e) {
                    log.error("第 {} 段压缩时发生内存溢出", i + 1, e);
                    System.gc();
                    break; // 停止处理剩余段落
                } catch (Exception e) {
                    log.error("第 {} 段压缩失败: {}", i + 1, e.getMessage());
                }

                // 每处理3段休眠一下，给GC时间
                if (i % 3 == 0 && i > 0) {
                    try {
                        Thread.sleep(100);
                        System.gc();
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            }

            return currentContent;

        } catch (OutOfMemoryError e) {
            log.error("分段压缩时发生内存溢出", e);
            System.gc();
            return createSimpleSummary(lastCompressContent, 0);
        } finally {
            // 清理chunks引用
            if (chunks != null) {
                chunks.clear();
                chunks = null;
            }
        }
    }

    /**
     * 分割长内容（参照chat-service）
     */
    private List<String> splitContentOptimized(String content, int maxChunkSize) {
        if (content == null || content.isEmpty()) {
            return new ArrayList<>();
        }

        // 预估分段数量，避免List频繁扩容
        int estimatedChunks = (content.length() / maxChunkSize) + 1;
        List<String> chunks = new ArrayList<>(estimatedChunks);

        int start = 0;
        int contentLength = content.length();

        try {
            while (start < contentLength) {
                int end = Math.min(start + maxChunkSize, contentLength);

                // 尝试在句号或换行符处分割，避免截断句子
                if (end < contentLength) {
                    int lastPeriod = content.lastIndexOf('。', end);
                    int lastNewline = content.lastIndexOf('\n', end);
                    int splitPoint = Math.max(lastPeriod, lastNewline);

                    if (splitPoint > start) {
                        end = splitPoint + 1;
                    }
                }

                // 创建子字符串时检查内存
                try {
                    String chunk = content.substring(start, end);
                    chunks.add(chunk);
                } catch (OutOfMemoryError e) {
                    log.error("创建内容分段时发生内存溢出，当前分段: {}", chunks.size(), e);
                    System.gc();
                    break;
                }

                start = end;

                // 每创建5个分段检查一次内存
                if (chunks.size() % 5 == 0) {
                    checkMemoryDuringProcessing();
                }
            }

        } catch (OutOfMemoryError e) {
            log.error("分割内容时发生内存溢出", e);
            System.gc();
        }

        log.debug("内容分割完成，共生成 {} 个分段", chunks.size());
        return chunks;
    }

    /**
     * 创建简单摘要（参照chat-service）
     */
    private String createSimpleSummary(String lastCompressContent, int messageCount) {
        return (lastCompressContent != null ? lastCompressContent : "") +
               "\n[系统摘要] 包含" + messageCount + "条对话记录（AI压缩失败）";
    }

    /**
     * 保存压缩结果（参照chat-service）
     */
    private void saveCompressedResult(MultiChatMsgCompress compressItem, String compressedContent,
                                     Long lastCompressSeq, MultiChatSession session,
                                     MultiChatSession.SceneSession sceneSession) {
        log.debug("保存场景会话 {} 的压缩结果", sceneSession.getSceneSessionId());

        try {
            // 保存压缩结果 - 确保数据一致性
            compressItem.setSummary(compressedContent);
            compressItem.setCompressSeqNumber(lastCompressSeq);
            compressItem.setUpdateTime(LocalDateTime.now());
            compressService.saveSummary(compressItem);

            // 同步更新场景会话的压缩序号，确保数据一致性
            sceneSession.setCompressedSeqNum(lastCompressSeq);
            chatSessionService.saveSession(session);

            log.info("场景会话 {} 压缩结果保存成功", sceneSession.getSceneSessionId());

        } catch (Exception e) {
            log.error("保存场景会话 {} 压缩结果时发生错误", sceneSession.getSceneSessionId(), e);
            throw e;
        }
    }

    /**
     * 创建新的压缩记录
     */
    private MultiChatMsgCompress createNewCompressItem(AgentStoryBaseVO story, Long sceneId, MultiChatSession session,
                                                       MultiChatSession.SceneSession sceneSession) {
        MultiChatMsgCompress compressItem = new MultiChatMsgCompress();
        compressItem.setId(UUID.randomUUID().toString());
        compressItem.setStoryId(story.getId());
        compressItem.setStorySessionId(session.getSessionId());
        compressItem.setSceneId(sceneId);
        compressItem.setSceneSessionId(sceneSession.getSceneSessionId());
        compressItem.setUsername(session.getUsername());
        compressItem.setSummary("");
        compressItem.setUpdateTime(LocalDateTime.now());
        return compressItem;
    }

    /**
     * 构建历史对话内容
     */
    private String buildHistoryContent(List<MultiChatMsg> messages, AgentStoryBaseVO story) {
        StringBuilder history = new StringBuilder();
        String myName = story.getMyName();

        for (MultiChatMsg message : messages) {
            String role = message.getRole();
            // 移除content中的心理内容（括号内容）
            String cleanContent = ChatUtils.removeBracketContent(message.getContent());

            if (CHAT_USER_ROLE.equals(role)) {
                history.append("【").append(myName).append("】：").append(cleanContent).append("\n");
            } else {
                String agentName = message.getAgentName();
                history.append("【").append(agentName).append("】：").append(cleanContent).append("\n");
            }
        }

        return history.toString();
    }

    /**
     * 📊 分批处理消息 - 处理超过限制的大量消息
     */
    private void processMessagesBatch(List<MultiChatMsg> allMessages, AgentStoryBaseVO story,
                                      MultiChatSession session, MultiChatSession.SceneSession sceneSession,
                                      AgentStorySceneBaseVO sceneVo, MultiChatMsgCompress compressItem,
                                      Long finalSeqNum) {

        String existingSummary = compressItem.getSummary();
        StringBuilder cumulativeSummary = new StringBuilder();

        if (existingSummary != null && !existingSummary.trim().isEmpty()) {
            cumulativeSummary.append(existingSummary).append("\n\n");
        }

        // 计算分批参数
        int totalMessages = allMessages.size();
        int batchCount = (int) Math.ceil((double) totalMessages / maxMessagesPerBatch);

        log.info("开始分批压缩场景 {} 的 {} 条消息，分为 {} 批，每批最多 {} 条，重叠 {} 条",
                sceneVo.getSceneName(), totalMessages, batchCount, maxMessagesPerBatch, batchOverlapSize);

        for (int batchIndex = 0; batchIndex < batchCount; batchIndex++) {
            try {
                // 计算当前批次的消息范围
                int startIndex = batchIndex * maxMessagesPerBatch;
                if (batchIndex > 0) {
                    // 从第二批开始，向前重叠一些消息以保持上下文
                    startIndex = Math.max(0, startIndex - batchOverlapSize);
                }

                int endIndex = Math.min(totalMessages, (batchIndex + 1) * maxMessagesPerBatch);
                List<MultiChatMsg> batchMessages = allMessages.subList(startIndex, endIndex);

                log.info("处理第 {}/{} 批消息，范围: {} - {}，消息数: {},前期摘要",
                        batchIndex + 1, batchCount, startIndex, endIndex - 1, batchMessages.size(), cumulativeSummary);

                // 构建当前批次的历史内容
                String batchHistoryContent = buildHistoryContent(batchMessages, story);

                // 如果有累积摘要，添加到当前批次内容前面
                if (cumulativeSummary.length() > 0) {
                    batchHistoryContent = "【前期摘要】\n" + cumulativeSummary.toString() +
                            "\n【当前对话】\n" + batchHistoryContent;
                }

                // 执行AI压缩 - 使用分批压缩的提示词
                boolean isFirstBatch = (batchIndex == 0);
                boolean hasPreviousSummary = (cumulativeSummary.length() > 0);
                String batchSummary = performAICompressionWithPrompt(story, sceneVo, batchHistoryContent,
                        isFirstBatch, hasPreviousSummary, batchIndex + 1, batchCount);

                if (batchSummary != null && !batchSummary.trim().isEmpty()) {
                    // 更新累积摘要
                    cumulativeSummary.setLength(0); // 清空之前的内容
                    cumulativeSummary.append(batchSummary);

                    log.info("第 {}/{} 批压缩完成，摘要长度: {} 字符",
                            batchIndex + 1, batchCount, batchSummary.length());
                } else {
                    log.warn("第 {}/{} 批压缩失败，AI返回空结果", batchIndex + 1, batchCount);
                    // 继续处理下一批，不中断整个流程
                }

                // 更新统计
                batchedMessagesTotal.addAndGet(batchMessages.size());
                splitBatchesTotal.incrementAndGet();

            } catch (Exception e) {
                log.error("处理第 {}/{} 批消息时发生异常", batchIndex + 1, batchCount, e);
                // 继续处理下一批，不中断整个流程
            }
        }

        // 保存最终的累积摘要
        if (cumulativeSummary.length() > 0) {
            compressItem.setSummary(cumulativeSummary.toString());
            compressItem.setCompressSeqNumber(finalSeqNum);
            compressItem.setUpdateTime(LocalDateTime.now());
            compressService.saveSummary(compressItem);

            // 同步更新场景会话的压缩序号
            sceneSession.setCompressedSeqNum(finalSeqNum);
            chatSessionService.saveSession(session);

            log.info("场景 {} 分批压缩完成，总消息数: {}，最终摘要长度: {} 字符",
                    sceneVo.getSceneName(), totalMessages, cumulativeSummary.length());
        } else {
            log.warn("场景 {} 分批压缩失败，未生成有效摘要", sceneVo.getSceneName());
        }
    }

    /**
     * 执行AI压缩 - 支持自定义提示词的分批压缩
     */
    private String performAICompressionWithPrompt(AgentStoryBaseVO story, AgentStorySceneBaseVO sceneVo, String historyContent, boolean isFirstBatch,
                                                  boolean hasPreviousSummary, int batchIndex, int totalBatches) {
        try {
            if (historyContent == null || historyContent.trim().isEmpty()) {
                log.warn("历史内容为空，跳过AI压缩");
                return null;
            }

            // 获取针对当前批次的优化提示词
            String compressionPrompt = getCompressionPrompt(story, sceneVo, isFirstBatch, story.getMyName(), hasPreviousSummary, batchIndex, totalBatches);


            List<ChatContextVO> contexts = buildCompressContextsWithPrompt(historyContent, compressionPrompt);

            log.debug("执行第 {}/{} 批AI压缩，提示词长度: {} 字符，内容长度: {} 字符",
                    batchIndex, totalBatches, compressionPrompt.length(), historyContent.length());

            AIResponseVO response = volcanoArkService.sendChatMessageTraditional(contexts);

            if (response != null && response.getContexts() != null && !response.getContexts().isEmpty()) {
                String result = response.getContexts().get(0).getContent();
                if (result != null && !result.trim().isEmpty()) {
                    log.debug("第 {}/{} 批AI压缩成功，输出长度: {} 字符", batchIndex, totalBatches, result.length());


                    return result;
                } else {
                    log.warn("第 {}/{} 批AI返回了空的压缩结果", batchIndex, totalBatches);
                    return null;
                }
            }

            log.warn("第 {}/{} 批AI压缩响应为空或格式不正确", batchIndex, totalBatches);
            return null;
        } catch (Exception e) {
            // 异常处理保持不变
            if (e instanceof InterruptedException) {
                Thread.currentThread().interrupt();
                log.error("第 {}/{} 批AI压缩被中断", batchIndex, totalBatches, e);
                throw new NonRetryableCompressException("AI压缩被中断", e);
            } else if (e instanceof TimeoutException) {
                log.warn("第 {}/{} 批AI压缩请求超时", batchIndex, totalBatches, e);
                throw new RetryableCompressException("AI压缩超时", e);
            } else if (e.getMessage() != null &&
                    (e.getMessage().contains("网络") || e.getMessage().contains("连接") ||
                            e.getMessage().contains("服务不可用"))) {
                log.warn("第 {}/{} 批AI压缩网络相关错误", batchIndex, totalBatches, e);
                throw new RetryableCompressException("AI压缩网络错误", e);
            } else {
                log.error("第 {}/{} 批AI压缩失败", batchIndex, totalBatches, e);
                throw new NonRetryableCompressException("AI压缩失败", e);
            }
        }
    }

    /**
     * 执行AI压缩 - 改进异常处理，区分可重试和不可重试的异常（兼容原有调用）
     */
    private String performAICompression(AgentStoryBaseVO story, AgentStorySceneBaseVO sceneVo, String historyContent) {
        try {
            if (historyContent == null || historyContent.trim().isEmpty()) {
                log.warn("历史内容为空，跳过AI压缩");
                return null;
            }

            List<ChatContextVO> contexts = buildCompressContexts(story, sceneVo, historyContent);
            AIResponseVO response = volcanoArkService.sendChatMessageTraditional(contexts);

            if (response != null && response.getContexts() != null && !response.getContexts().isEmpty()) {
                String result = response.getContexts().get(0).getContent();
                if (result != null && !result.trim().isEmpty()) {
                    return result;
                } else {
                    log.warn("AI返回了空的压缩结果");
                    return null;
                }
            }

            log.warn("AI压缩响应为空或格式不正确");
            return null;
        } catch (Exception e) {
            // 区分不同类型的异常，使用自定义异常类
            if (e instanceof InterruptedException) {
                Thread.currentThread().interrupt();
                log.error("AI压缩被中断", e);
                throw new NonRetryableCompressException("AI压缩被中断", e);
            } else if (e instanceof TimeoutException) {
                log.warn("AI压缩请求超时", e);
                throw new RetryableCompressException("AI压缩超时", e);
            } else if (e.getMessage() != null &&
                    (e.getMessage().contains("网络") || e.getMessage().contains("连接") ||
                            e.getMessage().contains("服务不可用"))) {
                log.warn("AI压缩网络相关错误", e);
                throw new RetryableCompressException("AI压缩网络错误", e);
            } else {
                log.error("AI压缩失败", e);
                throw new NonRetryableCompressException("AI压缩失败", e);
            }
        }
    }

    /**
     * 构建压缩上下文
     */
    private List<ChatContextVO> buildCompressContexts(AgentStoryBaseVO story, AgentStorySceneBaseVO sceneVo, String historyContent) {
        List<ChatContextVO> contexts = new ArrayList<>();

        // 系统提示词
        contexts.add(createChatContextVO("system", "text", getCompressionPrompt(story, sceneVo), "text"));

        // 用户对话内容
        contexts.add(createChatContextVO("user", "text", historyContent, "text"));

        return contexts;
    }

    /**
     * 创建ChatContextVO对象
     */
    private ChatContextVO createChatContextVO(String role, String type, String content, String contentType) {
        ChatContextVO context = new ChatContextVO();
        context.setRole(role);
        context.setType(type);
        context.setContent(content);
        context.setContentType(contentType);
        return context;
    }
    private String formatGender(Integer gender) {
        if (gender == null) {
            return "性别未知";
        }
        return switch (gender) {
            case 1 -> "男性";
            case 2 -> "女性";
            case 3 -> "其他";
            default -> "性别未知";
        };
    }
    /**
     * 获取压缩提示词
     */
    /**
     * 获取压缩提示词 - 支持不同压缩模式
     *
     * @param isFirstBatch       是否为第一批（完整压缩）
     * @param hasPreviousSummary 是否有前期摘要
     * @param batchIndex         当前批次索引（从1开始）
     * @param totalBatches       总批次数
     * @return 优化的压缩提示词
     */
    private String getCompressionPrompt(AgentStoryBaseVO story, AgentStorySceneBaseVO sceneVo,
                                        boolean isFirstBatch, String mainRole, boolean hasPreviousSummary,
                                        int batchIndex, int totalBatches) {

        // 根据批次和内容量选择模板复杂度
        TemplateLevel templateLevel = determineTemplateLevel(batchIndex, totalBatches, hasPreviousSummary);

        StringBuilder prompt = new StringBuilder();
        List<Long> agentIds = sceneVo.getAgentRelations().stream()
                .map(SceneAgentRelationBaseVO::getAgentId)
                .collect(Collectors.toList());

        String agentCacheKey = cacheProperties.getCacheName(AgentCommonCacheConstant.AGENT_BASE_MAP_CACHE_KEY);
        Map<Long, AgentBaseVO> agentMap = agentProxyService.getAgentsByIds(agentCacheKey, agentIds);

        // 基础指导原则
        prompt.append("# 故事压缩指南（严格控制在12K token内）\n\n");

        // 添加故事背景（简化版）
        addStoryBackground(prompt, story, mainRole, agentMap);

        // 添加核心原则
        addCoreRules(prompt, mainRole);

        // 根据模板级别添加相应的输出格式
        switch (templateLevel) {
            case SIMPLE:
                addSimpleTemplate(prompt, mainRole);
                break;
            case STANDARD:
                addStandardTemplate(prompt, mainRole);
                break;
            case DETAILED:
                addDetailedTemplate(prompt, mainRole);
                break;
        }

        // 添加分批处理说明
        if (totalBatches > 1) {
            addBatchProcessingInfo(prompt, batchIndex, totalBatches, hasPreviousSummary);
        }

        // 添加特别注意事项
        addSpecialNotes(prompt, mainRole);

        prompt.append("请开始压缩分析：\n");

        return prompt.toString();
    }

    // 模板复杂度枚举
    private enum TemplateLevel {
        SIMPLE,    // 简化模板，适用于大量内容
        STANDARD,  // 标准模板，适用于中等内容
        DETAILED   // 详细模板，适用于少量内容
    }

    // 确定模板复杂度
    private TemplateLevel determineTemplateLevel(int batchIndex, int totalBatches, boolean hasPreviousSummary) {
        // 多批次处理时使用简化模板
        if (totalBatches > 3) {
            return TemplateLevel.SIMPLE;
        }

        // 中间批次使用标准模板
        if (totalBatches > 1 && batchIndex > 1 && batchIndex < totalBatches) {
            return TemplateLevel.STANDARD;
        }

        // 单批次或最后批次使用详细模板
        return TemplateLevel.DETAILED;
    }

    // 添加故事背景（简化版）
    private void addStoryBackground(StringBuilder prompt, AgentStoryBaseVO story,
                                    String mainRole, Map<Long, AgentBaseVO> agentMap) {
        prompt.append("## 故事信息\n");
        prompt.append("**故事**：").append(story.getName()).append(" | ");
        prompt.append("**主角**：").append(story.getMyName()).append("（").append(mainRole).append("）\n\n");

        prompt.append("**其他角色**：\n");
        for (Map.Entry<Long, AgentBaseVO> entry : agentMap.entrySet()) {
            AgentBaseVO agent = entry.getValue();
            prompt.append("- ").append(agent.getName())
                    .append("：").append(agent.getIdentity())
                    .append("，").append(formatGender(agent.getGender()))
                    .append("，").append(agent.getIntroduction()).append("\n");
        }
        prompt.append("\n");
    }

    // 添加核心规则
    private void addCoreRules(StringBuilder prompt, String mainRole) {
        prompt.append("## 压缩原则\n");
        prompt.append("- 保留重要情节和角色互动\n");
        prompt.append("- 记录关系变化和情感发展\n");
        prompt.append("- 保存承诺、约定和未来计划\n");
        prompt.append("- 重点关注与").append(mainRole).append("的互动\n");
        prompt.append("- 严格控制输出长度，优先记录核心信息\n\n");
    }

    // 简化模板
    private void addSimpleTemplate(StringBuilder prompt, String mainRole) {
        prompt.append("## 输出格式（简化版）\n\n");

        prompt.append("### 【核心角色】\n");
        prompt.append("**").append(mainRole).append("**：[当前状态] - [主要变化] - [重要特征]\n");
        prompt.append("**[其他角色]**：[关系类型] - [亲密度] - [最新发展]\n\n");

        prompt.append("### 【关键事件】\n");
        prompt.append("按重要性排序：\n");
        prompt.append("1. **[事件]**：[时间地点] - [参与者] - [结果影响]\n");
        prompt.append("2. **[事件]**：[时间地点] - [参与者] - [结果影响]\n\n");

        prompt.append("### 【关系变化】\n");
        prompt.append("- **").append(mainRole).append(" ↔ [角色]**：[起点]→[现状]→[趋势]\n\n");

        prompt.append("### 【重要承诺】\n");
        prompt.append("- [约定内容] - [参与者] - [状态]\n\n");

        prompt.append("### 【待续线索】\n");
        prompt.append("- [未解决问题]\n");
        prompt.append("- [等待回应的行动]\n\n");
    }

    // 标准模板
    private void addStandardTemplate(StringBuilder prompt, String mainRole) {
        prompt.append("## 输出格式（标准版）\n\n");

        prompt.append("### 【人物状态】\n");
        prompt.append("**").append(mainRole).append("**\n");
        prompt.append("- 身份职业：[具体信息]\n");
        prompt.append("- 性格特征：[主要特点]\n");
        prompt.append("- 当前状态：[情况描述]\n");
        prompt.append("- 心理变化：[重要转变]\n\n");

        prompt.append("**[其他角色姓名]**\n");
        prompt.append("- 基本信息：[身份年龄性格]\n");
        prompt.append("- 与").append(mainRole).append("关系：[关系类型和亲密度]\n");
        prompt.append("- 态度变化：[对主角态度的发展]\n\n");

        prompt.append("### 【时间线发展】\n");
        prompt.append("#### 重要事件\n");
        prompt.append("**[时间] - [地点]**\n");
        prompt.append("- 参与者：[人物列表]\n");
        prompt.append("- 事件：[详细描述]\n");
        prompt.append("- 关键对话：[重要对话内容]\n");
        prompt.append("- ").append(mainRole).append("反应：[主角反应]\n");
        prompt.append("- 影响：[对后续的影响]\n\n");

        prompt.append("### 【关系动态】\n");
        prompt.append("- **").append(mainRole).append(" ↔ [角色A]**：[关系] - [亲密度] - [发展趋势] - [未解决问题]\n");
        prompt.append("- **").append(mainRole).append(" ↔ [角色B]**：[关系] - [亲密度] - [发展趋势] - [未解决问题]\n\n");

        prompt.append("### 【承诺约定】\n");
        prompt.append("- **约定**：[具体内容] - [参与人] - [时间地点] - [当前状态]\n\n");

        prompt.append("### 【冲突与张力】\n");
        prompt.append("- 当前冲突：[具体描述]\n");
        prompt.append("- ").append(mainRole).append("困境：[主要困难]\n");
        prompt.append("- 情感张力：[紧张点]\n\n");

        prompt.append("### 【未完成线索】\n");
        prompt.append("- 悬而未决：[具体事项]\n");
        prompt.append("- 等待回应：[他人期待]\n");
        prompt.append("- ").append(mainRole).append("待办：[需处理事项]\n\n");
    }

    // 详细模板
    private void addDetailedTemplate(StringBuilder prompt, String mainRole) {
        prompt.append("## 输出格式（详细版）\n\n");

        prompt.append("### 【人物档案】\n");
        prompt.append("#### 主要角色\n");
        prompt.append("**").append(mainRole).append("**（主角）\n");
        prompt.append("- 身份职业：[具体职业/身份]\n");
        prompt.append("- 年龄外貌：[年龄段及外貌特征]\n");
        prompt.append("- 性格特征：[3-5个关键词]\n");
        prompt.append("- 行为特点：[典型行为或口头禅]\n");
        prompt.append("- 背景信息：[重要背景]\n");
        prompt.append("- 当前状态：[在故事中的状况]\n");
        prompt.append("- 成长变化：[性格或心理的发展]\n\n");

        prompt.append("**[其他角色姓名]**\n");
        prompt.append("- 基本信息：[身份/年龄/外貌/性格]\n");
        prompt.append("- 行为特点：[说话风格/典型行为]\n");
        prompt.append("- 与").append(mainRole).append("关系：[具体关系描述]\n");
        prompt.append("- 态度发展：[对主角态度的变化过程]\n\n");

        prompt.append("#### 关系发展轨迹\n");
        prompt.append("- **").append(mainRole).append(" ↔ [角色A]**：[初识] → [发展] → [现状] → [趋势]\n");
        prompt.append("- **").append(mainRole).append(" ↔ [角色B]**：[初识] → [发展] → [现状] → [趋势]\n\n");

        prompt.append("### 【详细时间线】\n");
        prompt.append("#### 核心事件详述\n");
        prompt.append("**[具体时间] - [具体地点]**\n");
        prompt.append("- 参与者：[详细人物列表]\n");
        prompt.append("- 事件经过：[详细事件描述]\n");
        prompt.append("- 关键对话：[重要对话原文或核心内容]\n");
        prompt.append("- 情感变化：[具体情感变化]\n");
        prompt.append("- ").append(mainRole).append("反应：[主角的具体反应和内心想法]\n");
        prompt.append("- 其他角色反应：[其他重要角色的反应]\n");
        prompt.append("- 结果影响：[对关系和后续情节的具体影响]\n\n");

        prompt.append("### 【性格与行为分析】\n");
        prompt.append("#### 对话风格特征\n");
        prompt.append("- **").append(mainRole).append("**：[说话特点、常用词汇、语气特色]\n");
        prompt.append("- **[角色A]**：[说话特点、常用词汇、语气特色]\n\n");

        prompt.append("### 【关系深度分析】\n");
        prompt.append("#### 亲密度等级参考\n");
        prompt.append("陌生→认识→朋友→好友→亲密→恋人\n\n");

        prompt.append("#### 当前关系状态\n");
        prompt.append("- **").append(mainRole).append(" ↔ [角色]**：[关系类型] - [亲密度] - [最新互动] - [情感状态] - [未解决张力]\n\n");

        prompt.append("### 【重要承诺记录】\n");
        prompt.append("- **约定内容**：[详细内容]\n");
        prompt.append("- **参与人员**：[相关人员及角色]\n");
        prompt.append("- **时间地点**：[具体安排]\n");
        prompt.append("- **").append(mainRole).append("承诺**：[主角的具体承诺]\n");
        prompt.append("- **履行状态**：[完成情况/进展/冲突]\n\n");

        prompt.append("### 【冲突与发展张力】\n");
        prompt.append("- **核心冲突**：[主要矛盾点]\n");
        prompt.append("- **").append(mainRole).append("面临选择**：[重要决策点]\n");
        prompt.append("- **关系张力**：[角色间的紧张关系]\n");
        prompt.append("- **潜在发展**：[可能的情节走向]\n\n");

        prompt.append("### 【待续线索清单】\n");
        prompt.append("- **未解决事件**：[具体描述及重要程度]\n");
        prompt.append("- **等待").append(mainRole).append("行动**：[其他角色的期待]\n");
        prompt.append("- **").append(mainRole).append("计划**：[主角的后续打算]\n");
        prompt.append("- **伏笔线索**：[为后续发展埋下的线索]\n\n");
    }

    // 添加分批处理信息
    private void addBatchProcessingInfo(StringBuilder prompt, int batchIndex, int totalBatches, boolean hasPreviousSummary) {
        prompt.append("## 分批说明\n");
        prompt.append("当前：第").append(batchIndex).append("/").append(totalBatches).append("批");

        if (hasPreviousSummary) {
            prompt.append(" | 已有前期摘要，请补充更新");
        }

        if (batchIndex < totalBatches) {
            prompt.append(" | 重点记录当前时间段事件");
        } else {
            prompt.append(" | 最终批次，完整总结");
        }
        prompt.append("\n\n");
    }

    // 添加特别注意事项
    private void addSpecialNotes(StringBuilder prompt, String mainRole) {
        prompt.append("## 重要提醒\n");
        prompt.append("1. **严格控制长度**：总输出不超过12K token\n");
        prompt.append("2. **优先级排序**：重要信息优先，细节可适当精简\n");
        prompt.append("3. **避免重复**：相同信息不要在多个章节重复\n");
        prompt.append("4. **聚焦主角**：以").append(mainRole).append("视角为中心组织信息\n");
        prompt.append("5. **保留关键**：重要对话、承诺、转折点必须保留\n");
        prompt.append("6. **明确时序**：事件发展要有清晰的时间逻辑\n\n");
    }




    /**
     * 获取压缩提示词 - 兼容原有调用（单批压缩）
     */
    private String getCompressionPrompt(AgentStoryBaseVO story, AgentStorySceneBaseVO sceneVo) {
        return getCompressionPrompt(story, sceneVo, true, story.getMyName(), false, 1, 1);
    }

    /**
     * 构建压缩上下文 - 支持自定义提示词
     */
    private List<ChatContextVO> buildCompressContextsWithPrompt(String historyContent, String customPrompt) {
        List<ChatContextVO> contexts = new ArrayList<>();

        // 添加自定义系统提示
        contexts.add(new ChatContextVO("system", customPrompt));

        // 构建用户消息，明确这是需要压缩的历史记录
        String userMessage = "以下是需要压缩的历史对话记录：\n\n" + historyContent + "\n\n请按照系统要求进行压缩分析。";
        contexts.add(new ChatContextVO("user", userMessage));

        return contexts;
    }


    /**
     * 根据内容复杂度动态计算超时时间
     *
     * @param contentLength 内容长度
     * @param baseTimeout   基础超时时间
     * @return 调整后的超时时间
     */
    private long calculateDynamicTimeout(int contentLength, long baseTimeout) {
        // 根据内容长度动态调整超时时间
        // 每1000个字符增加10秒超时时间
        long extraTimeout = (contentLength / 1000) * 10000;

        // 最大不超过基础超时时间的3倍
        long maxTimeout = baseTimeout * 3;
        long adjustedTimeout = baseTimeout + extraTimeout;

        long finalTimeout = Math.min(adjustedTimeout, maxTimeout);

        if (finalTimeout > baseTimeout) {
            log.debug("根据内容长度 {} 调整超时时间：{} -> {} ms",
                    contentLength, baseTimeout, finalTimeout);
        }

        return finalTimeout;
    }

    /**
     * 优化的分批消息处理方法（保留原有功能）
     */
    private boolean processMessagesBatchOptimized(List<MultiChatMsg> allMessages, AgentStoryBaseVO story,
                                                 MultiChatSession session, MultiChatSession.SceneSession sceneSession,
                                                 AgentStorySceneBaseVO sceneVo, MultiChatMsgCompress compressItem,
                                                 Long finalSeqNum) {
        // 保留原有的分批处理逻辑，但添加内存监控
        String existingSummary = compressItem.getSummary();
        StringBuilder cumulativeSummary = new StringBuilder();

        if (existingSummary != null && !existingSummary.trim().isEmpty()) {
            cumulativeSummary.append(existingSummary).append("\n\n");
        }

        // 计算分批参数
        int totalMessages = allMessages.size();
        int batchCount = (int) Math.ceil((double) totalMessages / maxMessagesPerBatch);

        log.info("开始分批压缩场景 {} 的 {} 条消息，分为 {} 批，每批最多 {} 条，重叠 {} 条",
                sceneVo.getSceneName(), totalMessages, batchCount, maxMessagesPerBatch, batchOverlapSize);

        boolean hasSuccess = false;
        for (int batchIndex = 0; batchIndex < batchCount; batchIndex++) {
            try {
                // 检查内存使用情况
                checkMemoryDuringProcessing();

                // 计算当前批次的消息范围
                int startIndex = batchIndex * maxMessagesPerBatch;
                if (batchIndex > 0) {
                    // 从第二批开始，向前重叠一些消息以保持上下文
                    startIndex = Math.max(0, startIndex - batchOverlapSize);
                }

                int endIndex = Math.min(totalMessages, (batchIndex + 1) * maxMessagesPerBatch);
                List<MultiChatMsg> batchMessages = allMessages.subList(startIndex, endIndex);

                log.info("处理第 {}/{} 批消息，范围: {} - {}，消息数: {}",
                        batchIndex + 1, batchCount, startIndex, endIndex - 1, batchMessages.size());

                // 构建当前批次的历史内容
                String batchHistoryContent = buildHistoryContentOptimized(batchMessages, story);

                // 如果有累积摘要，添加到当前批次内容前面
                if (cumulativeSummary.length() > 0) {
                    batchHistoryContent = "【前期摘要】\n" + cumulativeSummary.toString() +
                            "\n【当前对话】\n" + batchHistoryContent;
                }

                // 执行AI压缩 - 使用分批压缩的提示词
                boolean isFirstBatch = (batchIndex == 0);
                boolean hasPreviousSummary = (cumulativeSummary.length() > 0);
                String batchSummary = performAICompressionWithPrompt(story, sceneVo, batchHistoryContent,
                        isFirstBatch, hasPreviousSummary, batchIndex + 1, batchCount);

                if (batchSummary != null && !batchSummary.trim().isEmpty()) {
                    // 更新累积摘要
                    cumulativeSummary.setLength(0); // 清空之前的内容
                    cumulativeSummary.append(batchSummary);
                    hasSuccess = true;

                    log.info("第 {}/{} 批压缩完成，摘要长度: {} 字符",
                            batchIndex + 1, batchCount, batchSummary.length());
                } else {
                    log.warn("第 {}/{} 批压缩失败，AI返回空结果", batchIndex + 1, batchCount);
                }

                // 更新统计
                batchedMessagesTotal.addAndGet(batchMessages.size());
                splitBatchesTotal.incrementAndGet();

            } catch (Exception e) {
                log.error("处理第 {}/{} 批消息时发生异常", batchIndex + 1, batchCount, e);
                // 继续处理下一批，不中断整个流程
            }
        }

        // 保存最终的累积摘要
        if (hasSuccess && cumulativeSummary.length() > 0) {
            saveCompressedResult(compressItem, cumulativeSummary.toString(), finalSeqNum, session, sceneSession);

            log.info("场景 {} 分批压缩完成，总消息数: {}，最终摘要长度: {} 字符",
                    sceneVo.getSceneName(), totalMessages, cumulativeSummary.length());
            return true;
        } else {
            log.warn("场景 {} 分批压缩失败，未生成有效摘要", sceneVo.getSceneName());
            return false;
        }
    }

    /**
     * 获取任务性能统计信息（参照chat-service，包含内存统计）
     */
    public String getPerformanceStats() {
        Runtime runtime = Runtime.getRuntime();
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        long usedMemory = totalMemory - freeMemory;
        long maxMemory = runtime.maxMemory();
        double memoryUsagePercent = (double) usedMemory / maxMemory * 100;

        return String.format(
            "历史记录压缩任务性能统计 - 总处理: %d, 总跳过: %d, 总错误: %d, 内存错误: %d, 活跃线程: %d, " +
            "运行状态: %s, 当前内存使用率: %.2f%%, 已用内存: %dMB, 最大内存: %dMB, " +
            "处理会话总数: %d, 压缩场景总数: %d, 分批处理消息总数: %d, 分割批次总数: %d",
            totalProcessed.get(),
            totalSkipped.get(),
            totalErrors.get(),
            totalMemoryErrors.get(),
            activeThreads.get(),
            isRunning.get() ? "运行中" : "空闲",
            memoryUsagePercent,
            usedMemory / 1024 / 1024,
            maxMemory / 1024 / 1024,
            processedSessionsTotal.get(),
            compressedScenesTotal.get(),
            batchedMessagesTotal.get(),
            splitBatchesTotal.get()
        );
    }

    /**
     * 重置性能统计（参照chat-service）
     */
    public void resetStats() {
        totalProcessed.set(0);
        totalSkipped.set(0);
        totalErrors.set(0);
        totalMemoryErrors.set(0);
        processedSessionsTotal.set(0);
        compressedScenesTotal.set(0);
        failedTasksTotal.set(0);
        retryableFailuresTotal.set(0);
        nonRetryableFailuresTotal.set(0);
        batchedMessagesTotal.set(0);
        splitBatchesTotal.set(0);
        log.info("历史记录压缩任务性能统计已重置");
    }

    /**
     * 检查任务健康状态（参照chat-service）
     */
    public boolean isHealthy() {
        // 检查线程池状态
        if (scheduledExecutor != null && scheduledExecutor.isShutdown()) {
            return false;
        }
        if (compressionExecutor != null && compressionExecutor.isShutdown()) {
            return false;
        }

        // 检查错误率（如果错误率超过50%认为不健康）
        long total = totalProcessed.get() + totalSkipped.get() + totalErrors.get();
        return total <= 100 || totalErrors.get() * 2 <= total;
    }

    /**
     * 手动触发压缩任务（参照chat-service，用于测试或紧急处理）
     */
    public void triggerManualCompression() {
        if (!compressConfig.isEnabled()) {
            log.warn("历史记录压缩任务已禁用，无法手动触发");
            return;
        }

        if (isRunning.get()) {
            log.warn("历史记录压缩任务正在运行中，无法重复触发");
            return;
        }

        log.info("手动触发历史记录压缩任务");
        scheduledExecutor.submit(this::executeCompressTaskSafely);
    }

    /**
     * 获取任务监控指标（保留原有方法）
     */
    public String getMetrics() {
        if (!compressConfig.isMetricsEnabled()) {
            return "监控指标已禁用";
        }

        return String.format(
                "压缩任务监控指标 - 处理会话总数: %d, 压缩场景总数: %d, 失败任务总数: %d, 当前运行任务数: %d, 可重试失败: %d, 不可重试失败: %d, 分批处理消息总数: %d, 分割批次总数: %d",
                processedSessionsTotal.get(),
                compressedScenesTotal.get(),
                failedTasksTotal.get(),
                currentRunningTasks.get(),
                retryableFailuresTotal.get(),
                nonRetryableFailuresTotal.get(),
                batchedMessagesTotal.get(),
                splitBatchesTotal.get()
        );
    }
}
