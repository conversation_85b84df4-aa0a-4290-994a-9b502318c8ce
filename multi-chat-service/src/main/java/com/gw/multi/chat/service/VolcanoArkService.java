package com.gw.multi.chat.service;

import com.gw.multi.chat.dto.CreateRemoteSessionParamsDTO;
import com.gw.multi.chat.dto.MessageContentDTO;
import com.gw.multi.chat.vo.AIResponseVO;
import com.gw.multi.chat.vo.ChatContextVO;
import lombok.Getter;

import java.util.List;

/**
 * 火山大模型服务接口
 * <p>
 * 支持多智能体群聊的AI对话功能
 */
public interface VolcanoArkService {


     String createConversation(List<MessageContentDTO> messages,Integer ttl);


    /**
     * 发送聊天消息（带回调）
     *
     * @param conversationId 会话ID
     * @param message 消息列表
     * @param systemMessages 系统消息列表
     * @param callback ResourceNotFound错误回调
     * @return AI响应
     * @throws Exception 异常
     */
    AIResponseVO sendChatMessage(String conversationId, List<ChatContextVO> message, List<MessageContentDTO> systemMessages,
                                 ResourceNotFoundCallback callback) throws Exception;

    AIResponseVO sendChatMessageTraditional(List<ChatContextVO> message) throws Exception;
    String sendCompressChatMessage(List<ChatContextVO> message) throws Exception;
    /**
     * ResourceNotFound错误回调接口
     */
    interface ResourceNotFoundCallback {
        /**
         * 当遇到ResourceNotFound错误时的回调
         *
         * @param conversationId 会话ID
         * @param systemContext 当前系统上下文
         * @param errorDetails 错误详情
         * @return 创建远程会话的参数
         */
        CreateRemoteSessionParamsDTO onResourceNotFound(String conversationId, SystemContext systemContext, String errorDetails);

        /**
         * 当遇到内容过长错误时的回调
         *
         * @param conversationId 会话ID
         * @param systemContext 当前系统上下文
         * @param errorDetails 错误详情
         * @return 创建远程会话的参数
         */
        CreateRemoteSessionParamsDTO onContentTooLong(String conversationId, SystemContext systemContext, String errorDetails);
    }

    /**
     * 系统上下文信息
     */
    @Getter
    class SystemContext {
        private final String contextCacheId;
        private final List<MessageContentDTO> systemMessages;
        private final List<ChatContextVO> conversationHistory;
        private final long timestamp;
        private final boolean contextCacheAvailable;

        public SystemContext(String contextCacheId, List<MessageContentDTO> systemMessages,
                           List<ChatContextVO> conversationHistory, boolean contextCacheAvailable) {
            this.contextCacheId = contextCacheId;
            this.systemMessages = systemMessages;
            this.conversationHistory = conversationHistory;
            this.timestamp = System.currentTimeMillis();
            this.contextCacheAvailable = contextCacheAvailable;
        }
    }
}