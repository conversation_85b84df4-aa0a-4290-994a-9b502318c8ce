package com.gw.multi.chat.websocket.manager;

import com.gw.common.agent.constant.AgentCommonCacheConstant;
import com.gw.common.agent.constant.StoryConstant;
import com.gw.common.agent.service.AgentProxyService;
import com.gw.common.agent.service.AgentStoryProxyService;
import com.gw.common.agent.vo.AgentBaseVO;
import com.gw.common.agent.vo.AgentStoryBaseVO;
import com.gw.common.agent.vo.AgentStorySceneBaseVO;
import com.gw.common.agent.vo.SceneAgentRelationBaseVO;
import com.gw.multi.chat.config.CacheProperties;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 智能体和故事缓存管理器
 * 专门负责智能体、故事、场景等数据的缓存管理
 */
@Component("multiChatCacheManager")  // 👈 避免Bean名称冲突
@Log4j2
public class AgentStoryCacheManager {

    private final AgentProxyService agentProxyService;
    private final AgentStoryProxyService agentStoryProxyService;
    private final CacheProperties cacheProperties;

    // 故事和智能体缓存
    private final Map<String, AgentStoryCache> userStoryCache = new ConcurrentHashMap<>();
    private final Map<String, Long> userCacheExpireTime = new ConcurrentHashMap<>();

    // 缓存清理配置
    @Value("${multi-chat.websocket.cache-expire-after-disconnect-seconds:10}")
    private int cacheExpireAfterDisconnectSeconds;

    private ScheduledExecutorService cacheCleanupExecutor;

    public AgentStoryCacheManager(AgentProxyService agentProxyService,
                                  AgentStoryProxyService agentStoryProxyService,
                                  CacheProperties cacheProperties) {
        this.agentProxyService = agentProxyService;
        this.agentStoryProxyService = agentStoryProxyService;
        this.cacheProperties = cacheProperties;
    }

    /**
     * 初始化缓存管理器
     */
    public void init() {
        this.cacheCleanupExecutor = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread t = new Thread(r, "agent-story-cache-cleanup");
            t.setDaemon(true);
            return t;
        });

        // 启动定期缓存清理任务
        cacheCleanupExecutor.scheduleWithFixedDelay(
                this::performCacheCleanup,
                5, // 5分钟后开始
                5, // 每5分钟执行一次
                TimeUnit.MINUTES
        );

        log.info("智能体故事缓存管理器初始化完成");
    }

    /**
     * 获取或创建故事和智能体缓存
     */
    public AgentStoryCache getOrCreateStoryCache(String username, Long storyId, Long sceneId) {
        String cacheKey = buildCacheKey(username, storyId, sceneId);

        return userStoryCache.computeIfAbsent(cacheKey, key -> {
            try {
                return loadStoryAndAgentData(username, storyId, sceneId);
            } catch (Exception e) {
                log.error("加载故事和智能体数据失败: username={}, storyId={}, sceneId={}",
                        username, storyId, sceneId, e);
                return null;
            }
        });
    }

    /**
     * 验证故事和场景是否有效
     */
    public ValidationResult validateStoryAndScene(String username, Long storyId, Long sceneId) {
        AgentStoryCache cache = getOrCreateStoryCache(username, storyId, sceneId);

        if (cache == null || !cache.isValid()) {
            return ValidationResult.invalid(determineErrorMessage(username, storyId, sceneId));
        }

        return ValidationResult.valid(cache.storyVo(), cache.sceneVo());
    }

    /**
     * 获取智能体信息
     */
    public AgentInfo getAgentInfo(String username, Long storyId, Long sceneId) {
        String cacheKey = buildCacheKey(username, storyId, sceneId);
        AgentStoryCache cache = userStoryCache.get(cacheKey);

        if (cache != null && cache.isValid()) {
            List<Long> agentIds = cache.sceneVo().getAgentRelations().stream()
                    .map(SceneAgentRelationBaseVO::getAgentId)
                    .collect(Collectors.toList());
            return new AgentInfo(agentIds, cache.agentMap());
        }

        log.warn("用户 {} 的智能体缓存不存在: storyId={}, sceneId={}", username, storyId, sceneId);
        return new AgentInfo(Collections.emptyList(), Collections.emptyMap());
    }

    /**
     * 延迟清理用户缓存
     */
    public void scheduleUserCacheCleanup(String username) {
        if (cacheCleanupExecutor == null || cacheCleanupExecutor.isShutdown()) {
            return;
        }

        long expireTime = System.currentTimeMillis() + (cacheExpireAfterDisconnectSeconds * 1000L);
        userCacheExpireTime.put(username, expireTime);

        cacheCleanupExecutor.schedule(() -> {
            try {
                Long scheduledExpireTime = userCacheExpireTime.get(username);
                if (scheduledExpireTime != null && System.currentTimeMillis() >= scheduledExpireTime) {
                    cleanupUserCaches(username);
                    log.info("延迟清理用户缓存完成: {}", username);
                }
            } catch (Exception e) {
                log.error("延迟清理用户缓存失败: {}", username, e);
            }
        }, cacheExpireAfterDisconnectSeconds, TimeUnit.SECONDS);

        log.debug("已安排 {} 秒后清理用户 {} 的缓存", cacheExpireAfterDisconnectSeconds, username);
    }

    /**
     * 立即清理用户缓存
     */
    public void cleanupUserCaches(String username) {
        userStoryCache.entrySet().removeIf(entry -> entry.getKey().startsWith(username + "_"));
        userCacheExpireTime.remove(username);
        log.info("清理用户 {} 的所有缓存完成", username);
    }

    /**
     * 获取缓存统计信息
     */
    public CacheStatistics getStatistics() {
        return new CacheStatistics(
                userStoryCache.size(),
                userCacheExpireTime.size()
        );
    }

    /**
     * 清理所有缓存
     */
    public void cleanup() {
        log.info("开始清理智能体故事缓存管理器");

        if (cacheCleanupExecutor != null && !cacheCleanupExecutor.isShutdown()) {
            cacheCleanupExecutor.shutdown();
            try {
                if (!cacheCleanupExecutor.awaitTermination(10, TimeUnit.SECONDS)) {
                    cacheCleanupExecutor.shutdownNow();
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                cacheCleanupExecutor.shutdownNow();
            }
        }

        userStoryCache.clear();
        userCacheExpireTime.clear();

        log.info("智能体故事缓存管理器清理完成");
    }

    // ========== 私有方法 ==========

    private String buildCacheKey(String username, Long storyId, Long sceneId) {
        return username + "_" + storyId + "_" + sceneId;
    }

    private AgentStoryCache loadStoryAndAgentData(String username, Long storyId, Long sceneId) {
        // 从远程服务获取故事信息
        String storyCacheKey = cacheProperties.getCacheName(AgentCommonCacheConstant.AGENT_BASE_STORY_MAP_CACHE_KEY);
        AgentStoryBaseVO storyVo = agentStoryProxyService.getStoryInfo(storyCacheKey, storyId);

        if (storyVo == null) {
            log.error("故事校验失败: 未找到故事信息, 用户: {}, 故事ID: {}", username, storyId);
            return null;
        }

        if (storyVo.getShelfStatus() != StoryConstant.SHELF_ON_STATUS) {
            log.error("故事校验失败: 故事未上架, 用户: {}, 故事ID: {}, 上架状态: {}",
                    username, storyId, storyVo.getShelfStatus());
            return null;
        }

        if (storyVo.getStatus() != StoryConstant.STORY_STATUS_PUBLISHED) {
            log.error("故事校验失败: 故事未发布, 用户: {}, 故事ID: {}, 发布状态: {}",
                    username, storyId, storyVo.getStatus());
            return null;
        }

        // 获取场景信息
        String sceneCacheKey = cacheProperties.getCacheName(AgentCommonCacheConstant.AGENT_BASE_STORY_SCENE_KEY);
        AgentStorySceneBaseVO sceneVo = agentStoryProxyService.getStorySceneInfo(sceneId, sceneCacheKey);

        if (sceneVo == null) {
            log.error("剧情章节校验失败: 未找到剧情章节信息, 用户: {}, 故事ID: {}, 剧情章节ID: {}",
                    username, storyId, sceneId);
            return null;
        }

        // 获取智能体信息
        List<Long> agentIds = sceneVo.getAgentRelations().stream()
                .map(SceneAgentRelationBaseVO::getAgentId)
                .collect(Collectors.toList());

        String agentCacheKey = cacheProperties.getCacheName(AgentCommonCacheConstant.AGENT_BASE_MAP_CACHE_KEY);
        Map<Long, AgentBaseVO> agentMap = agentProxyService.getAgentsByIds(agentCacheKey, agentIds);

        // 设置智能体名称
        sceneVo.getAgentRelations().forEach(relation -> {
            AgentBaseVO agent = agentMap.get(relation.getAgentId());
            if (agent != null) {
                relation.setAgentName(agent.getName());
            }
        });

        log.info("为用户 {} 创建故事缓存: storyId={}, sceneId={}", username, storyId, sceneId);
        return AgentStoryCache.create(storyVo, sceneVo, agentMap);
    }

    private String determineErrorMessage(String username, Long storyId, Long sceneId) {
        try {
            String storyCacheKey = cacheProperties.getCacheName(AgentCommonCacheConstant.AGENT_BASE_STORY_MAP_CACHE_KEY);
            AgentStoryBaseVO storyVo = agentStoryProxyService.getStoryInfo(storyCacheKey, storyId);

            if (storyVo == null) {
                return "故事不存在或已被删除";
            } else if (storyVo.getShelfStatus() != StoryConstant.SHELF_ON_STATUS) {
                return "故事已下架，暂时无法使用";
            } else if (storyVo.getStatus() != StoryConstant.STORY_STATUS_PUBLISHED) {
                return "故事尚未发布，暂时无法使用";
            } else {
                String sceneCacheKey = cacheProperties.getCacheName(AgentCommonCacheConstant.AGENT_BASE_STORY_SCENE_KEY);
                AgentStorySceneBaseVO sceneVo = agentStoryProxyService.getStorySceneInfo(sceneId, sceneCacheKey);
                if (sceneVo == null) {
                    return "剧情章节不存在或已被删除";
                } else {
                    return "故事或剧情章节配置异常，请稍后重试";
                }
            }
        } catch (Exception e) {
            log.error("获取详细错误信息失败: 用户: {}, 故事ID: {}, 剧情章节ID: {}", username, storyId, sceneId, e);
            return "系统异常，请稍后重试";
        }
    }

    private void performCacheCleanup() {
        try {
            long currentTime = System.currentTimeMillis();
            int cleanedCount = 0;

            List<String> expiredKeys = new ArrayList<>();
            for (Map.Entry<String, Long> entry : userCacheExpireTime.entrySet()) {
                String cacheKey = entry.getKey();
                Long expireTime = entry.getValue();

                if (expireTime != null && currentTime >= expireTime) {
                    expiredKeys.add(cacheKey);
                }
            }

            for (String key : expiredKeys) {
                userStoryCache.remove(key);
                userCacheExpireTime.remove(key);
                cleanedCount++;
            }

            if (cleanedCount > 0) {
                log.debug("清理了 {} 个过期的故事缓存", cleanedCount);
            }
        } catch (Exception e) {
            log.warn("清理过期故事缓存时发生异常", e);
        }
    }

    // ========== 内部类和记录 ==========

    /**
     * 验证结果
     */
    public record ValidationResult(boolean valid, AgentStoryBaseVO storyVo,
                                   AgentStorySceneBaseVO sceneVo, String errorMessage) {

        public static ValidationResult valid(AgentStoryBaseVO storyVo, AgentStorySceneBaseVO sceneVo) {
            return new ValidationResult(true, storyVo, sceneVo, null);
        }

        public static ValidationResult invalid(String errorMessage) {
            return new ValidationResult(false, null, null, errorMessage);
        }
    }

    /**
     * 智能体信息
     */
    public record AgentInfo(List<Long> agentIds, Map<Long, AgentBaseVO> agentMap) {
    }

    /**
     * 智能体故事缓存
     */
    public record AgentStoryCache(AgentStoryBaseVO storyVo, AgentStorySceneBaseVO sceneVo,
                                  Map<Long, AgentBaseVO> agentMap, long createTime) {

        public static AgentStoryCache create(AgentStoryBaseVO storyVo, AgentStorySceneBaseVO sceneVo,
                                             Map<Long, AgentBaseVO> agentMap) {
            return new AgentStoryCache(storyVo, sceneVo, agentMap, System.currentTimeMillis());
        }

        public boolean isValid() {
            return storyVo != null && sceneVo != null;
        }
    }

    /**
     * 缓存统计信息
     */
    public record CacheStatistics(int storyCacheSize, int expireTimeSize) {
    }
} 