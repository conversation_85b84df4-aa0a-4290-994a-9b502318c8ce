package com.gw.multi.chat.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.gw.multi.chat.entity.MultiChatMsg;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 多智能体群聊WebSocket消息DTO
 * 优化设计，支持多种消息类型和智能体交互
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MultiChatWebSocketChatAckMsg {

    /**
     * 消息ID（由服务端生成）
     */
    private String id;
    /**
     * 消息类型
     */
    private String contentType = "text";
    /**
     * 故事ID
     */
    private Long storyId;
    /**
     * 场景ID
     */
    private Long sceneId;
    /**
     * 会话ID
     */

    private List<Long> agentIds;

    private String role;
    /**
     * 消息内容
     */
    private String content;
    /**
     * 消息状态
     */
    private String status;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;


    public static MultiChatWebSocketChatAckMsg fromEntity(MultiChatMsg entity, List<Long> agentIds) {
        return MultiChatWebSocketChatAckMsg.builder()
                .id(entity.getId())
                .contentType("text")
                .storyId(entity.getStoryId())
                .sceneId(entity.getSceneId())
                .status(entity.getStatus())
                .content(entity.getContent())
                .createdAt(entity.getCreateTime())
                .agentIds(agentIds)
                .role(entity.getRole())
                .build();
    }

}