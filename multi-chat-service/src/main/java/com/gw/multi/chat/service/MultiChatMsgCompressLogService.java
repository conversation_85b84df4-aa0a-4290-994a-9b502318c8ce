package com.gw.multi.chat.service;

import com.gw.multi.chat.entity.MultiChatMsgCompressLog;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 多智能体群聊消息摘要服务
 * <p>
 * 负责消息摘要的持久化、查询和管理
 */
public interface MultiChatMsgCompressLogService {

    /**
     * 保存消息摘要
     *
     * @param messageSummary 消息摘要对象
     * @return 保存后的消息摘要
     */
    MultiChatMsgCompressLog saveSummary(MultiChatMsgCompressLog messageSummary);

    /**
     * 批量保存消息摘要
     *
     * @param messageSummaries 消息摘要列表
     * @return 保存后的消息摘要列表
     */
    List<MultiChatMsgCompressLog> saveSummaries(List<MultiChatMsgCompressLog> messageSummaries);

    /**
     * 根据故事会话ID查询消息摘要
     *
     * @param storyId        故事ID
     * @param storySessionId 故事会话ID
     * @param pageable       分页参数
     * @return 消息摘要分页结果
     */
    Page<MultiChatMsgCompressLog> findByStorySessionId(Long storyId, String storySessionId, Pageable pageable);

    /**
     * 根据场景会话ID查询消息摘要
     *
     * @param storyId        故事ID
     * @param sceneSessionId 场景会话ID
     * @param pageable       分页参数
     * @return 消息摘要分页结果
     */
    Page<MultiChatMsgCompressLog> findBySceneSessionId(Long storyId, String sceneSessionId, Pageable pageable);

    Page<MultiChatMsgCompressLog> findBySceneSessionId(Long storyId, Long sceneSessionId, Pageable pageable);

    /**
     * 根据故事ID查询消息摘要
     *
     * @param storyId  故事ID
     * @param pageable 分页参数
     * @return 消息摘要分页结果
     */
    Page<MultiChatMsgCompressLog> findByStoryId(Long storyId, Pageable pageable);

    /**
     * 根据场景ID查询消息摘要
     *
     * @param storyId  故事ID
     * @param sceneId  场景ID
     * @param pageable 分页参数
     * @return 消息摘要分页结果
     */
    Page<MultiChatMsgCompressLog> findBySceneId(Long storyId, Long sceneId, Pageable pageable);

    /**
     * 根据用户名查询消息摘要
     *
     * @param storyId  故事ID
     * @param username 用户名
     * @param pageable 分页参数
     * @return 消息摘要分页结果
     */
    Page<MultiChatMsgCompressLog> findByUsername(Long storyId, String username, Pageable pageable);

    /**
     * 根据故事ID和用户名查询消息摘要
     *
     * @param storyId  故事ID
     * @param username 用户名
     * @param pageable 分页参数
     * @return 消息摘要分页结果
     */
    Page<MultiChatMsgCompressLog> findByStoryIdAndUsername(Long storyId, String username, Pageable pageable);

    /**
     * 根据序列号范围查询消息摘要
     *
     * @param storyId        故事ID
     * @param storySessionId 故事会话ID
     * @param startSequence  开始序列号
     * @param endSequence    结束序列号
     * @return 消息摘要列表
     */
    List<MultiChatMsgCompressLog> findBySequenceRange(Long storyId, String storySessionId, Long startSequence, Long endSequence);

    /**
     * 根据多个条件查询消息摘要
     *
     * @param storyId        故事ID（可选）
     * @param sceneId        场景ID（可选）
     * @param username       用户名（可选）
     * @param storySessionId 故事会话ID（可选）
     * @param sceneSessionId 场景会话ID（可选）
     * @param pageable       分页参数
     * @return 消息摘要分页结果
     */
    Page<MultiChatMsgCompressLog> findByConditions(Long storyId, Long sceneId, String username,
                                                   String storySessionId, String sceneSessionId, Pageable pageable);

    /**
     * 根据故事会话ID统计消息摘要数量
     *
     * @param storyId        故事ID
     * @param storySessionId 故事会话ID
     * @return 消息摘要总数
     */
    long MultiChatMessageCompressLog(Long storyId, String storySessionId);

    /**
     * 根据多个条件统计消息摘要数量
     *
     * @param storyId        故事ID（可选）
     * @param sceneId        场景ID（可选）
     * @param username       用户名（可选）
     * @param storySessionId 故事会话ID（可选）
     * @param sceneSessionId 场景会话ID（可选）
     * @return 消息摘要总数
     */
    long countByConditions(Long storyId, Long sceneId, String username, String storySessionId, String sceneSessionId);

    /**
     * 删除指定时间之前的消息摘要
     *
     * @param storyId        故事ID
     * @param storySessionId 故事会话ID
     * @param beforeTime     时间阈值
     * @return 删除的消息摘要数量
     */
    long deleteSummariesBefore(Long storyId, String storySessionId, LocalDateTime beforeTime);

    /**
     * 根据消息摘要ID查询消息摘要
     *
     * @param storyId   故事ID
     * @param summaryId 消息摘要ID
     * @return 消息摘要对象
     */
    MultiChatMsgCompressLog findById(Long storyId, String summaryId);

    /**
     * 更新消息摘要
     *
     * @param storyId             故事ID
     * @param summaryId           消息摘要ID
     * @param summary             摘要内容
     * @param relationshipSummary 关系摘要
     * @param characterSummary    角色摘要
     */
    void updateSummary(Long storyId, String summaryId, String summary, String relationshipSummary, String characterSummary);

    /**
     * 删除消息摘要
     *
     * @param storyId 故事ID
     * @param logId   消息摘要ID
     * @return 是否删除成功
     */
    boolean deleteSummary(Long storyId, String logId);

    /**
     * 获取集合名称（根据故事ID分表）
     *
     * @param storyId 故事ID
     * @return 集合名称
     */
    String getCollectionName(Long storyId);
}