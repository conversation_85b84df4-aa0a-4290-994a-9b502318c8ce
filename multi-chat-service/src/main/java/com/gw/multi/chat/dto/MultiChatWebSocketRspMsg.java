package com.gw.multi.chat.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.gw.multi.chat.entity.MultiChatMsg;
import com.gw.multi.chat.util.ChatUtils;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 多智能体群聊WebSocket消息DTO
 * 优化设计，支持多种消息类型和智能体交互
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MultiChatWebSocketRspMsg {

    /**
     * 消息ID（由服务端生成）
     */
    private String id;
    /**
     * 消息类型
     */
    private String contentType = "text";
    /**
     * 故事ID
     */
    private Long storyId;
    /**
     * 场景ID
     */
    private Long sceneId;
    /**
     * 会话ID
     */
    private String sessionId;
    private Long agentId;
    private String agentName;
    private String role;
    /**
     * 消息内容
     */
    private String content;
    /**
     * 消息状态
     */
    private String status;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;


    public static MultiChatWebSocketRspMsg fromEntity(MultiChatMsg entity) {
        // 移除content中的心理内容（括号内容）
        String cleanContent = ChatUtils.removeBracketContent(entity.getContent());

        return MultiChatWebSocketRspMsg.builder()
                .id(entity.getId())
                .contentType("text")
                .storyId(entity.getStoryId())
                .sceneId(entity.getSceneId())
                .status(entity.getStatus())
                .sessionId(entity.getStorySessionId())
                .content(cleanContent)
                .createdAt(entity.getCreateTime())
                .agentId(entity.getAgentId())
                .agentName(entity.getAgentName())
                .role(entity.getRole())
                .build();
    }

}