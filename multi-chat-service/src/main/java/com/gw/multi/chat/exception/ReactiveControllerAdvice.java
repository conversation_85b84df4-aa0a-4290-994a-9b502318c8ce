package com.gw.multi.chat.exception;

import com.gw.common.dto.ResponseResult;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

/**
 * 响应式控制器异常处理
 * 专门用于处理WebFlux环境中的异常
 */
@RestControllerAdvice
public class ReactiveControllerAdvice {

    /**
     * 处理所有异常
     */
    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.OK)
    public Mono<ResponseResult<?>> handleException(Exception e, ServerWebExchange exchange) {
        logError(exchange, e);
        return Mono.just(ResponseResult.failure(500, "系统异常，请稍后重试"));
    }

    /**
     * 记录错误日志
     */
    private void logError(ServerWebExchange exchange, Exception e) {
        String requestURI = exchange.getRequest().getURI().getPath();
        String method = exchange.getRequest().getMethod().name();
        System.err.println("请求异常 - " + method + " " + requestURI + " - 异常类型: " + e.getClass().getSimpleName());
        System.err.println("异常消息: " + e.getMessage());
        e.printStackTrace();
    }
} 