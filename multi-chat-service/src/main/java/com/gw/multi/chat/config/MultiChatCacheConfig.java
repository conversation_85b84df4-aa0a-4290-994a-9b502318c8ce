package com.gw.multi.chat.config;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.gw.multi.chat.websocket.MultiChatSessionMetadata;
import lombok.Data;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.time.Duration;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.locks.StampedLock;

/**
 * 多智能体群聊缓存配置
 * 使用Caffeine框架提供高性能的本地缓存
 */
@Configuration
@Log4j2
public class MultiChatCacheConfig {

    @Value("${multi-chat.cache.session-max-size:1000}")
    private int sessionCacheMaxSize;

    @Value("${multi-chat.cache.session-expire-minutes:120}")
    private int sessionCacheExpireMinutes;

    @Value("${multi-chat.cache.lock-max-size:1000}")
    private int lockCacheMaxSize;

    @Value("${multi-chat.cache.lock-expire-minutes:120}")
    private int lockCacheExpireMinutes;

    @Value("${multi-chat.cache.active-time-max-size:1000}")
    private int activeTimeCacheMaxSize;

    @Value("${multi-chat.cache.active-time-expire-minutes:120}")
    private int activeTimeCacheExpireMinutes;

    @Value("${multi-chat.cache.stats-max-size:1000}")
    private int statsCacheMaxSize;

    @Value("${multi-chat.cache.stats-expire-minutes:120}")
    private int statsCacheExpireMinutes;

    @Value("${multi-chat.cache.story-max-size:200}")
    private int storyCacheMaxSize;

    @Value("${multi-chat.cache.story-expire-minutes:60}")
    private int storyCacheExpireMinutes;

    @Value("${multi-chat.cache.scene-max-size:500}")
    private int sceneCacheMaxSize;

    @Value("${multi-chat.cache.scene-expire-minutes:60}")
    private int sceneCacheExpireMinutes;

    @Value("${multi-chat.cache.agent-context-max-size:2000}")
    private int agentContextCacheMaxSize;

    @Value("${multi-chat.cache.agent-context-expire-minutes:60}")
    private int agentContextCacheExpireMinutes;

    /**
     * 生成会话缓存键
     */
    public static String generateSessionCacheKey(String username, Long storyId) {
        return username + "_" + storyId;
    }

    /**
     * 生成智能体上下文缓存键
     */
    public static String generateAgentContextCacheKey(String sessionId, Long agentId) {
        return sessionId + "_" + agentId;
    }

    /**
     * 生成群聊参与者缓存键
     */
    public static String generateParticipantsCacheKey(Long storyId, Long sceneId) {
        return "story_" + storyId + "_scene_" + sceneId;
    }

    /**
     * 多智能体会话缓存
     * 存储会话密钥到会话元数据的映射
     * 密钥格式：{username}_{storyId}
     */
    @Bean(name = "multiChatSessionCache")
    public Cache<String, MultiChatSessionMetadata> multiChatSessionCache() {
        return Caffeine.newBuilder()
                .maximumSize(sessionCacheMaxSize)
                .expireAfterAccess(Duration.ofMinutes(sessionCacheExpireMinutes))
                .recordStats()
                .build();
    }

    /**
     * 多智能体会话锁缓存
     * 存储会话ID到StampedLock的映射
     */
    @Bean(name = "multiChatSessionLockCache")
    public Cache<String, StampedLock> multiChatSessionLockCache() {
        return Caffeine.newBuilder()
                .maximumSize(lockCacheMaxSize)
                .expireAfterAccess(Duration.ofMinutes(lockCacheExpireMinutes))
                .recordStats()
                .build();
    }

    /**
     * 用户活跃时间缓存
     * 存储用户名到最后活跃时间的映射
     */
    @Bean(name = "multiChatUserActiveTimeCache")
    public Cache<String, Long> multiChatUserActiveTimeCache() {
        return Caffeine.newBuilder()
                .maximumSize(activeTimeCacheMaxSize)
                .expireAfterAccess(Duration.ofMinutes(activeTimeCacheExpireMinutes))
                .recordStats()
                .build();
    }

    /**
     * 会话统计信息缓存
     * 存储会话ID到会话统计信息的映射
     */
    @Bean(name = "multiChatSessionStatsCache")
    public Cache<String, MultiChatSessionStats> multiChatSessionStatsCache() {
        return Caffeine.newBuilder()
                .maximumSize(statsCacheMaxSize)
                .expireAfterAccess(Duration.ofMinutes(statsCacheExpireMinutes))
                .recordStats()
                .build();
    }

    /**
     * 故事信息缓存
     * 存储故事ID到故事信息的映射
     */
    @Bean(name = "multiChatStoryCache")
    public Cache<Long, Object> multiChatStoryCache() {
        return Caffeine.newBuilder()
                .maximumSize(storyCacheMaxSize)
                .expireAfterWrite(Duration.ofMinutes(storyCacheExpireMinutes))
                .recordStats()
                .build();
    }

    /**
     * 场景信息缓存
     * 存储场景ID到场景信息的映射
     */
    @Bean(name = "multiChatSceneCache")
    public Cache<Long, Object> multiChatSceneCache() {
        return Caffeine.newBuilder()
                .maximumSize(sceneCacheMaxSize)
                .expireAfterWrite(Duration.ofMinutes(sceneCacheExpireMinutes))
                .recordStats()
                .build();
    }

    /**
     * 智能体上下文缓存
     * 存储智能体上下文密钥到上下文信息的映射
     * 密钥格式：{sessionId}_{agentId}
     */
    @Bean(name = "multiChatAgentContextCache")
    public Cache<String, Object> multiChatAgentContextCache() {
        return Caffeine.newBuilder()
                .maximumSize(agentContextCacheMaxSize)
                .expireAfterAccess(Duration.ofMinutes(agentContextCacheExpireMinutes))
                .recordStats()
                .build();
    }

    /**
     * 故事智能体映射缓存
     * 存储故事ID到智能体ID列表的映射
     */
    @Bean(name = "multiChatStoryAgentsCache")
    public Cache<Long, java.util.List<Long>> multiChatStoryAgentsCache() {
        return Caffeine.newBuilder()
                .maximumSize(storyCacheMaxSize)
                .expireAfterWrite(Duration.ofMinutes(storyCacheExpireMinutes))
                .recordStats()
                .build();
    }

    /**
     * 场景智能体映射缓存
     * 存储场景ID到智能体ID列表的映射
     */
    @Bean(name = "multiChatSceneAgentsCache")
    public Cache<Long, java.util.List<Long>> multiChatSceneAgentsCache() {
        return Caffeine.newBuilder()
                .maximumSize(sceneCacheMaxSize)
                .expireAfterWrite(Duration.ofMinutes(sceneCacheExpireMinutes))
                .recordStats()
                .build();
    }

    /**
     * 群聊参与者缓存
     * 存储群聊会话ID到参与者列表的映射
     */
    @Bean(name = "multiChatParticipantsCache")
    public Cache<String, java.util.Set<String>> multiChatParticipantsCache() {
        return Caffeine.newBuilder()
                .maximumSize(sessionCacheMaxSize)
                .expireAfterAccess(Duration.ofMinutes(sessionCacheExpireMinutes))
                .recordStats()
                .build();
    }

    /**
     * 多智能体会话统计信息类
     */
    @Data
    public static class MultiChatSessionStats {
        private final String sessionId;
        private final String username;
        private final Long storyId;
        private final Long sceneId;
        private final long creationTime;
        private final AtomicInteger messageCount = new AtomicInteger(0);
        private final AtomicInteger agentResponseCount = new AtomicInteger(0);
        private final AtomicInteger sceneChangeCount = new AtomicInteger(0);
        private final ConcurrentHashMap<Long, Integer> agentSpeakCounts = new ConcurrentHashMap<>();
        private long lastActiveTime;
        private Long currentActiveAgentId;
        private String agentDecisionStrategy;

        public MultiChatSessionStats(String sessionId, String username, Long storyId, Long sceneId) {
            this.sessionId = sessionId;
            this.username = username;
            this.storyId = storyId;
            this.sceneId = sceneId;
            this.creationTime = System.currentTimeMillis();
            this.lastActiveTime = this.creationTime;
            this.agentDecisionStrategy = "ROUND_ROBIN";
        }

        public void incrementMessageCount() {
            messageCount.incrementAndGet();
            updateActiveTime();
        }

        public void incrementAgentResponseCount(Long agentId) {
            agentResponseCount.incrementAndGet();
            agentSpeakCounts.merge(agentId, 1, Integer::sum);
            currentActiveAgentId = agentId;
            updateActiveTime();
        }

        public void incrementSceneChangeCount() {
            sceneChangeCount.incrementAndGet();
            updateActiveTime();
        }

        private void updateActiveTime() {
            this.lastActiveTime = System.currentTimeMillis();
        }

        public int getMessageCount() {
            return messageCount.get();
        }

        public int getAgentResponseCount() {
            return agentResponseCount.get();
        }

        public int getSceneChangeCount() {
            return sceneChangeCount.get();
        }

        public long getSessionAgeMs() {
            return System.currentTimeMillis() - creationTime;
        }

        public long getIdleTimeMs() {
            return System.currentTimeMillis() - lastActiveTime;
        }

        public int getAgentSpeakCount(Long agentId) {
            return agentSpeakCounts.getOrDefault(agentId, 0);
        }

        public java.util.Map<Long, Integer> getAllAgentSpeakCounts() {
            return new ConcurrentHashMap<>(agentSpeakCounts);
        }
    }
} 