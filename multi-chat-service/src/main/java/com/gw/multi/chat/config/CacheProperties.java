package com.gw.multi.chat.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.List;

@Data
@Component
@ConfigurationProperties(prefix = "cache")
public class CacheProperties {
    private String prefix;
    private List<CacheConfig> configs;

    public String getCacheName(String cacheName) {
        return this.getPrefix() + ":" + cacheName;
    }

    public Duration getCacheExpiration(String cacheName) {
        return this.getConfigs().stream()
                .filter(config -> config.getName().equals(cacheName))
                .findFirst()
                .map(config -> {
                    String expiration = config.getExpireAfterWrite();
                    if (expiration.endsWith("m")) {
                        return Duration.ofMinutes(Long.parseLong(expiration.substring(0, expiration.length() - 1)));
                    } else if (expiration.endsWith("h")) {
                        return Duration.ofHours(Long.parseLong(expiration.substring(0, expiration.length() - 1)));
                    } else if (expiration.endsWith("s")) {
                        return Duration.ofSeconds(Long.parseLong(expiration.substring(0, expiration.length() - 1)));
                    }
                    return Duration.ofMinutes(10); // Default fallback
                })
                .orElse(Duration.ofMinutes(10)); // Default if not found
    }

    @Data
    public static class CacheConfig {
        private String name;
        private String expireAfterWrite;
    }
}
