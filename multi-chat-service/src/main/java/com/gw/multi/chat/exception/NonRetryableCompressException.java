package com.gw.multi.chat.exception;

/**
 * 不可重试的压缩异常
 * 用于数据格式错误、权限问题等不可恢复的错误
 */
public class NonRetryableCompressException extends CompressTaskException {

    public NonRetryableCompressException(String message) {
        super(message);
    }

    public NonRetryableCompressException(String message, Throwable cause) {
        super(message, cause);
    }

    @Override
    public boolean isRetryable() {
        return false;
    }
}
