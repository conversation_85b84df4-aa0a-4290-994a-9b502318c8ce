package com.gw.multi.chat.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 历史记录压缩任务配置
 * 支持各种可配置参数以提升灵活性
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "history.compress")
public class HistoryCompressConfig {

    /**
     * 是否启用压缩任务
     */
    private boolean enabled = false;

    /**
     * 调度间隔（毫秒）
     */
    private long scheduleInterval = 1200 * 1000; // 20分钟

    /**
     * 压缩阈值 - 消息数量超过此值才进行压缩
     */
    private long compressThreshold = 100;

    /**
     * 单次处理的最大会话数
     */
    private int maxSessionsPerBatch = 10;

    // 手动添加getter方法以确保IDE兼容性
    /**
     * 并发处理线程数
     */
    private int concurrentThreads = 3;

    /**
     * AI压缩请求超时时间（毫秒）
     * 增加到120秒以处理复杂提示词和大量内容
     */
    private long aiRequestTimeout = 120000; // 120秒

    /**
     * 单次压缩处理的最大消息数量
     * 超过此数量将进行分批处理
     */
    private int maxMessagesPerBatch = 200;

    /**
     * 分批处理时的重叠消息数量
     * 用于保持上下文连贯性
     */
    private int batchOverlapSize = 20;

    /**
     * 超时策略配置
     * 每个批次/场景的额外超时时间（毫秒）
     * 增加到30秒以适应更长的处理时间
     */
    private long perTaskExtraTimeout = 30000; // 30秒

    /**
     * 最大总超时时间（毫秒）
     * 防止超时时间过长，增加到10分钟以处理复杂任务
     */
    private long maxTotalTimeout = 600000; // 10分钟


    /**
     * 最大重试次数
     */
    private int maxRetryCount = 3;

    /**
     * 重试间隔（毫秒）
     */
    private long retryInterval = 5000; // 5秒

    /**
     * 是否启用监控指标
     */
    private boolean metricsEnabled = true;

    /**
     * 压缩记录保留天数
     */
    private int retentionDays = 90;

    /**
     * 批量查询大小
     */
    private int batchQuerySize = 1000;

    /**
     * 是否启用缓存
     */
    private boolean cacheEnabled = true;

    /**
     * 缓存过期时间（秒）
     */
    private long cacheExpireSeconds = 300; // 5分钟

    /**
     * 是否启用详细日志
     */
    private boolean verboseLogging = false;

    /**
     * 内存阈值百分比（参照chat-service）
     */
    private double memoryThresholdPercent = 80.0;

    /**
     * 强制GC阈值百分比（参照chat-service）
     */
    private double forceGcThresholdPercent = 85.0;

    /**
     * 内存检查间隔（参照chat-service）
     */
    private int memoryCheckInterval = 10;

    /**
     * 获取配置摘要（参照chat-service）
     */
    public String getConfigSummary() {
        return String.format(
            "HistoryCompressConfig{enabled=%s, scheduleInterval=%dms, compressThreshold=%d, " +
            "maxSessionsPerBatch=%d, concurrentThreads=%d, aiRequestTimeout=%dms, " +
            "maxMessagesPerBatch=%d, batchOverlapSize=%d, memoryThreshold=%.1f%%, forceGcThreshold=%.1f%%}",
            enabled, scheduleInterval, compressThreshold, maxSessionsPerBatch,
            concurrentThreads, aiRequestTimeout, maxMessagesPerBatch, batchOverlapSize,
            memoryThresholdPercent, forceGcThresholdPercent
        );
    }
}