package com.gw.multi.chat.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 群聊最新会话内容VO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "群聊最新会话内容")
public class ChatLatestContentVO {

    /**
     * 会话ID
     */
    @Schema(description = "会话ID")
    private String sessionId;

    /**
     * 故事ID
     */
    @Schema(description = "故事ID")
    private Long storyId;

    /**
     * 故事名称
     */
    @Schema(description = "故事名称")
    private String storyName;

    /**
     * 当前场景ID
     */
    @Schema(description = "当前场景ID")
    private Long sceneId;

    /**
     * 场景名称
     */
    @Schema(description = "场景名称")
    private String sceneName;

    /**
     * 场景背景图URL
     */
    @Schema(description = "场景背景图URL")
    private String sceneBgUrl;

    /**
     * 最后发言的智能体名称
     */
    @Schema(description = "最后发言的智能体名称")
    private String chatAgentName;

    /**
     * 最后一条消息内容
     */
    @Schema(description = "最后一条消息内容")
    private String chatContent;

    /**
     * 最后一条消息时间
     */
    @Schema(description = "最后一条消息时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 最后发言的智能体ID
     */
    @Schema(description = "最后发言的智能体ID")
    private Long lastChatAgentId;



    @Schema(description = "置顶状态 1 置顶 0 取消指定")
    private int topping;


    /**
     * 最后访问时间
     */
    @Schema(description = "最后访问时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastAccessTime;
}
