package com.gw.multi.chat.vo;

import lombok.Builder;
import lombok.Data;

/**
 * 聊天上下文VO
 */
@Data
@Builder
public class ChatContextVO {

    /**
     * 角色：user, assistant, system
     */
    private String role;

    /**
     * 消息类型：question, answer, text
     */
    private String type;

    /**
     * 消息内容
     */
    private String content;

    /**
     * 内容类型：text, image, audio, video
     */
    private String contentType;

    /**
     * 音频URL
     */
    private String audioUrl;

    /**
     * 音频时长
     */
    private String audioDuration;

    // 手动添加默认构造器和方法
    public ChatContextVO() {
    }

    public ChatContextVO(String role, String content) {
        this.role = role;
        this.content = content;
    }

    public ChatContextVO(String role, String type, String content, String contentType, String audioUrl, String audioDuration) {
        this.role = role;
        this.type = type;
        this.content = content;
        this.contentType = contentType;
        this.audioUrl = audioUrl;
        this.audioDuration = audioDuration;
    }
} 