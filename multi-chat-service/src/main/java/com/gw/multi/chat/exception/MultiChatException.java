package com.gw.multi.chat.exception;

import lombok.Getter;

/**
 * 🚨 多人聊天服务自定义异常基类
 * 提供更精确的异常分类和错误处理机制
 */
@Getter
public class MultiChatException extends RuntimeException {

    /**
     * 错误代码
     */
    private final String errorCode;

    /**
     * 是否可重试
     */
    private final boolean retryable;

    public MultiChatException(String message) {
        super(message);
        this.errorCode = "MULTI_CHAT_ERROR";
        this.retryable = false;
    }

    public MultiChatException(String errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
        this.retryable = false;
    }

    public MultiChatException(String errorCode, String message, boolean retryable) {
        super(message);
        this.errorCode = errorCode;
        this.retryable = retryable;
    }

    public MultiChatException(String message, Throwable cause) {
        super(message, cause);
        this.errorCode = "MULTI_CHAT_ERROR";
        this.retryable = false;
    }

    public MultiChatException(String errorCode, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
        this.retryable = false;
    }

    public MultiChatException(String errorCode, String message, Throwable cause, boolean retryable) {
        super(message, cause);
        this.errorCode = errorCode;
        this.retryable = retryable;
    }

    /**
     * 🔧 会话相关异常
     */
    public static class SessionException extends MultiChatException {
        public SessionException(String message) {
            super("SESSION_ERROR", message);
        }

        public SessionException(String message, Throwable cause) {
            super("SESSION_ERROR", message, cause);
        }
    }

    /**
     * 🤖 智能体响应异常
     */
    public static class AgentResponseException extends MultiChatException {
        public AgentResponseException(String message) {
            super("AGENT_RESPONSE_ERROR", message, true); // 智能体异常通常可重试
        }

        public AgentResponseException(String message, Throwable cause) {
            super("AGENT_RESPONSE_ERROR", message, cause, true);
        }

        public AgentResponseException(String message, boolean retryable) {
            super("AGENT_RESPONSE_ERROR", message, retryable);
        }
    }

    /**
     * ⚙️ 配置异常
     */
    public static class ConfigurationException extends MultiChatException {
        public ConfigurationException(String message) {
            super("CONFIG_ERROR", message);
        }

        public ConfigurationException(String message, Throwable cause) {
            super("CONFIG_ERROR", message, cause);
        }
    }

    /**
     * 🔄 业务逻辑异常
     */
    public static class BusinessException extends MultiChatException {
        public BusinessException(String message) {
            super("BUSINESS_ERROR", message);
        }

        public BusinessException(String message, Throwable cause) {
            super("BUSINESS_ERROR", message, cause);
        }
    }

    /**
     * 🌐 外部服务异常
     */
    public static class ExternalServiceException extends MultiChatException {
        public ExternalServiceException(String message) {
            super("EXTERNAL_SERVICE_ERROR", message, true); // 外部服务异常通常可重试
        }

        public ExternalServiceException(String message, Throwable cause) {
            super("EXTERNAL_SERVICE_ERROR", message, cause, true);
        }
    }
}