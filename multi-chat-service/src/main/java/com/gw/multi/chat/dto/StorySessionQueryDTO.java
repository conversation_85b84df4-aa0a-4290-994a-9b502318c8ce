package com.gw.multi.chat.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 故事会话查询DTO
 */
@Data
@Schema(description = "故事会话查询参数")
@NoArgsConstructor
@AllArgsConstructor
public class StorySessionQueryDTO {

    @Schema(description = "故事ID")
    @NotNull(message = "故事ID不能为空")
    private Long storyId;

    @Schema(description = "场景ID")
    @NotNull(message = "场景ID不能为空")
    private Long sceneId;
} 