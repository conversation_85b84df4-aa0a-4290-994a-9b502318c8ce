package com.gw.multi.chat.websocket.manager;

import com.gw.multi.chat.websocket.MultiChatSessionMetadata;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.socket.WebSocketSession;
import reactor.core.publisher.Sinks;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.locks.ReadWriteLock;
import java.util.concurrent.locks.ReentrantReadWriteLock;

/**
 * 🎯 **统一的WebSocket会话管理器**
 * <p>
 * 结合了原有简单版本和重构版本的所有优点：
 * - ✅ 基础会话管理（来自原有版本）
 * - ✅ 高级元数据管理（来自重构版本）
 * - ✅ 完整的统计系统
 * - ✅ 自动超时清理
 * - ✅ 心跳支持
 * - ✅ 线程安全保证
 * - ✅ 内存泄漏防护
 */
@Component
@Log4j2
public class WebSocketSessionManager {

    // ========== 核心存储 ==========

    // 会话元数据存储 - 支持高级功能
    private final Map<String, MultiChatSessionMetadata> sessionMetadataCache = new ConcurrentHashMap<>();

    // 简单会话存储 - 兼容原有接口
    private final Map<String, WebSocketSession> simpleSessions = new ConcurrentHashMap<>();

    // 消息Sink存储 - 支持响应式消息发送
    private final Map<String, Sinks.Many<String>> messageSinks = new ConcurrentHashMap<>();

    // 用户活跃时间跟踪
    private final Map<String, Long> userActiveTimeCache = new ConcurrentHashMap<>();

    // 会话到用户名的反向映射
    private final Map<String, String> sessionToUserMapping = new ConcurrentHashMap<>();

    // ========== 统计信息 ==========

    private final AtomicInteger totalSessionsCreated = new AtomicInteger(0);
    private final AtomicInteger totalSessionsCleaned = new AtomicInteger(0);
    private final AtomicInteger currentActiveSessions = new AtomicInteger(0);
    private final AtomicLong totalMessagesProcessed = new AtomicLong(0);
    private final AtomicInteger heartbeatsSent = new AtomicInteger(0);
    private final AtomicInteger timeoutCleanupsPerformed = new AtomicInteger(0);

    // ========== 线程安全保证 ==========

    private final ReadWriteLock cacheLock = new ReentrantReadWriteLock();

    // ========== 定时任务 ==========

    private ScheduledExecutorService cleanupExecutor;

    // ========== 配置参数 ==========

    @Value("${multi-chat.websocket.max-sessions:500}")
    private int maxSessions;

    @Value("${multi-chat.websocket.session-timeout-minutes:60}")
    private int sessionTimeoutMinutes;

    @Value("${multi-chat.websocket.cleanup-interval-minutes:10}")
    private int cleanupIntervalMinutes;

    @Value("${multi-chat.websocket.enable-auto-cleanup:true}")
    private boolean enableAutoCleanup;

    private volatile long sessionTimeoutMs;

    // ========== 初始化和销毁 ==========

    /**
     * 初始化会话管理器
     */
    public void init() {
        this.sessionTimeoutMs = sessionTimeoutMinutes * 60 * 1000L;

        if (enableAutoCleanup) {
            initCleanupScheduler();
        }

        log.info("🚀 统一WebSocket会话管理器初始化完成");
        log.info("📊 配置信息: 最大会话数={}, 超时时间={}分钟, 清理间隔={}分钟, 自动清理={}",
                maxSessions, sessionTimeoutMinutes, cleanupIntervalMinutes, enableAutoCleanup);
    }

    /**
     * 初始化清理调度器
     */
    private void initCleanupScheduler() {
        cleanupExecutor = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread t = new Thread(r, "unified-websocket-cleanup");
            t.setDaemon(true);
            return t;
        });

        cleanupExecutor.scheduleWithFixedDelay(
                this::performPeriodicCleanup,
                cleanupIntervalMinutes,
                cleanupIntervalMinutes,
                TimeUnit.MINUTES
        );

        log.info("⏰ 定时清理任务已启动，间隔: {}分钟", cleanupIntervalMinutes);
    }

    /**
     * 销毁会话管理器
     */
    public void destroy() {
        log.info("🔄 开始销毁统一WebSocket会话管理器");

        // 关闭定时任务
        if (cleanupExecutor != null && !cleanupExecutor.isShutdown()) {
            cleanupExecutor.shutdown();
            try {
                if (!cleanupExecutor.awaitTermination(30, TimeUnit.SECONDS)) {
                    cleanupExecutor.shutdownNow();
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                cleanupExecutor.shutdownNow();
            }
        }

        // 清理所有会话
        cleanupAllSessions();

        log.info("✅ 统一WebSocket会话管理器销毁完成");
    }

    // ========== 核心会话管理API ==========

    /**
     * 添加会话（兼容原有简单版本）
     */
    public void addSession(String username, WebSocketSession session,String storyId, String sceneId) {
        if (username == null || session == null || !session.isOpen()) {
            log.warn("⚠️ 无法添加无效的会话: username={}, session={}", username,
                    session != null ? session.getId() : "null");
            return;
        }

        cacheLock.writeLock().lock();
        try {
            // 检查会话数量限制
            if (sessionMetadataCache.size() >= maxSessions) {
                log.warn("❌ 会话数量已达上限 ({}), 拒绝新会话: {}", maxSessions, username);
                return;
            }

            // 清理用户的旧会话
            cleanupUserOldSessions(username);

            // 创建消息Sink
            Sinks.Many<String> sink = Sinks.many().multicast().onBackpressureBuffer();

            // 创建会话元数据
            MultiChatSessionMetadata metadata = MultiChatSessionMetadata.create(username, session, null, null);
            metadata.setMessageSink(sink);

            // 安全解析 sceneId 和 storyId，避免空字符串导致的 NumberFormatException
            Long parsedSceneId = null;
            Long parsedStoryId = null;

            if (sceneId != null && !sceneId.trim().isEmpty()) {
                try {
                    parsedSceneId = Long.parseLong(sceneId);
                } catch (NumberFormatException e) {
                    log.warn("⚠️ 无法解析 sceneId: '{}', 将设置为 null", sceneId);
                }
            }

            if (storyId != null && !storyId.trim().isEmpty()) {
                try {
                    parsedStoryId = Long.parseLong(storyId);
                } catch (NumberFormatException e) {
                    log.warn("⚠️ 无法解析 storyId: '{}', 将设置为 null", storyId);
                }
            }

            metadata.setHeadSceneId(parsedSceneId);
            metadata.setHeadStoryId(parsedStoryId);
            // 存储会话信息
            String sessionKey = buildSessionKey(username, session.getId());
            sessionMetadataCache.put(sessionKey, metadata);
            simpleSessions.put(username, session);
            messageSinks.put(username, sink);
            userActiveTimeCache.put(username, System.currentTimeMillis());
            sessionToUserMapping.put(session.getId(), username);

            // 更新统计信息
            updateSessionStatistics(true);

            log.info("✅ WebSocket会话已添加: 用户={}, 会话ID={}, storyId={}, sceneId={}",
                    username, session.getId(), parsedStoryId, parsedSceneId);

        } finally {
            cacheLock.writeLock().unlock();
        }
    }

    /**
     * 移除会话（兼容原有简单版本）
     */
    public void removeSession(String username) {
        if (username == null) {
            return;
        }

        cacheLock.writeLock().lock();
        try {
            // 清理会话元数据
            String sessionKey = findUserSessionKey(username);
            if (sessionKey != null) {
                MultiChatSessionMetadata metadata = sessionMetadataCache.remove(sessionKey);
                if (metadata != null) {
                    metadata.cleanup();
                }
            }

            // 清理简单会话存储
            WebSocketSession session = simpleSessions.remove(username);
            if (session != null) {
                sessionToUserMapping.remove(session.getId());
            }

            // 清理消息Sink
            Sinks.Many<String> sink = messageSinks.remove(username);
            if (sink != null) {
                sink.tryEmitComplete();
            }

            // 清理其他缓存
            userActiveTimeCache.remove(username);

            // 更新统计信息
            updateSessionStatistics(false);

            log.info("✅ WebSocket会话已移除: 用户={}", username);

        } finally {
            cacheLock.writeLock().unlock();
        }
    }

    // ========== 消息发送API ==========

    /**
     * 发送消息到用户（增强版本，返回发送结果）
     */
    public boolean sendMessageToUserWithResult(String username, String message) {
        if (username == null || message == null) {
            return false;
        }

        cacheLock.readLock().lock();
        try {
            Sinks.Many<String> sink = messageSinks.get(username);
            if (sink != null) {
                Sinks.EmitResult result = sink.tryEmitNext(message);
                boolean success = result.isSuccess();
                if (success) {
                    totalMessagesProcessed.incrementAndGet();
                    updateUserActiveTime(username);
                }
                return success;
            }
        } finally {
            cacheLock.readLock().unlock();
        }
        return false;
    }

    // ========== 查询API ==========

    /**
     * 获取会话元数据
     */
    public MultiChatSessionMetadata getSessionMetadata(String username) {
        String sessionKey = findUserSessionKey(username);
        return sessionKey != null ? sessionMetadataCache.get(sessionKey) : null;
    }

    /**
     * 获取所有活跃会话（用于心跳）
     */
    public Map<String, MultiChatSessionMetadata> getAllActiveSessions() {
        Map<String, MultiChatSessionMetadata> activeSessions = new HashMap<>();

        cacheLock.readLock().lock();
        try {
            for (Map.Entry<String, MultiChatSessionMetadata> entry : sessionMetadataCache.entrySet()) {
                MultiChatSessionMetadata metadata = entry.getValue();
                if (metadata != null && metadata.isValid()) {
                    String username = extractUsernameFromSessionKey(entry.getKey());
                    activeSessions.put(username, metadata);
                }
            }
        } finally {
            cacheLock.readLock().unlock();
        }

        return activeSessions;
    }

    // ========== 状态管理API ==========

    /**
     * 更新用户活跃时间
     */
    public void updateUserActiveTime(String username) {
        if (username == null) {
            return;
        }

        long currentTime = System.currentTimeMillis();
        userActiveTimeCache.put(username, currentTime);

        // 同时更新会话元数据中的活跃时间
        String sessionKey = findUserSessionKey(username);
        if (sessionKey != null) {
            sessionMetadataCache.computeIfPresent(sessionKey, (key, metadata) -> {
                metadata.updateActiveTime();
                return metadata;
            });
        }
    }

    // ========== 清理和维护API ==========

    /**
     * 清理超时会话
     */
    public int cleanupTimeoutSessions() {
        int cleanedCount = 0;
        long currentTime = System.currentTimeMillis();

        cacheLock.writeLock().lock();
        try {
            List<String> usersToCleanup = new ArrayList<>();

            for (Map.Entry<String, Long> entry : userActiveTimeCache.entrySet()) {
                String username = entry.getKey();
                Long lastActiveTime = entry.getValue();

                if (lastActiveTime != null && (currentTime - lastActiveTime) > sessionTimeoutMs) {
                    usersToCleanup.add(username);
                }
            }

            for (String username : usersToCleanup) {
                try {
                    log.info("⏰ 清理超时会话: 用户={}, 超时时间={}分钟",
                            username, (currentTime - userActiveTimeCache.get(username)) / (60 * 1000));
                    removeSession(username);
                    cleanedCount++;
                } catch (Exception e) {
                    log.error("❌ 清理超时会话失败: username={}", username, e);
                }
            }

            if (cleanedCount > 0) {
                timeoutCleanupsPerformed.addAndGet(cleanedCount);
            }

        } finally {
            cacheLock.writeLock().unlock();
        }

        return cleanedCount;
    }

    /**
     * 执行定期清理
     */
    private void performPeriodicCleanup() {
        try {
            log.debug("🧹 开始执行定期清理任务");

            int cleanedSessions = cleanupTimeoutSessions();

            if (cleanedSessions > 0) {
                log.info("📊 定期清理完成: 超时会话={}", cleanedSessions);
            }

            // 记录统计信息
            logPeriodicStatistics();

        } catch (Exception e) {
            log.error("❌ 定期清理任务执行失败", e);
        }
    }

    /**
     * 清理所有会话
     */
    public void cleanupAllSessions() {
        log.info("🧹 开始清理所有WebSocket会话");

        cacheLock.writeLock().lock();
        try {
            int cleanedMetadata = 0;
            int cleanedSinks = 0;

            // 清理会话元数据
            for (MultiChatSessionMetadata metadata : sessionMetadataCache.values()) {
                try {
                    if (metadata != null) {
                        metadata.cleanup();
                        cleanedMetadata++;
                    }
                } catch (Exception e) {
                    log.warn("⚠️ 清理会话元数据失败", e);
                }
            }

            // 清理消息Sink
            for (Sinks.Many<String> sink : messageSinks.values()) {
                try {
                    if (sink != null) {
                        sink.tryEmitComplete();
                        cleanedSinks++;
                    }
                } catch (Exception e) {
                    log.warn("⚠️ 清理消息Sink失败", e);
                }
            }

            // 清理所有缓存
            sessionMetadataCache.clear();
            simpleSessions.clear();
            messageSinks.clear();
            userActiveTimeCache.clear();
            sessionToUserMapping.clear();

            log.info("✅ 清理完成: 元数据={}, Sink={}, 总缓存已清空", cleanedMetadata, cleanedSinks);

        } finally {
            cacheLock.writeLock().unlock();
        }
    }

    // ========== 统计和监控API ==========

    /**
     * 获取简单统计信息（兼容性）
     */
    public SessionStatistics getStatistics() {
        return new SessionStatistics(
                totalSessionsCreated.get(),
                totalSessionsCleaned.get(),
                currentActiveSessions.get(),
                sessionMetadataCache.size(),
                userActiveTimeCache.size()
        );
    }

    /**
     * 记录心跳发送
     */
    public void recordHeartbeatSent() {
        heartbeatsSent.incrementAndGet();
    }

    /**
     * 记录定期统计信息
     */
    private void logPeriodicStatistics() {
        log.info("📊 会话统计: 活跃={}, 总创建={}, 总清理={}, 总消息={}, 心跳={}",
                currentActiveSessions.get(), totalSessionsCreated.get(), totalSessionsCleaned.get(),
                totalMessagesProcessed.get(), heartbeatsSent.get());
    }

    // ========== 工具方法 ==========

    private String buildSessionKey(String username, String sessionId) {
        return username + "_" + sessionId;
    }

    private String findUserSessionKey(String username) {
        return sessionMetadataCache.keySet().stream()
                .filter(key -> key.startsWith(username + "_"))
                .findFirst()
                .orElse(null);
    }

    private String extractUsernameFromSessionKey(String sessionKey) {
        int index = sessionKey.indexOf("_");
        return index > 0 ? sessionKey.substring(0, index) : sessionKey;
    }

    private void cleanupUserOldSessions(String username) {
        // 清理会话元数据中的旧会话
        sessionMetadataCache.entrySet().removeIf(entry -> {
            String sessionKey = entry.getKey();
            if (sessionKey.startsWith(username + "_")) {
                MultiChatSessionMetadata metadata = entry.getValue();
                if (metadata != null) {
                    log.info("🧹 清理用户{}的旧会话: {}", username, metadata.getSessionId());
                    metadata.cleanup();
                }
                return true;
            }
            return false;
        });

        // 清理其他存储中的旧会话
        WebSocketSession oldSession = simpleSessions.remove(username);
        if (oldSession != null) {
            sessionToUserMapping.remove(oldSession.getId());
        }

        Sinks.Many<String> oldSink = messageSinks.remove(username);
        if (oldSink != null) {
            oldSink.tryEmitComplete();
        }
    }

    private void updateSessionStatistics(boolean isCreate) {
        if (isCreate) {
            totalSessionsCreated.incrementAndGet();
            currentActiveSessions.incrementAndGet();
        } else {
            totalSessionsCleaned.incrementAndGet();
            currentActiveSessions.updateAndGet(count -> Math.max(0, count - 1));
        }
    }

    // ========== 统计信息记录类 ==========

    /**
     * 简单会话统计信息（兼容性）
     */
    public record SessionStatistics(
            int totalCreated,
            int totalCleaned,
            int currentActive,
            int cacheSize,
            int activeTimeSize
    ) {
    }
}
