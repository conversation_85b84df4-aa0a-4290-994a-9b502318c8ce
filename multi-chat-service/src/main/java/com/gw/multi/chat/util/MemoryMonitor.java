package com.gw.multi.chat.util;

import lombok.Data;
import lombok.extern.log4j.Log4j2;

/**
 * 内存监控工具类
 * 用于监控和管理JVM内存使用情况
 */
@Log4j2
public class MemoryMonitor {

    /**
     * 内存信息数据类
     */
    @Data
    public static class MemoryInfo {
        private long totalMemory;      // 总内存
        private long freeMemory;       // 空闲内存
        private long usedMemory;       // 已使用内存
        private long maxMemory;        // 最大内存
        private double usagePercent;   // 使用百分比

        public MemoryInfo(long totalMemory, long freeMemory, long usedMemory, long maxMemory, double usagePercent) {
            this.totalMemory = totalMemory;
            this.freeMemory = freeMemory;
            this.usedMemory = usedMemory;
            this.maxMemory = maxMemory;
            this.usagePercent = usagePercent;
        }

        @Override
        public String toString() {
            return String.format("内存使用情况 - 已用: %dMB, 空闲: %dMB, 总计: %dMB, 最大: %dMB, 使用率: %.2f%%",
                    usedMemory / 1024 / 1024,
                    freeMemory / 1024 / 1024,
                    totalMemory / 1024 / 1024,
                    maxMemory / 1024 / 1024,
                    usagePercent);
        }
    }

    /**
     * 获取当前内存使用信息
     *
     * @return 内存信息对象
     */
    public static MemoryInfo getCurrentMemoryInfo() {
        Runtime runtime = Runtime.getRuntime();
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        long usedMemory = totalMemory - freeMemory;
        long maxMemory = runtime.maxMemory();
        double usagePercent = (double) usedMemory / maxMemory * 100;

        return new MemoryInfo(totalMemory, freeMemory, usedMemory, maxMemory, usagePercent);
    }

    /**
     * 强制进行垃圾回收
     *
     * @return 垃圾回收后的内存信息
     */
    public static MemoryInfo forceGarbageCollection() {
        MemoryInfo beforeGC = getCurrentMemoryInfo();
        log.debug("垃圾回收前: {}", beforeGC);

        // 建议JVM进行垃圾回收
        System.gc();
        
        // 等待一小段时间让GC完成
        try {
            Thread.sleep(100);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        MemoryInfo afterGC = getCurrentMemoryInfo();
        log.debug("垃圾回收后: {}", afterGC);

        long freedMemory = beforeGC.getUsedMemory() - afterGC.getUsedMemory();
        if (freedMemory > 0) {
            log.info("垃圾回收释放了 {}MB 内存", freedMemory / 1024 / 1024);
        }

        return afterGC;
    }

    /**
     * 检查是否有足够的内存
     *
     * @param requiredMemory 需要的内存大小（字节）
     * @return 是否有足够内存
     */
    public static boolean hasEnoughMemory(long requiredMemory) {
        MemoryInfo memoryInfo = getCurrentMemoryInfo();
        long availableMemory = memoryInfo.getMaxMemory() - memoryInfo.getUsedMemory();
        return availableMemory >= requiredMemory;
    }

    /**
     * 估算处理指定数量消息需要的内存
     *
     * @param messageCount 消息数量
     * @return 估算的内存需求（字节）
     */
    public static long estimateMemoryForMessages(int messageCount) {
        // 估算每条消息平均占用内存：1KB（包括对象开销）
        long estimatedMemoryPerMessage = 1024;
        return messageCount * estimatedMemoryPerMessage;
    }

    /**
     * 检查内存使用率是否超过阈值
     *
     * @param threshold 阈值百分比（0-100）
     * @return 是否超过阈值
     */
    public static boolean isMemoryUsageAboveThreshold(double threshold) {
        MemoryInfo memoryInfo = getCurrentMemoryInfo();
        return memoryInfo.getUsagePercent() > threshold;
    }

    /**
     * 获取可用内存大小
     *
     * @return 可用内存大小（字节）
     */
    public static long getAvailableMemory() {
        MemoryInfo memoryInfo = getCurrentMemoryInfo();
        return memoryInfo.getMaxMemory() - memoryInfo.getUsedMemory();
    }

    /**
     * 获取内存使用率
     *
     * @return 内存使用率百分比
     */
    public static double getMemoryUsagePercent() {
        return getCurrentMemoryInfo().getUsagePercent();
    }

    /**
     * 记录当前内存状态到日志
     *
     * @param context 上下文信息
     */
    public static void logMemoryStatus(String context) {
        MemoryInfo memoryInfo = getCurrentMemoryInfo();
        log.info("{} - {}", context, memoryInfo);
    }

    /**
     * 在内存使用率过高时进行警告和清理
     *
     * @param warningThreshold 警告阈值
     * @param forceGcThreshold 强制GC阈值
     * @return 处理后的内存信息
     */
    public static MemoryInfo checkAndCleanMemory(double warningThreshold, double forceGcThreshold) {
        MemoryInfo memoryInfo = getCurrentMemoryInfo();
        
        if (memoryInfo.getUsagePercent() > forceGcThreshold) {
            log.warn("内存使用率过高: {:.2f}%，强制进行垃圾回收", memoryInfo.getUsagePercent());
            return forceGarbageCollection();
        } else if (memoryInfo.getUsagePercent() > warningThreshold) {
            log.warn("内存使用率较高: {:.2f}%，建议关注", memoryInfo.getUsagePercent());
        }
        
        return memoryInfo;
    }
}
