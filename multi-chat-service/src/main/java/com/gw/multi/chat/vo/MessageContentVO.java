package com.gw.multi.chat.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 消息内容VO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MessageContentVO {

    /**
     * 角色：system, user, assistant
     */
    private String role;

    /**
     * 消息内容
     */
    private String content;

    // 手动添加getter/setter方法
    public String getRole() {
        return role;
    }

    public void setRole(String role) {
        this.role = role;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
} 