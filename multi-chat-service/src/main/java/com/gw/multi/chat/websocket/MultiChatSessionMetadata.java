package com.gw.multi.chat.websocket;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.web.reactive.socket.WebSocketSession;
import reactor.core.Disposable;
import reactor.core.publisher.Sinks;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.locks.StampedLock;

/**
 * 多智能体群聊会话元数据
 * 扩展原有SessionMetadata，支持群聊特性和智能体管理
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MultiChatSessionMetadata {

    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * 用户名
     */
    private String username;
    private Long headStoryId;
    private Long headSceneId;

    /**
     * 故事ID
     */
    private Long storyId;

    /**
     * 当前场景ID
     */
    private Long currentSceneId;

    /**
     * WebSocket会话
     */
    private WebSocketSession session;

    /**
     * 消息Sink
     */
    private Sinks.Many<String> messageSink;

    /**
     * 消息订阅
     */
    private Disposable subscription;

    /**
     * 会话活跃状态
     */
    private AtomicBoolean active;

    /**
     * 最后活跃时间
     */
    private volatile long lastActiveTime;

    /**
     * 会话锁（使用StampedLock提供更好的性能）
     */
    private StampedLock sessionLock;

    /**
     * 创建时间
     */
    private long createTime;

    /**
     * 消息计数
     */
    private AtomicLong messageCount;

    /**
     * 参与的智能体ID列表
     */
    private Set<Long> participatingAgents;

    /**
     * 智能体状态映射：agentId -> status
     */
    private Map<Long, String> agentStatuses;

    /**
     * 智能体最后发言时间：agentId -> timestamp
     */
    private Map<Long, Long> agentLastSpeakTimes;

    /**
     * 智能体发言次数：agentId -> count
     */
    private Map<Long, Integer> agentSpeakCounts;

    /**
     * 当前活跃的智能体ID
     */
    private Long currentActiveAgentId;

    /**
     * 智能体轮询索引（用于轮询策略）
     */
    private AtomicLong agentRoundRobinIndex;

    /**
     * 用户画像信息
     */
    private Map<String, Object> userProfile;

    /**
     * 场景上下文信息
     */
    private Map<String, Object> sceneContext;

    /**
     * 智能体上下文缓存：agentId -> context
     */
    private Map<Long, Object> agentContextCache;

    /**
     * 消息历史（最近N条消息）
     */
    private List<String> recentMessages;

    /**
     * 智能体决策策略：ROUND_ROBIN, INTELLIGENT, MANUAL
     */
    private String agentDecisionStrategy;

    /**
     * 场景切换历史
     */
    private List<SceneChangeRecord> sceneChangeHistory;

    /**
     * 是否启用智能体自动回复
     */
    private Boolean autoAgentReply;

    /**
     * 智能体回复延迟（毫秒）
     */
    private Long agentReplyDelay;

    /**
     * 群聊配置
     */
    private GroupChatConfig groupChatConfig;
    public boolean checkSessionValid(Long storyId, Long sceneId){
        if (headStoryId == null || headSceneId == null) {
            return storyId.equals(this.storyId) && sceneId.equals(this.currentSceneId);
        }
        return storyId.equals(this.headStoryId) && sceneId.equals(this.headSceneId);
    }
    /**
     * 创建新的多智能体会话元数据
     */
    public static MultiChatSessionMetadata create(String username, WebSocketSession session,
                                                  Long storyId, Long sceneId) {
        long currentTime = System.currentTimeMillis();

        GroupChatConfig defaultConfig = GroupChatConfig.builder()
                .maxAgents(10)
                .allowConcurrentReplies(false)
                .agentReplyInterval(2000L)
                .enableEmotionState(true)
                .recordContext(true)
                .contextRetentionTime(3600000L) // 1小时
                .build();

        return MultiChatSessionMetadata.builder()
                .sessionId(session.getId())
                .username(username)
                .storyId(storyId)
                .currentSceneId(sceneId)
                .session(session)
                .active(new AtomicBoolean(true))
                .lastActiveTime(currentTime)
                .createTime(currentTime)
                .sessionLock(new StampedLock())
                .messageCount(new AtomicLong(0L))
                .participatingAgents(ConcurrentHashMap.newKeySet())
                .agentStatuses(new ConcurrentHashMap<>())
                .agentLastSpeakTimes(new ConcurrentHashMap<>())
                .agentSpeakCounts(new ConcurrentHashMap<>())
                .agentRoundRobinIndex(new AtomicLong(0))
                .userProfile(new ConcurrentHashMap<>())
                .sceneContext(new ConcurrentHashMap<>())
                .agentContextCache(new ConcurrentHashMap<>())
                .recentMessages(new java.util.ArrayList<>())
                .sceneChangeHistory(new java.util.ArrayList<>())
                .agentDecisionStrategy("ROUND_ROBIN")
                .autoAgentReply(true)
                .agentReplyDelay(2000L)
                .groupChatConfig(defaultConfig)
                .build();
    }

    /**
     * 检查会话是否有效
     */
    public boolean isValid() {
        return session != null && session.isOpen() &&
                active != null && active.get();
    }

    /**
     * 更新最后活跃时间
     */
    public void updateActiveTime() {
        this.lastActiveTime = System.currentTimeMillis();
    }

    /**
     * 标记会话为非活跃
     */
    public void markInactive() {
        if (active != null) {
            active.set(false);
        }
    }

    /**
     * 清理资源
     */
    public void cleanup() {
        markInactive();

        if (messageSink != null) {
            try {
                messageSink.tryEmitComplete();
            } catch (Exception e) {
                // 忽略清理异常
            }
        }

        if (subscription != null && !subscription.isDisposed()) {
            try {
                subscription.dispose();
            } catch (Exception e) {
                // 忽略清理异常
            }
        }

        // 清理缓存数据
        if (agentContextCache != null) {
            agentContextCache.clear();
        }
        if (recentMessages != null) {
            recentMessages.clear();
        }
    }

    /**
     * 场景切换记录
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SceneChangeRecord {
        private Long fromSceneId;
        private Long toSceneId;
        private String changeReason;
        private Long timestamp;
        private String triggeredBy;
    }

    /**
     * 群聊配置
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GroupChatConfig {
        /**
         * 最大智能体数量
         */
        private Integer maxAgents;

        /**
         * 是否允许并发回复
         */
        private Boolean allowConcurrentReplies;

        /**
         * 智能体回复间隔（毫秒）
         */
        private Long agentReplyInterval;

        /**
         * 是否启用情感状态
         */
        private Boolean enableEmotionState;

        /**
         * 是否记录上下文
         */
        private Boolean recordContext;

        /**
         * 上下文保留时间（毫秒）
         */
        private Long contextRetentionTime;
    }
} 