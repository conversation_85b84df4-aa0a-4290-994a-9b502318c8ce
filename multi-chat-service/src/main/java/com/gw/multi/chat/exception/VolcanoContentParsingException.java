package com.gw.multi.chat.exception;

/**
 * 火山方舟内容解析异常
 * 用于处理AI响应内容解析过程中的各种异常情况
 */
public class VolcanoContentParsingException extends Exception {

    private final String errorCode;

    public VolcanoContentParsingException(String message, String errorCode) {
        super(message);
        this.errorCode = errorCode;
    }

    public VolcanoContentParsingException(String message, String errorCode, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
    }

    public String getErrorCode() {
        return errorCode;
    }

    /**
     * 创建空响应异常
     */
    public static VolcanoContentParsingException emptyResponse() {
        return new VolcanoContentParsingException("AI响应为空或无效", "EMPTY_RESPONSE");
    }

    /**
     * 创建内容过长异常
     */
    public static VolcanoContentParsingException contentTooLong() {
        return new VolcanoContentParsingException("AI响应内容过长被截断", "CONTENT_TOO_LONG");
    }

    /**
     * 创建空内容异常
     */
    public static VolcanoContentParsingException emptyContent() {
        return new VolcanoContentParsingException("AI响应内容为空", "EMPTY_CONTENT");
    }

    /**
     * 创建解析失败异常
     */
    public static VolcanoContentParsingException parsingFailed(String details) {
        return new VolcanoContentParsingException("AI响应内容解析失败: " + details, "PARSING_FAILED");
    }

    /**
     * 创建解析失败异常（带原因）
     */
    public static VolcanoContentParsingException parsingFailed(String details, Throwable cause) {
        return new VolcanoContentParsingException("AI响应内容解析失败: " + details, "PARSING_FAILED", cause);
    }
}
