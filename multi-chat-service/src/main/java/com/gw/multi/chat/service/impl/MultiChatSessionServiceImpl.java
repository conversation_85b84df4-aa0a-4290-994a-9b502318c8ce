package com.gw.multi.chat.service.impl;

import com.gw.common.agent.constant.AgentCommonCacheConstant;
import com.gw.common.agent.constant.StoryConstant;
import com.gw.common.agent.service.AgentProxyService;
import com.gw.common.agent.service.AgentStoryProxyService;
import com.gw.common.agent.vo.AgentBaseVO;
import com.gw.common.agent.vo.AgentStoryBaseVO;
import com.gw.common.agent.vo.AgentStorySceneBaseVO;
import com.gw.multi.chat.config.CacheProperties;
import com.gw.multi.chat.entity.MultiChatMsg;
import com.gw.multi.chat.entity.MultiChatSession;
import com.gw.multi.chat.service.MultiChatMsgService;
import com.gw.multi.chat.service.MultiChatSessionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.bson.Document;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;

import static com.gw.multi.chat.constant.MultiChatConstant.CHAT_ASSISTANT_ROLE;

/**
 * 多智能体群聊会话服务实现
 */
@Service
@RequiredArgsConstructor
@Log4j2
public class MultiChatSessionServiceImpl implements MultiChatSessionService {
    private final AgentStoryProxyService storyProxyService;
    private final MongoTemplate mongoTemplate;
    private final CacheProperties cacheProperties;
    private final MultiChatMsgService chatMessageService;
    private final AgentProxyService agentProxyService;

    private MultiChatMsg createOpenTextMessage(Long agentId, String agentName, String content, Long storyId,
                                               Long sceneId,
                                               MultiChatSession chatSession, String sceneSessionId) {
        MultiChatMsg message = new MultiChatMsg();
        message.setStoryId(storyId);
        message.setSceneId(sceneId);
        message.setStorySessionId(chatSession.getSessionId());
        message.setSceneSessionId(sceneSessionId);
        message.setAgentId(agentId);
        message.setAgentName(agentName);
        message.setContent(content);
        message.setStatus("SENT");
        message.setRole(CHAT_ASSISTANT_ROLE);
        message.setReplyToMessageId("");
        message.setSequenceNumber(0L);
        message.setCreateTime(java.time.LocalDateTime.now());
        message.setUpdateTime(java.time.LocalDateTime.now());

        return message;
    }

    public MultiChatSession.SceneSession createSceneSession(Long sceneId, MultiChatSession storySession) {
        String cacheKey = cacheProperties.getCacheName(AgentCommonCacheConstant.AGENT_BASE_STORY_SCENE_KEY);
        AgentStorySceneBaseVO sceneVo = storyProxyService.getStorySceneInfo(sceneId, cacheKey);
        if (sceneVo == null) {
            log.error("场景不存在:{}", sceneId);
            throw new RuntimeException("场景不存在:" + sceneId);
        }
        var sceneSession = new MultiChatSession.SceneSession();
        sceneSession.setSceneId(sceneId);
        sceneSession.setSceneSessionId(generateSceneSessionId(sceneId));
        if (sceneVo.getOpeningText() != null && !sceneVo.getOpeningText().isEmpty() &&
                sceneVo.getOpeningAgentId() != null) {
            cacheKey = cacheProperties.getCacheName(AgentCommonCacheConstant.AGENT_BASE_MAP_CACHE_KEY);
            AgentBaseVO agent = agentProxyService.getAgentInfo(cacheKey, sceneVo.getOpeningAgentId());
            if (agent != null) {
                chatMessageService.saveMessage(createOpenTextMessage(agent.getId(), agent.getName(), sceneVo.getOpeningText(),
                        storySession.getStoryId(), sceneId, storySession, sceneSession.getSceneSessionId()));
            }
        }
        return sceneSession;
    }

    @Override
    public MultiChatSession getOrCreateStorySession(String username, Long storyId, Long sceneId) {
        log.info("获取或创建会话，username: {}, storyId: {}", username, storyId);

        MultiChatSession session;
        Query query = new Query(Criteria.where("username").is(username).and("storyId").is(storyId));
        session = mongoTemplate.findOne(query, MultiChatSession.class);
        String cacheKey = cacheProperties.getCacheName(AgentCommonCacheConstant.AGENT_BASE_STORY_MAP_CACHE_KEY);
        AgentStoryBaseVO vo = storyProxyService.getStoryInfo(cacheKey, storyId);
        // 如果没有找到现有会话，创建新会话
        if (vo == null) {
            log.error("故事不存在: {}", storyId);
            throw new RuntimeException("故事不存在: " + storyId);
        } else if (vo.getShelfStatus() != StoryConstant.SHELF_ON_STATUS) {
            log.error("故事未上上架，请联系管理员: {}", storyId);
            throw new RuntimeException("故事未上上架，请联系管理员: " + storyId);
        } else if (vo.getStatus() != StoryConstant.STORY_STATUS_PUBLISHED) {
            log.error("故事未发布，请联系管理员: {}", storyId);
            throw new RuntimeException("故事未发布，请联系管理员: " + storyId);
        }
        if (session == null) {

            session = new MultiChatSession();
            session.setSessionId(generateSessionId());
            session.setUsername(username);
            session.setStoryId(storyId);

            session.setName(vo.getName());
            session.setMessageCount(0L);

            session.setIsPinned(false);
            LocalDateTime now = LocalDateTime.now();
            session.setCreateTime(now);
            session.setUpdateTime(now);
            session.setLastAccessTime(now);
            var sceneSession = createSceneSession(sceneId, session);
            Map<Long, MultiChatSession.SceneSession> sceneSessionMap = new HashMap<>();
            sceneSessionMap.put(sceneId, sceneSession);
            session.setSceneMap(sceneSessionMap);
            session.setCurrentSceneId(sceneId);
            session.setLstCompressTime(0L);
            session.setLastMessageTime(0L);
            session.setLastAccessTime(now);
            session = saveSession(session);
            return session;
        }
        var sceneSession = session.getSceneMap().get(sceneId);
        if (sceneSession == null) {
            sceneSession = createSceneSession(sceneId, session);
            session.getSceneMap().put(sceneId, sceneSession);
        }
        session.setLastAccessTime(LocalDateTime.now());
        session.setCurrentSceneId(sceneId);
        session = saveSession(session);
        return session;
    }

    @Override
    public MultiChatSession getSession(String sessionId) {
        if (sessionId == null || sessionId.isEmpty()) {
            return null;
        }

        Query query = new Query(Criteria.where("sessionId").is(sessionId));
        return mongoTemplate.findOne(query, MultiChatSession.class);
    }

    @Override
    public MultiChatSession saveSession(MultiChatSession session) {
        if (session == null) {
            return null;
        }

        session.setUpdateTime(LocalDateTime.now());
        return mongoTemplate.save(session);
    }

    @Override
    public MultiChatSession updateSession(MultiChatSession session) {
        return saveSession(session);
    }

    @Override
    public void updateCurrentScene(String sessionId, Long sceneId) {
        Query query = new Query(Criteria.where("sessionId").is(sessionId));
        Update update = new Update()
                .set("currentSceneId", sceneId)
                .set("updateTime", LocalDateTime.now());

        mongoTemplate.updateFirst(query, update, MultiChatSession.class);
    }

    @Override
    public Page<MultiChatSession> findByUsername(String username, Pageable pageable) {
        Query query = new Query(Criteria.where("username").is(username));

        long total = mongoTemplate.count(query, MultiChatSession.class);

        query.with(pageable);
        List<MultiChatSession> sessions = mongoTemplate.find(query, MultiChatSession.class);

        return new PageImpl<>(sessions, pageable, total);
    }

    @Override
    public Page<MultiChatSession> findByStoryId(Long storyId, Pageable pageable) {
        Query query = new Query(Criteria.where("storyId").is(storyId));

        long total = mongoTemplate.count(query, MultiChatSession.class);

        query.with(pageable);
        List<MultiChatSession> sessions = mongoTemplate.find(query, MultiChatSession.class);

        return new PageImpl<>(sessions, pageable, total);
    }

    @Override
    public void deleteSession(String sessionId) {
        Query query = new Query(Criteria.where("sessionId").is(sessionId));
        mongoTemplate.remove(query, MultiChatSession.class);
    }

    @Override
    public List<MultiChatSession> getActiveSessionsByUser(String username) {
        Query query = new Query(Criteria.where("username").is(username)
                .and("status").is("ACTIVE"));

        return mongoTemplate.find(query, MultiChatSession.class);
    }

    @Override
    public List<MultiChatSession> getSessionsByUserAndStoryId(String username, Long storyId) {
        Query query = new Query(Criteria.where("username").is(username)
                .and("storyId").is(storyId));
        return mongoTemplate.find(query, MultiChatSession.class);
    }

    /**
     * 生成唯一的会话ID
     */
    private String generateSessionId() {
        return "session_" + UUID.randomUUID().toString().replace("-", "");
    }
    private String generateSceneSessionId(Long sceneId) {
        return "scene_" + sceneId + "_" + UUID.randomUUID().toString().replace("-", "");
    }
    @Override
    public void deleteAllSessionBySceneId(Long storyId, Long sceneId, String username) {
        List<MultiChatSession> chatSessions = getSessionsByUserAndStoryId(username, storyId);
        log.info("查找会话数量 {}", chatSessions.size());
        for (MultiChatSession session : chatSessions) {
            if (session.getSceneMap().containsKey(sceneId)) {
                log.info("删除章节会话 {}", session.getSessionId());
                session.getSceneMap().remove(sceneId);
                if(session.getCurrentSceneId().equals(sceneId)){
                    session.setLastMessage( null);
                    session.setLastChatAgentId( null);
                }
                saveSession(session);
            }
        }

    }

    @Override
    public List<MultiChatSession> findNeedCompressSession() {

        Query query = new Query(
                new Criteria().andOperator(
                        Criteria.where("lstCompressTime").exists(true),
                        Criteria.where("$expr").is(
                                new Document("$gt", Arrays.asList(
                                        new Document("$subtract", Arrays.asList("$lastMessageTime", "$lstCompressTime")),
                                        1200L // 15分钟 = 900秒
                                ))
                        )
                )
        );


        return mongoTemplate.find(query, MultiChatSession.class);
    }

    @Override
    public Page<MultiChatSession> handleGetSessionsLatestChatByUser(String username, PageRequest pageRequest) {
        log.info("获取用户 {} 的群聊会话列表，分页参数: {}", username, pageRequest);

        // 构建查询条件：查询用户的所有会话，按置顶状态和最后消息时间排序
        Query query = new Query(Criteria.where("username").is(username));

        // 过滤掉没有最后消息的会话
        query.addCriteria(new Criteria().orOperator(
                Criteria.where("lastMessage").exists(true).ne(null).ne(""),
                Criteria.where("lastMessage").exists(false).type(10) // 10表示null类型
        ).not());

        // 计算总数
        long total = mongoTemplate.count(query, MultiChatSession.class);

        // 应用分页和排序
        query.with(pageRequest);
        List<MultiChatSession> sessions = mongoTemplate.find(query, MultiChatSession.class);

        log.info("查询到用户 {} 的会话数量: {}", username, sessions.size());

        return new PageImpl<>(sessions, pageRequest, total);
    }

    @Override
    public void sessionTopping(String sessionId, int topping, String username) {
        log.info("设置会话置顶状态，sessionId: {}, topping: {}, username: {}", sessionId, topping, username);

        if (sessionId == null || sessionId.isEmpty()) {
            throw new IllegalArgumentException("会话ID不能为空");
        }

        if (username == null || username.isEmpty()) {
            throw new IllegalArgumentException("用户名不能为空");
        }

        // 验证topping参数
        if (topping != 0 && topping != 1) {
            throw new IllegalArgumentException("置顶状态参数错误，只能是0或1");
        }

        // 查询会话
        Query query = new Query(Criteria.where("sessionId").is(sessionId));
        MultiChatSession session = mongoTemplate.findOne(query, MultiChatSession.class);

        if (session == null) {
            log.error("会话不存在，sessionId: {}", sessionId);
            throw new RuntimeException("会话不存在");
        }

        // 验证用户权限
        if (!session.getUsername().equals(username)) {
            log.error("用户 {} 没有权限修改会话 {}", username, sessionId);
            throw new RuntimeException("没有权限修改该会话");
        }

        // 转换topping为Boolean：1表示置顶，0表示取消置顶
        Boolean isPinned = (topping == 1);

        // 更新置顶状态
        Update update = new Update()
                .set("isPinned", isPinned)
                .set("updateTime", LocalDateTime.now());

        mongoTemplate.updateFirst(query, update, MultiChatSession.class);

        log.info("成功设置会话置顶状态，sessionId: {}, topping: {}, isPinned: {}", sessionId, topping, isPinned);
    }
}