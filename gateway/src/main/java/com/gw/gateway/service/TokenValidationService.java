package com.gw.gateway.service;

import com.gw.common.CommonConstant;
import com.gw.gateway.vo.TokenInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Log4j2
@Service
@RequiredArgsConstructor
public class TokenValidationService {

    private final ReactiveUserAuthService userAuthService;
    private final TokenCacheService tokenCacheService;

    public Mono<TokenInfo> validateTokenAndPermissions(String token, String path) {
        return tokenCacheService.getTokenInfo(token)
                .switchIfEmpty(
                        userAuthService.validateToken(token)
                                .flatMap(rsp -> {
                                    if (rsp.getCode() != CommonConstant.SUCCESS_CODE) {
                                        return Mono.just(TokenInfo.invalid());
                                    }
                                    TokenInfo tokenInfo = rsp.getData();
                                    if (tokenInfo.isValid()) {
                                        return tokenCacheService.setTokenInfo(token, tokenInfo)
                                                .thenReturn(tokenInfo);
                                    }
                                    return Mono.just(tokenInfo);
                                })
                )
//            .flatMap(tokenInfo -> checkPermissions(tokenInfo, path))
                .doOnError(error -> log.error("Token validation error", error));
    }

    private Mono<TokenInfo> checkPermissions(TokenInfo tokenInfo, String path) {
        if (!tokenInfo.isValid()) {
            return Mono.just(tokenInfo);
        }
        return userAuthService.checkPermission(tokenInfo.getUsername(), path)
                .map(hasPermission -> {
                    if (!hasPermission) {
                        return TokenInfo.invalid();
                    }
                    return tokenInfo;
                });
    }
} 