package com.gw.gateway.service;

import com.gw.gateway.vo.TokenInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.data.redis.core.ReactiveRedisTemplate;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.time.Duration;

@Log4j2
@Service
@RequiredArgsConstructor
public class TokenCacheService {

    private static final String TOKEN_KEY_PREFIX = "ca:user:token:";
    private static final Duration DEFAULT_CACHE_DURATION = Duration.ofMinutes(5);
    private final ReactiveRedisTemplate<String, TokenInfo> redisTemplate;

    public Mono<TokenInfo> getTokenInfo(String token) {
        return redisTemplate.opsForValue()
                .get(TOKEN_KEY_PREFIX + token)
                .doOnError(error -> log.error("Redis get error", error));
    }

    public Mono<Boolean> setTokenInfo(String token, TokenInfo tokenInfo) {
        return redisTemplate.opsForValue()
                .set(TOKEN_KEY_PREFIX + token, tokenInfo, DEFAULT_CACHE_DURATION)
                .doOnError(error -> log.error("Redis set error", error));
    }

    public Mono<Boolean> removeTokenInfo(String token) {
        return redisTemplate.opsForValue()
                .delete(TOKEN_KEY_PREFIX + token)
                .doOnError(error -> log.error("Redis delete error", error));
    }
} 