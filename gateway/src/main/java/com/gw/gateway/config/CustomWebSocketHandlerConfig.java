package com.gw.gateway.config;

import com.gw.gateway.websocket.WebSocketProxyHandler;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.reactive.HandlerMapping;
import org.springframework.web.reactive.handler.SimpleUrlHandlerMapping;
import org.springframework.web.reactive.socket.WebSocketHandler;

import java.util.HashMap;
import java.util.Map;

/**
 * 自定义WebSocket处理配置
 * 提供基于Reactive的WebSocket支持
 * <p>
 * 注意：此配置是处理WebSocket路由的主要方式，替代了application.yml中的路由配置
 * 优点是可以直接与WebSocketProxyHandler集成，提供更灵活的控制
 */
@Configuration
@RequiredArgsConstructor
public class CustomWebSocketHandlerConfig {

    private final WebSocketProxyHandler webSocketProxyHandler;

    /**
     * 配置WebSocket处理器映射
     * 将特定路径映射到我们的代理处理器
     */
    @Bean
    public HandlerMapping webSocketHandlerMapping() {
        Map<String, WebSocketHandler> map = new HashMap<>();
        // 将WebSocket聊天服务路径映射到代理处理器
        map.put("/api/v1/ws/chat/**", webSocketProxyHandler);
        // 将WebSocket多人聊天服务路径映射到代理处理器
        map.put("/api/v1/ws/multi/chat/**", webSocketProxyHandler);

        SimpleUrlHandlerMapping mapping = new SimpleUrlHandlerMapping();
        mapping.setUrlMap(map);
        // 设置高优先级，确保WebSocket路由被正确处理
        mapping.setOrder(-1);
        return mapping;
    }
}