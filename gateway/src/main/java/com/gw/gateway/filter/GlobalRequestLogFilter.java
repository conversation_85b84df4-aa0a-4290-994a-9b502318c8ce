package com.gw.gateway.filter;

import com.gw.gateway.config.RequestLogProperties;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;

@Log4j2
@Component
@RequiredArgsConstructor
public class GlobalRequestLogFilter implements GlobalFilter, Ordered {

    private final RequestLogProperties properties;

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        if (!properties.isEnabled()) {
            return chain.filter(exchange);
        }

        ServerHttpRequest request = exchange.getRequest();
        LocalDateTime startTime = LocalDateTime.now();

        if (properties.isIncludeHeaders()) {
            log.info("Request headers: {}", request.getHeaders());
        }

        log.info("Incoming request: {} {}", request.getMethod(), request.getURI());

        return chain.filter(exchange)
                .doOnSuccess(v -> {
                    LocalDateTime endTime = LocalDateTime.now();
                    long duration = java.time.Duration.between(startTime, endTime).toMillis();
                    log.info("Request completed: {} {} - {} - {}ms",
                            request.getMethod(),
                            request.getURI(),
                            exchange.getResponse().getStatusCode(),
                            duration);
                })
                .doOnError(throwable -> {
                    LocalDateTime endTime = LocalDateTime.now();
                    long duration = java.time.Duration.between(startTime, endTime).toMillis();
                    log.error("Request failed: {} {} - {}ms - Error: {}",
                            request.getMethod(),
                            request.getURI(),
                            duration,
                            throwable.getMessage());
                });
    }

    @Override
    public int getOrder() {
        return Ordered.HIGHEST_PRECEDENCE;
    }
} 