package com.gw.gateway.filter;

import lombok.Data;
import lombok.Getter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.List;

@Getter
@Configuration
@ConfigurationProperties(prefix = "auth")
public class AuthProperties {
    private final Skip skip = new Skip();

    @Data
    public static class Skip {
        private List<String> urls = new ArrayList<>();
    }

}
