package com.gw.gateway.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TokenInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * token是否有效
     */
    private boolean valid;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 用户名
     */
    private String username;


    /**
     * 真实姓名
     */
    private String realName;

    /**
     * 角色列表
     */
    private List<String> roles;

    /**
     * 权限列表
     */
    private List<String> permissions;

    /**
     * token过期时间 (时间戳)
     */
    private Long expireTime;

    /**
     * 登录时间 (时间戳)
     */
    private Long loginTime;

    /**
     * 额外信息，可以存储一些补充数据
     */
    private Map<String, Object> additionalInfo;

    /**
     * 创建一个无效的TokenInfo
     */
    public static TokenInfo invalid() {
        return TokenInfo.builder()
                .valid(false)
                .build();
    }

    /**
     * 构建一个TokenInfo的简单工厂方法
     */
    public static TokenInfo createToken(String userId, String username, List<String> roles) {
        return TokenInfo.builder()
                .valid(true)
                .userId(userId)
                .username(username)
                .roles(roles)
                .loginTime(System.currentTimeMillis())
                .build();
    }

    /**
     * 检查token是否过期
     */
    public boolean isExpired() {
        if (expireTime == null) {
            return false;
        }
        return System.currentTimeMillis() > expireTime;
    }

    /**
     * 检查是否具有特定角色
     */
    public boolean hasRole(String role) {
        return roles != null && roles.contains(role);
    }

    /**
     * 检查是否具有特定权限
     */
    public boolean hasPermission(String permission) {
        return permissions != null && permissions.contains(permission);
    }

    /**
     * 获取额外信息中的特定字段
     */
    @SuppressWarnings("unchecked")
    public <T> T getAdditionalField(String key) {
        return additionalInfo != null ? (T) additionalInfo.get(key) : null;
    }

    /**
     * 检查TokenInfo是否包含必要的基本信息
     */
    public boolean hasBasicInfo() {
        return valid && userId != null && username != null;
    }

    /**
     * 转换为简单的Map格式（用于传递给其他服务）
     */
    public Map<String, String> toHeaderMap() {
        Map<String, String> headers = new HashMap<>();
        headers.put("X-User-Id", userId);
        headers.put("X-Username", username);
        headers.put("X-RealName", username);
        if (roles != null && !roles.isEmpty()) {
            headers.put("X-Roles", String.join(",", roles));
        }
        return headers;
    }
}
