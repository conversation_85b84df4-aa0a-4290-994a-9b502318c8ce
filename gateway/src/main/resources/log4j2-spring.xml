<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="WARN" monitorInterval="30">
    <Properties>
        <Property name="LOG_PATTERN">%d{yyyy-MM-dd HH:mm:ss.SSS} [%t] %-5level %logger{36} - %msg%n</Property>
        <Property name="LOG_PATH">logs</Property>
    </Properties>

    <Appenders>
        <!-- 控制台输出 -->
        <Console name="Console" target="SYSTEM_OUT">
            <PatternLayout pattern="%d{yyyy-MM-dd HH:mm:ss.SSS} [%t] %-5level %logger{36} - %msg%n"/>
        </Console>

        <!-- INFO级别日志文件 -->
        <RollingFile name="InfoFile" fileName="logs/gateway_info.log"
                     filePattern="logs/gateway_info-%d{yyyy-MM-dd}-%i.log.gz"
                     bufferedIO="true" bufferSize="8192">
            <Filters>
                <ThresholdFilter level="info" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
            <SizeBasedTriggeringPolicy size="5 MB"/>
            <PatternLayout pattern="%d{yyyy-MM-dd HH:mm:ss.SSS} [%t] %-5level %logger{36} - %msg%n"/>
            <DefaultRolloverStrategy max="100" compressionLevel="9">
                <Delete basePath="logs" maxDepth="1">
                    <IfFileName glob="gateway_info-*.log.gz">
                        <IfLastModified age="30d"/>
                    </IfFileName>
                </Delete>
            </DefaultRolloverStrategy>
        </RollingFile>

        <!-- WARN级别日志文件 -->
        <RollingFile name="WarnFile" fileName="logs/gateway_warn.log"
                     filePattern="logs/gateway_warn-%d{yyyy-MM-dd}-%i.log.gz"
                     bufferedIO="true" bufferSize="8192">
            <Filters>
                <ThresholdFilter level="warn" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
            <SizeBasedTriggeringPolicy size="5 MB"/>
            <PatternLayout pattern="%d{yyyy-MM-dd HH:mm:ss.SSS} [%t] %-5level %logger{36} - %msg%n"/>
            <DefaultRolloverStrategy max="100" compressionLevel="9">
                <Delete basePath="logs" maxDepth="1">
                    <IfFileName glob="gateway_warn-*.log.gz">
                        <IfLastModified age="30d"/>
                    </IfFileName>
                </Delete>
            </DefaultRolloverStrategy>
        </RollingFile>

        <!-- ERROR级别及以上日志文件 -->
        <RollingFile name="ErrorFile" fileName="logs/gateway_error.log"
                     filePattern="logs/gateway_error-%d{yyyy-MM-dd}-%i.log.gz"
                     bufferedIO="true" bufferSize="8192">
            <Filters>
                <ThresholdFilter level="error" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
            <SizeBasedTriggeringPolicy size="5 MB"/>
            <PatternLayout pattern="%d{yyyy-MM-dd HH:mm:ss.SSS} [%t] %-5level %logger{36} - %msg%n"/>
            <DefaultRolloverStrategy max="100" compressionLevel="9">
                <Delete basePath="logs" maxDepth="1">
                    <IfFileName glob="gateway_error-*.log.gz">
                        <IfLastModified age="30d"/>
                    </IfFileName>
                </Delete>
            </DefaultRolloverStrategy>
        </RollingFile>

        <!-- 异步日志配置 -->
        <Async name="AsyncInfo">
            <AppenderRef ref="InfoFile"/>
        </Async>
        <Async name="AsyncWarn">
            <AppenderRef ref="WarnFile"/>
        </Async>
        <Async name="AsyncError">
            <AppenderRef ref="ErrorFile"/>
        </Async>
    </Appenders>

    <Loggers>
        <!-- 网关路由日志 -->
        <Logger name="org.springframework.cloud.gateway" level="debug" additivity="false">
            <AppenderRef ref="Console"/>
        </Logger>

        <!-- HTTP 请求日志 -->
        <Logger name="org.springframework.web" level="debug" additivity="false">
            <AppenderRef ref="Console"/>
        </Logger>

        <!-- 负载均衡日志 -->
        <Logger name="org.springframework.cloud.loadbalancer" level="debug" additivity="false">
            <AppenderRef ref="Console"/>
        </Logger>

        <!-- 异步根日志配置 -->
        <AsyncRoot level="info" includeLocation="false">
            <!-- 输出所有级别至控制台 -->
            <AppenderRef ref="Console"/>

            <!-- 分别输出不同级别至各自文件 -->
            <AppenderRef ref="InfoFile" level="info"/>
            <AppenderRef ref="WarnFile" level="warn"/>
            <AppenderRef ref="ErrorFile" level="error"/>
        </AsyncRoot>
    </Loggers>
</Configuration>