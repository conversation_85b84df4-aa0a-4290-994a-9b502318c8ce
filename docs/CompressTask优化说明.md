# CompressTask 优化说明

## 优化概述

本次优化主要解决了 CompressTask 中的多个问题，提升了代码的健壮性、性能和可维护性。

## 主要问题及解决方案

### 1. buildCompressContexts 方法逻辑错误

**问题**：
- 第376行错误地调用了 `getCompressionPrompt(userMessage)` 而不是直接使用 `userMessage`
- 重复设置了 `contentType` 属性
- 角色设置不正确（应该是 "user" 而不是 "system"）

**解决方案**：
```java
// 修复前
contexts.add(ChatContextVO.builder()
    .contentType("text")
    .role("system")  // 错误的角色
    .type("text")
    .content(getCompressionPrompt(userMessage))  // 错误的调用
    .contentType("text")  // 重复设置
    .build());

// 修复后
contexts.add(ChatContextVO.builder()
    .role("user")  // 正确的角色
    .type("text")
    .content(userMessageBuilder.toString())  // 正确的内容
    .contentType("text")
    .build());
```

### 2. @PostConstruct 中直接执行压缩任务

**问题**：
- 应用启动时直接执行压缩任务，可能影响启动性能
- 缺乏线程安全控制

**解决方案**：
- 使用专用线程池进行后台处理
- 延迟启动，避免影响应用启动
- 添加线程安全控制（AtomicBoolean）

```java
@PostConstruct
public void init() {
    if (!taskEnabled) {
        log.info("压缩任务已禁用，跳过初始化");
        return;
    }
    
    // 创建专用线程池
    executorService = Executors.newSingleThreadScheduledExecutor(r -> {
        Thread thread = new Thread(r, "compress-task-thread");
        thread.setDaemon(true);
        return thread;
    });
    
    // 延迟启动，避免影响应用启动性能
    executorService.schedule(this::startPeriodicCompression, 2, TimeUnit.MINUTES);
}
```

### 3. needsCompression 方法总是返回 true

**问题**：
- ConversationSessionCompressDto 的 needsCompression 方法总是返回 true
- 缺乏实际的业务逻辑判断

**解决方案**：
- 实现基于序列号的压缩判断逻辑
- 检查是否有新消息需要压缩

```java
public boolean needsCompression() {
    // 检查序列号是否存在且有效
    if (lstSeqNum == null || lstSeqNum <= 0) {
        return false; // 没有有效消息，不需要压缩
    }

    // 如果从未压缩过，需要压缩
    if (lstCompressTime == null || lstCompressTime == 0) {
        return true;
    }

    // 如果压缩序列号为空或小于当前序列号，需要压缩
    if (lstCompressSeqNum == null || lstCompressSeqNum < lstSeqNum) {
        return true;
    }

    return false;
}
```

### 4. 缺少错误恢复和资源管理

**问题**：
- 缺少线程池的正确关闭
- 异常处理可能中断定期任务
- 缺少手动触发机制

**解决方案**：
- 添加 destroy 方法正确关闭线程池
- 改进异常处理，避免中断定期任务
- 添加手动触发压缩任务的方法

## 新增功能

### 1. 配置化控制

新增配置项：
```yaml
compress:
  task:
    enabled: true              # 是否启用压缩任务
    interval-minutes: 30       # 任务执行间隔（分钟）
    page-size: 1000           # 分页大小
    max-pages: 1000           # 最大页数
    batch-size: 100           # 批处理大小
```

### 2. 线程安全和性能优化

- 使用 AtomicBoolean 控制任务执行状态
- 专用线程池处理压缩任务
- 添加适当的休眠，避免过度占用资源
- 改进的统计信息（处理数量、跳过数量）

### 3. 手动触发机制

```java
public void triggerCompressionTask() {
    if (executorService != null && !executorService.isShutdown()) {
        executorService.submit(this::executeCompressTaskSafely);
        log.info("手动触发压缩任务");
    }
}
```

## 测试覆盖

创建了完整的单元测试：
- 测试不同场景下的压缩需求判断
- 测试任务启用/禁用功能
- 测试手动触发和销毁功能
- 测试空结果处理

## 使用建议

1. **生产环境配置**：
   - 根据实际负载调整 `interval-minutes`
   - 适当设置 `page-size` 和 `batch-size`
   - 启用详细日志监控任务执行情况

2. **监控要点**：
   - 关注任务执行时间和处理数量
   - 监控内存使用情况
   - 观察压缩效果和AI服务调用情况

3. **故障排查**：
   - 检查配置是否正确
   - 查看日志中的错误信息
   - 使用手动触发功能进行测试

## 兼容性说明

- 保持了原有的公共方法接口
- 配置项向后兼容
- 不影响现有的压缩逻辑和数据结构
