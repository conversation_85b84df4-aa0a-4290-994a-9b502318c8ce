-- 1. 对象管理
INSERT INTO a_menu (id, name, code, parent_id, sequence, status, path)
VALUES (1, '对象管理', 'object', NULL, 1, 1, '/device/object');

-- 2. 设备管理
INSERT INTO a_menu (id, name, code, parent_id, sequence, status, path)
VALUES (2, '设备管理', 'device', NULL, 2, 1, '/device'),
       (21, '设备管理', 'device-manage', 2, 1, 1, '/device/manage'),
       (22, '设备分组', 'device-group', 2, 3, 1, '/device/group'),
       (23, '联动配置', 'device-scene', 2, 3, 1, '/device/scene'),
       (24, '模式配置', 'device-mode', 2, 3, 1, '/device/mode'),
       (25, '地址映射', 'device-mapping', 2, 3, 1, '/device/mapping');

-- 3. 通讯管理
INSERT INTO a_menu (id, name, code, parent_id, sequence, status, path)
VALUES (3, '通讯管理', 'communication', NULL, 3, 1, '/communication'),
       (31, '设备管理', 'communication-manage', 3, 1, 1, '/communication/device');

-- 4. 报警管理
INSERT INTO a_menu (id, name, code, parent_id, sequence, status, path)
VALUES (4, '报警管理', 'warning', NULL, 4, 1, '/warning'),
       (41, '报警设置', 'warning-manage', 4, 1, 1, '/warning/manage');

-- 5. 巡视维保
INSERT INTO a_menu (id, name, code, parent_id, sequence, status, path)
VALUES (5, '巡视维保', 'patrol', NULL, 5, 1, '/patrol/manage'),
       (51, '巡视内容', 'patrol-manage', 5, 1, 1, '/patrol/manage'),
       (52, '工单管理', 'patrol-workOrder', 5, 2, 1, '/patrol/workOrder');

-- 6. 时间计划
INSERT INTO a_menu (id, name, code, parent_id, sequence, status, path)
VALUES (6, '时间计划', 'plan', NULL, 6, 1, '/plan/index');

-- 7. 系统设置
INSERT INTO a_menu (id, name, code, parent_id, sequence, status, path)
VALUES (7, '系统设置', 'system', NULL, 7, 1, '/system/user'),
       (71, '用户管理', 'system-user', 7, 1, 1, '/system/user'),
       (72, '角色管理', 'system-role', 7, 2, 1, '/system/role'),
       (73, '系统日志', 'system-log', 7, 10, 1, '/system/log'),
       (74, '部门管理', 'system-department', 7, 3, 1, '/system/department');
-- 角色管理按钮
INSERT INTO a_button (id, menu_id, name, code, sequence)
VALUES (1000, 72, '新增', 'system.role.add', 1),
       (1001, 72, '修改', 'system.role.edit', 2),
       (1002, 72, '删除', 'system.role.del', 3),
       (1003, 72, '授权', 'system.role.auth', 4),
       (1004, 72, '启用', 'system.role.status', 5);
--用户管理按钮
INSERT INTO a_button (id, menu_id, name, code, sequence)
VALUES (1010, 71, '新增', 'system.user.add', 1),
       (1011, 71, '修改', 'system.user.edit', 2),
       (1012, 71, '删除', 'system.user.del', 3),
       (1013, 71, '启用', 'system.user.status', 4),
       (1014, 71, '修改密码', 'system.user.password', 5);
INSERT INTO a_button (id, menu_id, name, code, sequence)
VALUES (1020, 74, '新增', 'system.department.add', 1),
       (1021, 74, '修改', 'system.department.edit', 2),
       (1022, 74, '删除', 'system.department.del', 3);

-- 8. 流程管理
INSERT INTO a_menu (id, name, code, parent_id, sequence, status, path)
VALUES (8, '流程管理', 'system', NULL, 8, 1, '/process/list'),
       (81, '流程列表', 'process-manage', 8, 1, 1, '/process/list');