package com.gw.user.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDateTime;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Schema(description = "登录返回的信息")
public class LoginWXInfo {
    @Schema(description = "token 后续请求用")
    private String accessToken;
    @Schema(description = "用户名")
    private String username;
    @Schema(description = "真实姓名")
    private String realName;
    @Schema(description = "昵称")
    private String nickname;
    @Schema(description = "用户ID")
    private Long userId;
    @Schema(description = "是否新用户")
    private int isNew;
    @JsonIgnore
    private String refreshToken;
    @Schema(description = "超时时间 单位是秒")
    @JsonIgnore
    private long expiresTime;
    @Schema(description = "授权的菜单")
    private List<String> roles;
    @Schema(description = "授权的按键")
    private List<String> authBtnlist;
    @Schema(description = "微信openId")
    private String wxOpenId;
    @Schema(description = "微信头像")
    private String avatar;
    @Schema(description = "性别")
    private int gender;
    @Schema(description = "会员等级")
    private int vipLevel;
    @Schema(description = "会员剩余天数")
    private int vipRemainDays;
    @Schema(description = "邀请码")
    private String inviteCode;
    private String identify;
    @Schema(description = "会员过期时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime vipExpireTime;
}