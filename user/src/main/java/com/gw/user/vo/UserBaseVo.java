package com.gw.user.vo;


import com.gw.user.entity.UserBaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.beans.BeanUtils;

@Getter
@Setter
@NoArgsConstructor
@Schema(description = "用户基本信息")
public class UserBaseVo {
    private long id;
    private String username;
    private String realName;
    private String phone;

    public UserBaseVo(UserBaseEntity entity) {
        BeanUtils.copyProperties(entity, this);
    }
}