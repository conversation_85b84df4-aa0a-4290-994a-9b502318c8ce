package com.gw.user.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

@EqualsAndHashCode(callSuper = true)
@Data
public class UserDetailVo extends UserVo {
    @Schema(description = "会员等级")
    private int vipLevel;
    @Schema(description = "会员剩余天数")
    private int vipRemainDays;
    @Schema(description = "邀请码")
    private String inviteCode;
    @Schema(description = "会员过期时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime vipExpireTime;
    private int inviteCount;
    private String invitor;
}
