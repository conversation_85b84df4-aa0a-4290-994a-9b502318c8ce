package com.gw.user.service.impl;

import com.alibaba.fastjson2.JSON;
import com.gw.common.membership.constant.MemberCacheConstant;
import com.gw.common.membership.service.MembershipProxyService;
import com.gw.common.membership.vo.InvitationCodeVO;
import com.gw.common.membership.vo.UserMembershipVO;
import com.gw.user.config.CacheProperties;
import com.gw.user.constant.UserConstant;
import com.gw.user.dto.LoginParam;
import com.gw.user.dto.WxLoginBaseParam;
import com.gw.user.dto.WxLoginParam;
import com.gw.user.entity.RoleEntity;
import com.gw.user.entity.TokenEntity;
import com.gw.user.entity.UserEntity;
import com.gw.user.mapper.TokenMapper;
import com.gw.user.mapper.UserMapper;
import com.gw.user.provider.JwtProvider;
import com.gw.user.service.AuthenticationService;
import com.gw.user.service.RoleService;
import com.gw.user.service.TokenService;
import com.gw.user.service.UserService;
import com.gw.user.vo.LoginInfo;
import com.gw.user.vo.LoginWXInfo;
import com.gw.user.vo.RefreshTokenInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Log4j2
public class AuthenticationServiceImpl implements AuthenticationService {
    private final UserMapper userMapper;
    private final TokenService tokenService;
    private final TokenMapper tokenMapper;
    private final PasswordEncoder passwordEncoder;
    private final JwtProvider jwtService;
    private final AuthenticationManager authenticationManager;
    private final RoleService roleService;
    private final UserService userService;
    private final MembershipProxyService membershipProxyService;
    private final CacheProperties cacheProperties;

    @Override
    @CacheEvict(value = "token", allEntries = true, beforeInvocation = true)
    public LoginInfo authenticate(LoginParam loginRequest) {
        authenticateUser(loginRequest.getUsername(), loginRequest.getPassword());

        UserEntity user = getUserOrThrowException(loginRequest.getUsername());
        validateUserStatus(user);

        String device = loginRequest.getDevice();
        String jwtToken = generateAndSaveTokens(user, device);
        String refreshToken = jwtService.generateRefreshToken(user, device);

        log.info("用户:{} 登录成功", user.getRoles());
        int vipLevel = 0;
        int vipRemainDays = 0;
        String inviteCode = "";
        try {
            String cacheName = cacheProperties.getCacheName(MemberCacheConstant.MEMBERSHIP_BASE_CACHE_KEY + ":" + user.getUsername());
            UserMembershipVO vo = membershipProxyService.getMembershipByUsername(cacheName, user.getUsername());
            if (vo != null) {
                vipLevel = vo.getVipLevel();
                vipRemainDays = vo.getRemainingDays();
            }
            InvitationCodeVO codeVo = membershipProxyService.getInvitationCodeByUsername(user.getUsername());
            if (codeVo != null) {
                inviteCode = codeVo.getCode();
            } else {
                inviteCode = "";
            }
            log.info("vipLevel {}", vipLevel);
            log.info("vipRemainDays {}", vipRemainDays);
            log.info("inviteCode {}", inviteCode);

        } catch (Exception ex) {
            log.error("获取会员信息异常{}", ex.getMessage());
        }
        return buildLoginInfo(user, jwtToken, refreshToken);
    }

    @Override
    @CacheEvict(value = "token", allEntries = true, beforeInvocation = true)
    public RefreshTokenInfo refreshToken(String refreshToken, String device) throws IOException {
        String username = jwtService.extractUsername(refreshToken);
        if (username != null) {
            UserEntity user = this.userMapper.findByUsername(username)
                    .orElseThrow(() -> new BadCredentialsException("用户不存在"));

            if (jwtService.isTokenValid(refreshToken, user)) {
                String accessToken = generateAndSaveTokens(user, device);

                return RefreshTokenInfo.builder()
                        .accessToken(accessToken)
                        .refreshToken(refreshToken)
                        .build();
            }
        }
        throw new BadCredentialsException("Token无效");
    }

    @Override
    public LoginWXInfo wxMiniAppLogin(WxLoginParam loginRequest) {
        UserEntity user = userService.findOrCreateWxUser(loginRequest);
        validateUserStatus(user);

        String device = UserConstant.WX_LOGIN;
        String jwtToken = generateAndSaveTokens(user, device);
        String refreshToken = jwtService.generateRefreshToken(user, device);

        log.info("微信登录成功");
        if (user.getIsNew() == 1) {
            log.info("新用户登录 邀请码 {}", loginRequest.getInviteCode());
            if (loginRequest.getInviteCode() != null && !loginRequest.getInviteCode().isEmpty()) {
                membershipProxyService.useInvitationCode(user.getUsername(), loginRequest.getInviteCode());
            }
            membershipProxyService.newUserReg(user.getUsername());
        }

        return buildWxLoginInfo(user, jwtToken, refreshToken);
    }

    @Override
    public LoginWXInfo wxMiniAppLoginWithoutPhone(WxLoginBaseParam loginRequest) {
        UserEntity user = userService.findOrCreateWxUserWithoutPhone(loginRequest);
        validateUserStatus(user);

        String device = UserConstant.WX_LOGIN;
        String jwtToken = generateAndSaveTokens(user, device);
        String refreshToken = jwtService.generateRefreshToken(user, device);

        log.info("微信登录成功");
        if (user.getIsNew() == 1) {
            log.info("新用户登录 邀请码 {}", loginRequest.getInviteCode());
            if (loginRequest.getInviteCode() != null && !loginRequest.getInviteCode().isEmpty()) {
                membershipProxyService.useInvitationCode(user.getUsername(), loginRequest.getInviteCode());
            }
            membershipProxyService.newUserReg(user.getUsername());
        }

        return buildWxLoginInfo(user, jwtToken, refreshToken);
    }

    private void authenticateUser(String username, String password) {
        authenticationManager.authenticate(
                new UsernamePasswordAuthenticationToken(username, password));
    }

    private UserEntity getUserOrThrowException(String username) {
        try {
            return userService.findUserByUsername(username);
        } catch (Exception e) {
            log.error("查询用户失败", e);
            throw new BadCredentialsException("用户名或密码错误");
        }
    }

    private void validateUserStatus(UserEntity user) {
        if (user.getDeleted() == 1) {
            throw new BadCredentialsException("用户已被删除");
        }
        if (user.getStatus() == UserConstant.DISABLE_USER) {
            throw new BadCredentialsException("用户已被禁用");
        }
    }

    private String generateAndSaveTokens(UserEntity user, String device) {
        Map<String, Object> extraClaims = new HashMap<>();
        String jwtToken = jwtService.generateToken(extraClaims, user, device);

        revokeAllUserTokens(user, device);
        saveUserToken(user, jwtToken, device);

        return jwtToken;
    }

    private void saveUserToken(UserEntity user, String jwtToken, String device) {
        TokenEntity token = TokenEntity.builder()
                .userId(user.getId())
                .accessToken(jwtToken)
                .expired(false)
                .revoked(false)
                .device(device)
                .build();
        tokenMapper.insert(token);
    }

    private void revokeAllUserTokens(UserEntity user, String device) {
        var validUserTokens = tokenMapper.findAllValidTokenByUser(user.getId(), device);
        if (validUserTokens.isEmpty()) {
            return;
        }

        validUserTokens.forEach(token -> {
            token.setExpired(true);
            token.setRevoked(true);
            tokenMapper.updateById(token);
        });
    }

    private List<String> extractRoleNames(UserEntity user) {
        if (user.getRoles() == null) {
            return new ArrayList<>();
        }
        return user.getRoles().stream()
                .map(RoleEntity::getName)
                .collect(Collectors.toList());
    }

    private List<String> extractAuthButtons(UserEntity user) {
        if (user.getRoles() == null) {
            return new ArrayList<>();
        }
        return user.getRoles().stream()
                .filter(role -> role.getBtns() != null)
                .flatMap(role -> role.getBtns().stream())
                .distinct()
                .collect(Collectors.toList());
    }

    private LoginInfo buildLoginInfo(UserEntity user, String jwtToken, String refreshToken) {
        return LoginInfo.builder()
                .accessToken(jwtToken)
                .refreshToken(refreshToken)
                .username(user.getUsername())
                .realName(user.getRealName())
                .userId(user.getId())
                .roles(extractRoleNames(user))
                .authBtnlist(extractAuthButtons(user))
                .build();
    }

    private LoginWXInfo buildWxLoginInfo(UserEntity user, String jwtToken, String refreshToken) {
        int vipLevel = 0;
        int vipRemainDays = 0;
        LocalDateTime vipExpireTime = null;
        String inviteCode = "";
        try {
            String cacheName = cacheProperties.getCacheName(MemberCacheConstant.MEMBERSHIP_BASE_CACHE_KEY + ":" + user.getUsername());
            UserMembershipVO vo = membershipProxyService.getMembershipByUsername(cacheName, user.getUsername());
            if (vo != null) {
                vipLevel = vo.getVipLevel();
                vipRemainDays = vo.getRemainingDays();
                vipExpireTime = vo.getExpireTime();
                if (vipExpireTime != null) {
                    log.info("vipExpireTime {}", vipExpireTime.toString());
                }
            }
            InvitationCodeVO codeVo = membershipProxyService.getInvitationCodeByUsername(user.getUsername());
            if (codeVo != null) {
                inviteCode = codeVo.getCode();
            } else {
                inviteCode = "";
            }
        } catch (Exception ex) {
            log.error("获取会员信息异常{}", ex.getMessage());
        }
        log.info("username  {} 邀请码 {}", user.getUsername(), inviteCode);
        LoginWXInfo wxInfo = LoginWXInfo.builder()
                .accessToken(jwtToken)
                .isNew(user.getIsNew())
                .wxOpenId(user.getWxOpenId())
                .avatar(user.getAvatar())
                .refreshToken(refreshToken)
                .username(user.getUsername())
                .nickname(user.getNickname() != null ? user.getNickname() : user.getUsername())
                .realName(user.getRealName())
                .userId(user.getId())
                .roles(extractRoleNames(user))
                .authBtnlist(extractAuthButtons(user))
                .vipLevel(vipLevel)
                .gender(user.getGender() != null ? user.getGender() : 3)
                .identify(user.getIdentify() != null ? user.getIdentify() : "")
                .vipRemainDays(vipRemainDays)
                .inviteCode(inviteCode)
                .vipExpireTime(vipExpireTime)
                .build();
        log.info("微信登录信息:{}", JSON.toJSONString(wxInfo));
        return wxInfo;
    }
}
