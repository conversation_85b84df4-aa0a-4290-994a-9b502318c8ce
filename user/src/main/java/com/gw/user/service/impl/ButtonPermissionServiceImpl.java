package com.gw.user.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gw.common.exception.EntityNotFoundException;
import com.gw.user.entity.ButtonPermissionEntity;
import com.gw.user.mapper.ButtonPermissionMapper;
import com.gw.user.service.ButtonPermissionService;
import lombok.extern.log4j.Log4j2;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.stereotype.Service;
import org.springframework.util.AntPathMatcher;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Log4j2
@Service
public class ButtonPermissionServiceImpl extends ServiceImpl<ButtonPermissionMapper, ButtonPermissionEntity>
        implements ButtonPermissionService {

    private final AntPathMatcher pathMatcher = new AntPathMatcher();
    private final Map<String, ButtonPermissionEntity> permissionCache = new ConcurrentHashMap<>();

    @Override
    public ButtonPermissionEntity createButtonPermission(ButtonPermissionEntity permission) {
        this.baseMapper.insert(permission);
        return permission;
    }

    @Override
    public void updateButtonPermission(ButtonPermissionEntity permission) {
        this.baseMapper.updateById(permission);
        // 清除缓存
        permissionCache.clear();
    }

    @Override
    public void deleteButtonPermission(Long id) {
        ButtonPermissionEntity entity = findById(id);
        entity.setDeleted(1);
        updateById(entity);
        this.baseMapper.deleteById(entity);
        // 清除缓存
        permissionCache.clear();
    }

    @Override
    public ButtonPermissionEntity findById(Long id) {
        return this.baseMapper.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("按钮权限不存在"));
    }

    @Override
    public List<ButtonPermissionEntity> findAll() {
        return this.baseMapper.selectList(new QueryWrapper<ButtonPermissionEntity>()
                .eq("deleted", 0)
                .orderByAsc("sequence"));
    }

    @Override
    public boolean checkPermission(String url, String method, Collection<? extends GrantedAuthority> authorities) {
        // 如果没有权限信息，直接返回false
        if (authorities == null || authorities.isEmpty()) {
            return false;
        }

        // 遍历用户的所有权限
        for (GrantedAuthority authority : authorities) {
            String permissionCode = authority.getAuthority();

            // 从缓存中获取权限信息
            ButtonPermissionEntity permission = permissionCache.computeIfAbsent(
                    permissionCode,
                    key -> this.baseMapper.findByCode(key)
                            .orElse(null)
            );

            // 如果找到匹配的权限
            if (permission != null &&
                    permission.getStatus() == 1 &&
                    method.equalsIgnoreCase(permission.getMethod()) &&
                    pathMatcher.match(permission.getUrlPattern(), url)) {
                return true;
            }
        }

        return false;
    }
} 