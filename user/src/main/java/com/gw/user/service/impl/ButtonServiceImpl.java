package com.gw.user.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gw.common.exception.EntityNotFoundException;
import com.gw.user.entity.ButtonEntity;
import com.gw.user.mapper.ButtonMapper;
import com.gw.user.service.ButtonService;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@Log4j2
@RequiredArgsConstructor
public class ButtonServiceImpl extends ServiceImpl<ButtonMapper, ButtonEntity> implements ButtonService {

    @Override
    public List<ButtonEntity> findByMenuId(Long menuId) {
        return this.baseMapper.findByMenuId(menuId);
    }

    @Override
    @Transactional
    public ButtonEntity createButton(ButtonEntity button) {
        this.baseMapper.insert(button);
        return button;
    }

    @Override
    @Transactional
    public void updateButton(ButtonEntity button) {
        this.baseMapper.updateById(button);
    }

    @Override
    @Transactional
    public void deleteButton(Long id) {
        ButtonEntity button = findById(id);
        button.setDeleted(1);
        updateById(button);
    }

    @Override
    public ButtonEntity findById(Long id) {
        return this.baseMapper.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("按钮权限不存在"));
    }

    @Override
    public List<ButtonEntity> findAll() {
        return this.baseMapper.selectList(new QueryWrapper<ButtonEntity>()
                .eq("deleted", 0)
                .orderByAsc("sequence"));
    }
} 