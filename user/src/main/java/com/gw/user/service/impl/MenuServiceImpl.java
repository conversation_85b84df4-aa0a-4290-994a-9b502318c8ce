package com.gw.user.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gw.common.exception.EntityNotFoundException;
import com.gw.user.entity.ButtonEntity;
import com.gw.user.entity.MenuEntity;
import com.gw.user.mapper.MenuMapper;
import com.gw.user.service.ButtonService;
import com.gw.user.service.MenuService;
import com.gw.user.vo.MenuVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@Service
@Log4j2
@RequiredArgsConstructor
public class MenuServiceImpl extends ServiceImpl<MenuMapper, MenuEntity> implements MenuService {

    private final ButtonService buttonService;

    @Override
    public List<MenuVO> getMenuTree() {
        List<MenuEntity> allMenus = this.baseMapper.findAll();
        return buildMenuTree(allMenus, null);
    }

    private List<MenuVO> buildMenuTree(List<MenuEntity> allMenus, Long parentId) {
        return allMenus.stream()
                .filter(menu -> (parentId == null && menu.getParentId() == null) ||
                        (parentId != null && parentId.equals(menu.getParentId())))
                .map(menu -> {
                    MenuVO vo = new MenuVO();
                    vo.setId(menu.getId());
                    vo.setLabel(menu.getName());
                    vo.setAuth(menu.getCode());

                    // 获取子菜单
                    List<MenuVO> children = buildMenuTree(allMenus, menu.getId());

                    // 如果没有子菜单，则获取按钮权限作为子节点
                    if (children.isEmpty()) {
                        List<ButtonEntity> buttons = buttonService.findByMenuId(menu.getId());
                        if (!buttons.isEmpty()) {
                            children = buttons.stream()
                                    .map(button -> {
                                        MenuVO buttonVo = new MenuVO();
                                        buttonVo.setId(button.getId());
                                        buttonVo.setLabel(button.getName());
                                        buttonVo.setAuth(button.getCode());
                                        return buttonVo;
                                    })
                                    .collect(Collectors.toList());
                        }
                    }

                    if (!children.isEmpty()) {
                        vo.setChildren(children);
                    }

                    return vo;
                })
                .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public MenuEntity createMenu(MenuEntity menu) {
        this.baseMapper.insert(menu);
        return menu;
    }

    @Override
    @Transactional
    public void updateMenu(MenuEntity menu) {
        this.baseMapper.updateById(menu);
    }

    @Override
    @Transactional
    public void deleteMenu(Long id) {
        MenuEntity menu = findById(id);
        menu.setDeleted(1);
        updateById(menu);
        this.baseMapper.deleteById(menu);
    }

    @Override
    public MenuEntity findById(Long id) {
        return this.baseMapper.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("菜单不存在"));
    }

    @Override
    public List<MenuEntity> findByParentId(Long parentId) {
        return this.baseMapper.findByParentId(parentId);
    }
} 