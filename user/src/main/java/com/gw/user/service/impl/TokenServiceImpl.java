package com.gw.user.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gw.user.entity.TokenEntity;
import com.gw.user.mapper.TokenMapper;
import com.gw.user.service.TokenService;
import lombok.extern.log4j.Log4j2;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Service
@Log4j2
public class TokenServiceImpl extends ServiceImpl<TokenMapper, TokenEntity> implements TokenService {
    @Override
    @Cacheable(value = "token", key = "#token")
    public Optional<TokenEntity> findByToken(String token) {
        return this.baseMapper.findByToken(token);
    }
}
