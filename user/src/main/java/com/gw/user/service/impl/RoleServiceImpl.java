package com.gw.user.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.gw.common.exception.EntityNotFoundException;
import com.gw.user.constant.UserConstant;
import com.gw.user.dto.RoleQueryDto;
import com.gw.user.entity.RoleEntity;
import com.gw.user.entity.UserRoleEntity;
import com.gw.user.mapper.RoleMapper;
import com.gw.user.service.RoleService;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Log4j2
@Service
@RequiredArgsConstructor
public class RoleServiceImpl extends ServiceImpl<RoleMapper, RoleEntity> implements RoleService {

    private static final String CACHE_NAME = "role";
    private final BaseServiceImpl baseService;
    private final RoleMapper roleMapper;

    @Override
    public List<RoleEntity> findAll() {
        return this.baseMapper.selectList(createBaseQuery());
    }

    @Override
    @Transactional
    @CacheEvict(cacheNames = CACHE_NAME, allEntries = true)
    public void updateStatus(RoleEntity role, int status) {
        this.baseMapper.updateRoleStatus(role, status);
    }

    @Override
    @Transactional
    @CacheEvict(cacheNames = CACHE_NAME, allEntries = true)
    public void delete(RoleEntity role) {
        role.setDeleted(1);
        this.baseMapper.updateById(role);
        this.baseMapper.deleteById(role);
    }

    @Override
    @Cacheable(value = CACHE_NAME, key = "'findById:' + #roleId", unless = "#result == null")
    public RoleEntity findById(long roleId) {
        return this.baseMapper.findById(roleId)
                .orElseThrow(() -> new EntityNotFoundException("角色不存在"));
    }

    @Cacheable(value = CACHE_NAME, key = "'findByCode:' + #code", unless = "#result == null")
    @Override
    public RoleEntity findByCode(String code) {
        return this.baseMapper.findByCode(code)
                .orElseThrow(() -> new EntityNotFoundException("角色不存在"));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(cacheNames = CACHE_NAME, allEntries = true)
    public long insert(RoleEntity role) {
        this.baseMapper.insert(role);
        if (role.getCode() == null || role.getCode().isEmpty()) {
            role.setCode("ROLE_" + role.getId());
            this.baseMapper.updateById(role);
        }
        return role.getId();
    }

    @Override
    @Transactional
    @CacheEvict(cacheNames = CACHE_NAME, allEntries = true)
    public long update(RoleEntity role) {
        this.baseMapper.updateById(role);
        return role.getId();
    }

    @Override
    @Transactional
    @CacheEvict(cacheNames = CACHE_NAME, allEntries = true)
    public void updateRoleAuth(RoleEntity role) {
        this.baseMapper.updateById(role);
    }

    @Override
    public PageInfo<RoleEntity> page(int pageNum, int pageSize, RoleQueryDto query) {
        PageHelper.startPage(pageNum, pageSize);
        List<RoleEntity> list = this.baseMapper.selectList(buildQueryWrapper(query));
        fillRoleDetails(list);
        return new PageInfo<>(list);
    }

    @Override
    public Map<Long, List<Long>> findAllRoleIdsGroupByUserId() {
        return this.baseMapper.findAllUserRoles().stream()
                .collect(Collectors.groupingBy(
                        UserRoleEntity::getUserId,
                        Collectors.mapping(UserRoleEntity::getRoleId, Collectors.toList())));
    }

    @Override
    public void assignAPPRolesToUser(Long userId) {
        // 获取默认角色（这里假设ID为1的角色是默认用户角色）
        RoleEntity defaultRole = this.findByCode(UserConstant.APP_USER_ROLE_CODE);
        if (defaultRole != null) {
            roleMapper.insertUserReRole(userId, defaultRole.getId());
        }
    }

    private QueryWrapper<RoleEntity> createBaseQuery() {
        QueryWrapper<RoleEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("deleted", 0);
        return queryWrapper;
    }

    private QueryWrapper<RoleEntity> buildQueryWrapper(RoleQueryDto query) {
        QueryWrapper<RoleEntity> queryWrapper = createBaseQuery();

        if (query != null) {
            if (StringUtils.isNotBlank(query.getQueryValue())) {
                queryWrapper.and(wrapper -> wrapper
                        .like("name", query.getQueryValue())
                        .or()
                        .like("remarks", query.getQueryValue()));
            }
        }

        queryWrapper.orderByDesc("update_time");
        return queryWrapper;
    }

    private void fillRoleDetails(List<RoleEntity> entities) {
        baseService.fillBaseEntityName(entities);
    }
}
