package com.gw.user.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gw.user.entity.MenuEntity;
import com.gw.user.vo.MenuVO;

import java.util.List;

public interface MenuService extends IService<MenuEntity> {
    List<MenuVO> getMenuTree();

    MenuEntity createMenu(MenuEntity menu);

    void updateMenu(MenuEntity menu);

    void deleteMenu(Long id);

    MenuEntity findById(Long id);

    List<MenuEntity> findByParentId(Long parentId);
} 