package com.gw.user.service;

import com.gw.user.entity.ButtonPermissionEntity;
import org.springframework.security.core.GrantedAuthority;

import java.util.Collection;
import java.util.List;

public interface ButtonPermissionService {
    ButtonPermissionEntity createButtonPermission(ButtonPermissionEntity permission);

    void updateButtonPermission(ButtonPermissionEntity permission);

    void deleteButtonPermission(Long id);

    ButtonPermissionEntity findById(Long id);

    List<ButtonPermissionEntity> findAll();

    boolean checkPermission(String url, String method, Collection<? extends GrantedAuthority> authorities);
} 