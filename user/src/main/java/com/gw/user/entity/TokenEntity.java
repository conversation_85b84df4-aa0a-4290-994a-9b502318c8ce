package com.gw.user.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;

@Getter
@Setter
@Builder
@TableName("a_token")
public class TokenEntity implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    @TableId
    private Long id;
    private String accessToken;
    private String refreshToken;
    private Long userId;
    private boolean expired;
    private boolean revoked;
    //    主要用来区分移动端、PC端、小程序等的登录类型
    private String device;
}
