package com.gw.user.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gw.common.entity.BaseEntity;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@TableName("a_button")
public class ButtonEntity extends BaseEntity {
    private Long menuId;
    private String name;
    private String code;
    private Integer sequence;
    private Integer status;

    @TableField(exist = false)
    private MenuEntity menu;

}
