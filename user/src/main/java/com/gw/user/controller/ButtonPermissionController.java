package com.gw.user.controller;

import com.gw.common.dto.ItemIdDTO;
import com.gw.common.dto.ResponseResult;
import com.gw.user.entity.ButtonPermissionEntity;
import com.gw.user.service.ButtonPermissionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequiredArgsConstructor
@RequestMapping("/api/v1/auth/button-permission")
@Tag(name = "按钮权限管理", description = "按钮权限相关操作")
public class ButtonPermissionController {

    private final ButtonPermissionService buttonPermissionService;

    @PostMapping
    @Operation(summary = "创建按钮权限")
    public ResponseResult<ButtonPermissionEntity> create(@RequestBody ButtonPermissionEntity permission) {
        return ResponseResult.success(buttonPermissionService.createButtonPermission(permission));
    }

    @PostMapping("/update")
    @Operation(summary = "更新按钮权限")
    public ResponseResult<?> update(@RequestBody ButtonPermissionEntity permission) {
        buttonPermissionService.updateButtonPermission(permission);
        return ResponseResult.success(null);
    }

    @PostMapping("/delete")
    @Operation(summary = "删除按钮权限")
    public ResponseResult<?> delete(@RequestBody ItemIdDTO req) {
        buttonPermissionService.deleteButtonPermission(req.getId());
        return ResponseResult.success(null);
    }

    @GetMapping("/list")
    @Operation(summary = "获取按钮权限列表")
    public ResponseResult<List<ButtonPermissionEntity>> list() {
        return ResponseResult.success(buttonPermissionService.findAll());
    }

    @PostMapping("/get")
    @Operation(summary = "获取按钮权限详情")
    public ResponseResult<ButtonPermissionEntity> getById(@RequestBody ItemIdDTO req) {
        return ResponseResult.success(buttonPermissionService.findById(req.getId()));
    }
} 