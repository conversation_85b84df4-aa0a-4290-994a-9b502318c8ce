package com.gw.user.controller;

import com.gw.common.dto.ResponseResult;
import com.gw.common.user.context.UserContextUtil;
import com.gw.user.entity.InvitationEntity;
import com.gw.user.entity.UserEntity;
import com.gw.user.service.InvitationService;
import com.gw.user.vo.InviteCodeVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/v1/invitation")
@RequiredArgsConstructor
@Tag(name = "邀请模块", description = "邀请相关操作")
@Log4j2
public class InvitationController {

    private final InvitationService invitationService;

    @Operation(summary = "生成邀请码", responses = {
            @ApiResponse(responseCode = "200", description = "生成成功")
    })
    @PostMapping("/code")
    public ResponseResult<InviteCodeVO> generateInviteCode() {
        UserEntity user = (UserEntity) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        String inviteCode = invitationService.generateInviteCode(user.getId());

        return ResponseResult.success(InviteCodeVO.builder()
                .inviteCode(inviteCode)
                .inviteLink("https://yourapp.com/register?inviteCode=" + inviteCode)
                .build());
    }

    @Operation(summary = "获取邀请记录", responses = {
            @ApiResponse(responseCode = "200", description = "获取成功")
    })
    @GetMapping("/records")
    public ResponseResult<List<InvitationEntity>> getInvitationRecords() {
        Long userId = UserContextUtil.getCurrentUserId();
        List<InvitationEntity> records = invitationService.getUserInvitations(userId);
        return ResponseResult.success(records);
    }

    @Operation(summary = "验证邀请码", responses = {
            @ApiResponse(responseCode = "200", description = "验证成功")
    })
    @GetMapping("/validate")
    public ResponseResult<Boolean> validateInviteCode(@RequestParam String inviteCode) {
        InvitationEntity invitation = invitationService.validateInviteCode(inviteCode);
        return ResponseResult.success(invitation != null);
    }
}