package com.gw.user.controller;

import com.gw.common.dto.ResponseResult;
import com.gw.common.exception.ErrorRequestException;
import com.gw.user.constant.UserConstant;
import com.gw.user.dto.LoginParam;
import com.gw.user.dto.WxLoginBaseParam;
import com.gw.user.dto.WxLoginParam;
import com.gw.user.service.AuthenticationService;
import com.gw.user.vo.LoginCaptcha;
import com.gw.user.vo.LoginInfo;
import com.gw.user.vo.LoginWXInfo;
import com.wf.captcha.SpecCaptcha;
import com.wf.captcha.base.Captcha;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;

import java.awt.*;
import java.io.IOException;
import java.time.Duration;
import java.util.UUID;

@RestController
@RequestMapping("/api/v1/auth/login")
@RequiredArgsConstructor
@Tag(name = "登录模块", description = "登录相关操作")
@Log4j2
public class LoginController {
    private final AuthenticationService service;
    private final String CAPTCHA_PREFIX_KEY = "ca:auth:captchaId:";
    @Resource
    RedisTemplate<String, Object> redisTemplate;

    private LoginInfo login(LoginParam loginRequest, String device) {
        String code = redisTemplate.opsForValue().get(CAPTCHA_PREFIX_KEY + loginRequest.getCaptchaId()) == null ? null
                : redisTemplate.opsForValue().get(CAPTCHA_PREFIX_KEY + loginRequest.getCaptchaId()).toString();
        redisTemplate.delete(CAPTCHA_PREFIX_KEY + loginRequest.getCaptchaId());
        if (code == null || !code.equals(loginRequest.getCaptchaCode())) {
            throw new ErrorRequestException("验证码错误");
        }
        loginRequest.setDevice(device);
        return service.authenticate(loginRequest);
    }

    @Operation(summary = "登录接口", responses = {
            @ApiResponse(responseCode = "200", description = "成功")
    })
    @PostMapping("")
    public ResponseResult<LoginInfo> webLogin(@RequestBody LoginParam loginRequest) {

        return ResponseResult.success(login(loginRequest, UserConstant.WEB_LOGIN));
    }

    @Operation(summary = "App登录接口", responses = {
            @ApiResponse(responseCode = "200", description = "成功")
    })
    @PostMapping("app")
    public ResponseResult<LoginInfo> appLogin(@Valid @RequestBody LoginParam loginRequest) {
        return ResponseResult.success(login(loginRequest, UserConstant.APP_LOGIN));
    }

    @Operation(summary = "微信登录接口", responses = {
            @ApiResponse(responseCode = "200", description = "成功")
    })
    @PostMapping("wx")
    public ResponseResult<LoginInfo> wxLogin(@Valid @RequestBody LoginParam loginRequest) {
        return ResponseResult.success(login(loginRequest, UserConstant.WX_LOGIN));
    }

    @Operation(summary = "获取验证码ID", responses = {
            @ApiResponse(responseCode = "200", description = "成功")
    })
    @GetMapping("/captchaid")
    public ResponseResult<LoginCaptcha> code() {
        var code = UUID.randomUUID().toString();
        redisTemplate.opsForValue().set(CAPTCHA_PREFIX_KEY + code, "", Duration.ofMinutes(1));
        return ResponseResult.success(new LoginCaptcha(code));
    }

    @Operation(summary = "通过验证码ID获取验证码", responses = {
            @ApiResponse(responseCode = "200", description = "成功")
    })
    @GetMapping("/captcha")
    public void getCaptchaImage(@RequestParam("captchaid") String captchaid, HttpServletResponse httpServletResponse)
            throws IOException {

        if (redisTemplate.opsForValue().get(CAPTCHA_PREFIX_KEY + captchaid) == null) {
            throw new ErrorRequestException("没有获取验证码ID或者验证码ID失效");
        }
        // 三个参数分别为宽、高、位数
        SpecCaptcha specCaptcha = new SpecCaptcha(130, 48, 5);
        // 设置字体
        specCaptcha.setFont(new Font("Verdana", Font.PLAIN, 32)); // 有默认字体，可以不用设置
        // 设置类型，纯数字、纯字母、字母数字混合
        specCaptcha.setCharType(Captcha.TYPE_ONLY_NUMBER);
        // 获取验证码
        redisTemplate.opsForValue().set(CAPTCHA_PREFIX_KEY + captchaid, specCaptcha.text(), Duration.ofMinutes(1));

        // 将BufferedImage转换为字节数组
        httpServletResponse.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
        httpServletResponse.setHeader("Pragma", "no-cache");
        httpServletResponse.setDateHeader("Expires", 0);
        httpServletResponse.setContentType("image/png");
        // 输出图片流
        specCaptcha.out(httpServletResponse.getOutputStream());

    }
    @Operation(summary = "微信小程序一键登录接口(不获取手机号)", responses = {
            @ApiResponse(responseCode = "200", description = "成功")
    })
    @PostMapping("/wx/miniapp_no_phone")
    public ResponseResult<LoginWXInfo> wxMiniAppLoginWithoutPhone(@Valid @RequestBody WxLoginBaseParam loginRequest) {
        log.info(" 微信登录参数不获取电话的登录 {}", loginRequest.toString());
        return ResponseResult.success(service.wxMiniAppLoginWithoutPhone(loginRequest));
    }
    @Operation(summary = "微信小程序一键登录接口", responses = {
            @ApiResponse(responseCode = "200", description = "成功")
    })
    @PostMapping("/wx/miniapp")
    public ResponseResult<LoginWXInfo> wxMiniAppLogin(@Valid @RequestBody WxLoginParam loginRequest) {
        log.info("微信登录参数{}", loginRequest.toString());
        return ResponseResult.success(service.wxMiniAppLogin(loginRequest));
    }
}
