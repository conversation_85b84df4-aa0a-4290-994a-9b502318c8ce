package com.gw.user.config.properties;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Setter
@ConfigurationProperties(prefix = "security.cors")
public class SecurityCorsProperties {
    /**
     * 允许的源站
     */
    private List<String> allowedOrigins = new ArrayList<>();

    /**
     * 允许的HTTP方法
     */
    private List<String> allowedMethods = new ArrayList<>();

    /**
     * 允许的请求头
     */
    private List<String> allowedHeaders = new ArrayList<>();

    /**
     * 暴露的响应头
     */
    private List<String> exposedHeaders = new ArrayList<>();

    /**
     * 是否允许携带凭证
     */
    @Getter
    private boolean allowCredentials = false;

    /**
     * 预检请求的有效期,单位秒
     */
    @Getter
    private long maxAge = 3600;

    public List<String> getAllowedOrigins() {
        if (allowedOrigins == null || allowedOrigins.isEmpty()) {
            return Collections.singletonList("*");
        }
        return allowedOrigins;
    }

    public List<String> getAllowedMethods() {
        if (allowedMethods == null || allowedMethods.isEmpty()) {
            return Collections.singletonList("*");
        }
        return allowedMethods;
    }

    public List<String> getAllowedHeaders() {
        if (allowedHeaders == null || allowedHeaders.isEmpty()) {
            return Collections.singletonList("*");
        }
        return allowedHeaders;
    }

    public List<String> getExposedHeaders() {
        if (exposedHeaders == null || exposedHeaders.isEmpty()) {
            return Collections.singletonList("Authorization");
        }
        return exposedHeaders;
    }
}