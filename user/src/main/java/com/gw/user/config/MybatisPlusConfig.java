package com.gw.user.config;

import com.github.pagehelper.PageInterceptor;
import org.apache.ibatis.type.ArrayTypeHandler;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;

import java.util.Properties;

@Configuration
public class MybatisPlusConfig {
    @Bean
    @Order(2)
    public org.apache.ibatis.session.Configuration configuration() {
        org.apache.ibatis.session.Configuration configuration = new org.apache.ibatis.session.Configuration();
        // 启用延迟加载
        configuration.setLazyLoadingEnabled(true);
        configuration.setAggressiveLazyLoading(true);
        configuration.getTypeHandlerRegistry().register(ArrayTypeHandler.class);
        // 设置其他 MyBatis 配置...
        return configuration;
    }

    @Bean
    @Order(1)
    public PageInterceptor pageInterceptor() {
        PageInterceptor pageInterceptor = new PageInterceptor();
        Properties properties = new Properties();

        // 最小化配置，只保留关键参数
        properties.setProperty("helperDialect", "postgresql");
        properties.setProperty("reasonable", "false");
        properties.setProperty("pageSizeZero", "true");

        pageInterceptor.setProperties(properties);
        return pageInterceptor;
    }
} 