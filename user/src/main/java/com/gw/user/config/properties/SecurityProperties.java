package com.gw.user.config.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Data
@Component
@ConfigurationProperties(prefix = "application.security")
public class SecurityProperties {
    /**
     * 公开访问路径
     */
    private List<String> publicPaths = new ArrayList<>();

    /**
     * CORS配置
     */
    private SecurityCorsProperties cors = new SecurityCorsProperties();


    /**
     * 内部服务列表
     */
    private List<String> internalServices = new ArrayList<>();
} 