package com.gw.user.config;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.J<PERSON><PERSON><PERSON>er;
import com.alibaba.fastjson2.JSONWriter;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.gw.common.util.DateTimeUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.caffeine.CaffeineCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.RedisSerializationContext;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.data.redis.serializer.SerializationException;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.springframework.util.StringUtils;

import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Configuration
@EnableCaching
@EnableConfigurationProperties(CacheProperties.class)
public class RedisConfig {

    @Autowired
    private CacheProperties cacheProperties;

    @Bean
    public RedisTemplate<String, Object> redisTemplate(RedisConnectionFactory factory) {
        RedisTemplate<String, Object> template = new RedisTemplate<>();
        template.setConnectionFactory(factory);

        // 使用StringRedisSerializer来序列化和反序列化redis的key值
        StringRedisSerializer stringRedisSerializer = new StringRedisSerializer();
        template.setKeySerializer(stringRedisSerializer);
        template.setHashKeySerializer(stringRedisSerializer);

        // 使用FastJson2序列化器来序列化和反序列化redis的value值
        FastJson2RedisSerializer<Object> fastJson2RedisSerializer = new FastJson2RedisSerializer<>(Object.class);
        template.setValueSerializer(fastJson2RedisSerializer);
        template.setHashValueSerializer(fastJson2RedisSerializer);

        template.afterPropertiesSet();
        return template;
    }

    @Bean
    @Primary
    public CacheManager cacheManager() {
        CaffeineCacheManager cacheManager = new CaffeineCacheManager();

        // 设置默认的缓存配置
        Caffeine<Object, Object> caffeine = Caffeine.newBuilder()
                .expireAfterWrite(10, TimeUnit.MINUTES)  // 默认过期时间
                .initialCapacity(100)                   // 初始容量
                .maximumSize(10000);                     // 最大容量

        cacheManager.setCaffeine(caffeine);

        // 如果有自定义缓存配置，可以设置为自定义的缓存
        if (cacheProperties.getConfigs() != null) {
            for (CacheProperties.CacheConfig config : cacheProperties.getConfigs()) {
                String cacheName = config.getName();


                // 使用CacheProperties中的方法获取Duration
                java.time.Duration duration = cacheProperties.getCacheExpiration(config.getName());

                // 添加到缓存管理器的已知缓存名称中
                cacheManager.registerCustomCache(cacheName,
                        Caffeine.newBuilder()
                                .expireAfterWrite(duration.toMillis(), TimeUnit.MILLISECONDS)
                                .initialCapacity(100)
                                .maximumSize(10000)
                                .build());
            }
        }

        return cacheManager;
    }

    // 保留原来的 Redis CacheManager 用于可能的情况下需要使用 Redis 缓存
    @Bean(name = "redisCacheManager")
    public CacheManager redisCacheManager(RedisConnectionFactory factory) {
        // 创建FastJson2序列化器
        FastJson2RedisSerializer<Object> fastJson2RedisSerializer = new FastJson2RedisSerializer<>(Object.class);

        // 默认配置
        RedisCacheConfiguration defaultConfig = RedisCacheConfiguration.defaultCacheConfig()
                .serializeKeysWith(RedisSerializationContext.SerializationPair.fromSerializer(new StringRedisSerializer()))
                .serializeValuesWith(RedisSerializationContext.SerializationPair.fromSerializer(fastJson2RedisSerializer))
                .disableCachingNullValues();

        // 添加前缀
        if (StringUtils.hasText(cacheProperties.getPrefix())) {
            defaultConfig = defaultConfig.prefixCacheNameWith(cacheProperties.getPrefix() + ":");
        }

        // 配置缓存TTL
        Map<String, RedisCacheConfiguration> configMap = new HashMap<>();
        if (cacheProperties.getConfigs() != null) {
            for (CacheProperties.CacheConfig config : cacheProperties.getConfigs()) {
                configMap.put(config.getName(),
                        defaultConfig.entryTtl(DateTimeUtils.parseDuration(config.getExpireAfterWrite())));
            }
        }

        return RedisCacheManager.builder(factory)
                .cacheDefaults(defaultConfig)
                .withInitialCacheConfigurations(configMap)
                .build();
    }

    // FastJson2序列化器
    public static class FastJson2RedisSerializer<T> implements RedisSerializer<T> {
        private static final Charset DEFAULT_CHARSET = StandardCharsets.UTF_8;
        private final Class<T> clazz;

        public FastJson2RedisSerializer(Class<T> clazz) {
            this.clazz = clazz;
        }

        @Override
        public byte[] serialize(T t) throws SerializationException {
            if (t == null) {
                return new byte[0];
            }
            try {
                return JSON.toJSONBytes(t, JSONWriter.Feature.WriteClassName);
            } catch (Exception ex) {
                throw new SerializationException("Could not serialize: " + ex.getMessage(), ex);
            }
        }

        @Override
        public T deserialize(byte[] bytes) throws SerializationException {
            if (bytes == null || bytes.length <= 0) {
                return null;
            }
            try {
                return JSON.parseObject(bytes, clazz, JSONReader.Feature.SupportAutoType);
            } catch (Exception ex) {
                throw new SerializationException("Could not deserialize: " + ex.getMessage(), ex);
            }
        }
    }
}