package com.gw.user.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Pattern;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@Schema(description = "创建用户的请求")
public class UserSubmitBaseDto {

    @Schema(description = "真实姓名")
    private String realName;
    @Pattern(regexp = "^\\d{11}$", message = "手机号码格式不正确")
    @Schema(description = "手机号")
    private String phone;
    @Schema(description = "照片地址")
    private String photo;
    @Schema(description = "关联角色ID")
    private List<Long> roles;
}

