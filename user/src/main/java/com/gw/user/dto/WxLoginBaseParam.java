package com.gw.user.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

@Data
public class WxLoginBaseParam {
    @NotBlank(message = "code不能为空")
    @Schema(description = "微信登录code")
    private String openIdCode;

    @Schema(description = "邀请码")
    private String inviteCode;
    @Schema(description = "用户信息加密数据，非必须")
    private String encryptedData;

    @Schema(description = "加密算法的初始向量，非必须")
    private String iv;

    public String toString() {
        return "WxLoginParam{" +
                "openIdCode='" + openIdCode + '\'' +
                ", inviteCode='" + inviteCode + '\'' +
                ", encryptedData='" + encryptedData + '\'' +
                ", iv='" + iv + '\'' +
                '}';
    }
}
