package com.gw.user.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Pattern;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Schema(description = "修改密码的请求")
public class ChangeSelfPasswordDto {
    @Schema(description = "旧密码")
    private String oldPassword;

    @Schema(description = "新密码")
    @Pattern(regexp = "^(?=.*[a-zA-Z])(?=.*\\d)[a-zA-Z\\d]{8,}$", message = "密码必须至少包含字母、数字中的两种，且长度不小于8位")
    private String newPassword;
}