package com.gw.user.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "登录请求参数")
public class LoginParam {
    @NotBlank(message = "用户名或者密码错误")
    @Size(min = 3, max = 50, message = "用户名长度必须在3到50个字符之间")
    @Schema(description = "用户名")
    private String username;
    @NotBlank(message = "用户名或者密码错误")
    @Schema(description = "密码")
    private String password;
    @NotBlank(message = "验证码错误")
    @Schema(description = "验证码ID")
    private String captchaId;
    @NotBlank(message = "验证码错误")
    @Schema(description = "验证码")
    private String captchaCode;
    @JsonIgnore
    private String device;
}
