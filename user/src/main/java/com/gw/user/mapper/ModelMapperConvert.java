package com.gw.user.mapper;

import com.gw.user.entity.UserEntity;
import com.gw.user.vo.UserVo;
import org.modelmapper.ModelMapper;
import org.modelmapper.convention.MatchingStrategies;

public class ModelMapperConvert {
    private static final ModelMapper USER_MODEL_MAPPER = createUserModelMapper();
    private static final ModelMapper BASE_MODEL_MAPPER = new ModelMapper();

    public static ModelMapper getBaseModelMapper() {
        return BASE_MODEL_MAPPER;
    }

    private static ModelMapper createUserModelMapper() {
        ModelMapper modelMapper = new ModelMapper();
        modelMapper.getConfiguration()
                .setMatchingStrategy(MatchingStrategies.STRICT)
                .setPropertyCondition(context -> !(
                        context.getSource() instanceof UserEntity &&
                                (context.getDestination() instanceof UserVo)
                ));
        return modelMapper;
    }

    public static ModelMapper getUserModelMapper() {
        return USER_MODEL_MAPPER;
    }

    public static UserVo convertToUserVo(UserEntity entity) {
        return USER_MODEL_MAPPER.map(entity, UserVo.class);
    }
}
