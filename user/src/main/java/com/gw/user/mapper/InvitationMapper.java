package com.gw.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gw.user.entity.InvitationEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface InvitationMapper extends BaseMapper<InvitationEntity> {

    /**
     * 根据邀请码查询邀请记录
     *
     * @param inviteCode 邀请码
     * @return 邀请记录
     */
    @Select("SELECT * FROM t_invitation WHERE invite_code = #{inviteCode} AND status = 0")
    InvitationEntity findByInviteCode(@Param("inviteCode") String inviteCode);

    /**
     * 查询用户的邀请记录
     *
     * @param inviterId 邀请人ID
     * @return 邀请记录列表
     */
    @Select("SELECT * FROM t_invitation WHERE inviter_id = #{inviterId}")
    List<InvitationEntity> findByInviterId(@Param("inviterId") Long inviterId);
}