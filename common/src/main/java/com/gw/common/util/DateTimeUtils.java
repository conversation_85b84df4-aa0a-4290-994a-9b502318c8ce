package com.gw.common.util;

import java.time.Duration;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class DateTimeUtils {

    public final static String CHINESE_MONTH_DAY_REGEX = "(\\d{1,2})月(\\d{1,2})日";

    public static LocalDateTime[] parseTimeRange(String timeRangeStr, LocalDateTime baseDateTime) {
        // Normalize the time format
        timeRangeStr = timeRangeStr.replaceAll("\\.|，|：", ":");

        // Split the time range string
        String[] times = timeRangeStr.split("～");
        if (times.length != 2) {
            return null;
        }

        // Parse the start time
        LocalDateTime startTime = parseTime(times[0], baseDateTime);

        // Parse the end time, considering the "次" keyword
        LocalDateTime endTime;
        if (times[1].startsWith("次")) {
            endTime = parseTime(times[1].substring(1), baseDateTime.plusDays(1));
        } else {
            endTime = parseTime(times[1], baseDateTime);
        }

        return new LocalDateTime[]{startTime, endTime};
    }

    private static LocalDateTime parseTime(String timeStr, LocalDateTime baseDateTime) {
        Pattern pattern = Pattern.compile("(\\d{1,2}):(\\d{1,2})");
        Matcher matcher = pattern.matcher(timeStr);

        if (matcher.find()) {
            // Extract hours and minutes
            String hour = matcher.group(1);
            String minute = matcher.group(2);

            // Add hours and minutes to the base LocalDateTime object
            try {
                return baseDateTime.withHour(Integer.parseInt(hour)).withMinute(Integer.parseInt(minute));
            } catch (NumberFormatException e) {
                throw new IllegalArgumentException("时间格式不正确: " + timeStr);
            }
        } else {
            throw new IllegalArgumentException("时间格式不正确: " + timeStr);
        }
    }

    public static LocalDateTime convertDate(String dateStr, String regex) {
        // 定义正则表达式匹配 x月x日 格式
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(dateStr);

        if (matcher.find()) {
            // 提取月份和日期
            String month = matcher.group(1);
            String day = matcher.group(2);

            // 获取当前年份
            int year = LocalDateTime.now().getYear();

            // 构建日期字符串
            String dateWithYear = String.format("%d-%02d-%02d", year, Integer.parseInt(month), Integer.parseInt(day));

            // 解析并返回 LocalDateTime 对象
            try {
                return LocalDateTime.parse(dateWithYear + "T00:00:00", DateTimeFormatter.ISO_LOCAL_DATE_TIME);
            } catch (DateTimeParseException e) {
                throw new IllegalArgumentException("日期格式不正确: " + dateWithYear);
            }
        } else {
            throw new IllegalArgumentException("输入的日期格式不正确: " + dateStr);
        }
    }

    public static LocalDateTime convertAndAddTime(String timeStr, LocalDateTime dateTime) {
        // 定义正则表达式匹配 x:x 格式
        timeStr = timeStr.trim().replaceAll("[.，：;；]", ":");

        Pattern pattern = Pattern.compile("(\\d{1,2}):(\\d{1,2})");
        Matcher matcher = pattern.matcher(timeStr);

        if (matcher.find()) {
            // 提取小时和分钟
            String hour = matcher.group(1);
            String minute = matcher.group(2);

            // 将小时和分钟加到当前的 LocalDateTime 对象中
            try {
                return dateTime.withHour(Integer.parseInt(hour)).withMinute(Integer.parseInt(minute));
            } catch (DateTimeParseException e) {
                throw new IllegalArgumentException("时间格式不正确: " + timeStr);
            }
        } else {
            throw new IllegalArgumentException("输入的时间格式不正确: " + timeStr);
        }
    }

    public static String convertTimeToString(LocalDateTime time, String formate) {
        return time.format(DateTimeFormatter.ofPattern(formate));
    }

    public static Duration parseDuration(String duration) {
        if (duration == null || duration.isEmpty()) {
            return Duration.ofMinutes(10);
        }
        if (duration.endsWith("m")) {
            long interval = Long.parseLong(duration.replace("m", ""));
            return Duration.ofMinutes(interval);
        } else if (duration.endsWith("h")) {
            long interval = Long.parseLong(duration.replace("h", ""));
            return Duration.ofHours(interval);
        } else if (duration.endsWith("d")) {
            long interval = Long.parseLong(duration.replace("d", ""));
            return Duration.ofDays(interval);
        } else {
            return Duration.ofSeconds(Long.parseLong(duration));
        }
    }

    public static LocalDateTime convertUnixTimestampToLocalDateTime(long unixTimestamp) {
        Instant instant = Instant.ofEpochSecond(unixTimestamp);
        return LocalDateTime.ofInstant(instant, ZoneId.systemDefault());
    }
}
