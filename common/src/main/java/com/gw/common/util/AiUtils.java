package com.gw.common.util;

import java.nio.charset.StandardCharsets;
import java.util.regex.Pattern;

/**
 * AI相关工具类
 * 提供AI模型相关的实用方法
 */
public class AiUtils {

    /**
     * 中文字符正则表达式
     */
    private static final Pattern CHINESE_PATTERN = Pattern.compile("[\u4e00-\u9fa5]");

    /**
     * 英文单词正则表达式
     */
    private static final Pattern ENGLISH_WORD_PATTERN = Pattern.compile("\\b\\w+\\b");

    /**
     * 标点符号和特殊字符正则表达式
     */
    private static final Pattern PUNCTUATION_PATTERN = Pattern.compile("[\\p{Punct}\\s]+");

    /**
     * 计算字符串的近似token数量
     * <p>
     * 计算规则：
     * - 中文字符：每个字符约等于1个token
     * - 英文单词：每个单词约等于1个token
     * - 标点符号和空格：每个约等于0.5个token
     * <p>
     * 注意：这是一个近似计算，实际的token数量可能因模型而异
     *
     * @param text 要计算的文本
     * @return 估算的token数量
     */
    public static int calculateTokenCount(String text) {
        if (text == null || text.isEmpty()) {
            return 0;
        }

        // 统计中文字符数量
        long chineseCount = CHINESE_PATTERN.matcher(text).results().count();

        // 移除中文字符后统计英文单词
        String textWithoutChinese = CHINESE_PATTERN.matcher(text).replaceAll("");
        long englishWordCount = ENGLISH_WORD_PATTERN.matcher(textWithoutChinese).results().count();

        // 移除中文和英文单词后统计标点符号和特殊字符
        String remainingText = ENGLISH_WORD_PATTERN.matcher(textWithoutChinese).replaceAll("");
        long punctuationCount = remainingText.replaceAll("\\s+", "").length();

        // 计算总token数（标点符号按0.5个token计算）
        return (int) (chineseCount + englishWordCount + Math.ceil(punctuationCount * 0.5));
    }

    /**
     * 计算字符串的字节长度（UTF-8编码）
     *
     * @param text 要计算的文本
     * @return UTF-8编码的字节长度
     */
    public static int calculateByteLength(String text) {
        if (text == null) {
            return 0;
        }
        return text.getBytes(StandardCharsets.UTF_8).length;
    }

    /**
     * 根据最大token数截断文本
     *
     * @param text      原始文本
     * @param maxTokens 最大token数
     * @return 截断后的文本
     */
    public static String truncateByTokenCount(String text, int maxTokens) {
        if (text == null || text.isEmpty() || maxTokens <= 0) {
            return "";
        }

        if (calculateTokenCount(text) <= maxTokens) {
            return text;
        }

        // 二分查找最佳截断位置
        int left = 0;
        int right = text.length();
        String result = "";

        while (left <= right) {
            int mid = (left + right) / 2;
            String substring = text.substring(0, mid);
            int tokenCount = calculateTokenCount(substring);

            if (tokenCount <= maxTokens) {
                result = substring;
                left = mid + 1;
            } else {
                right = mid - 1;
            }
        }

        return result;
    }
}