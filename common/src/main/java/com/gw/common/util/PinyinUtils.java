package com.gw.common.util;


import lombok.extern.log4j.Log4j2;
import net.sourceforge.pinyin4j.PinyinHelper;
import net.sourceforge.pinyin4j.format.HanyuPinyinCaseType;
import net.sourceforge.pinyin4j.format.HanyuPinyinOutputFormat;
import net.sourceforge.pinyin4j.format.HanyuPinyinToneType;
import net.sourceforge.pinyin4j.format.exception.BadHanyuPinyinOutputFormatCombination;

@Log4j2
public class PinyinUtils {
    private static final HanyuPinyinOutputFormat FORMAT = new HanyuPinyinOutputFormat();

    static {
        FORMAT.setCaseType(HanyuPinyinCaseType.LOWERCASE);
        FORMAT.setToneType(HanyuPinyinToneType.WITHOUT_TONE);
    }

    public static String getFirstLetter(String chinese) {
        if (chinese == null || chinese.isEmpty()) {
            return "#";
        }
        char firstChar = chinese.charAt(0);
        String[] pinyinArray = new String[0];
        try {
            pinyinArray = PinyinHelper.toHanyuPinyinStringArray(firstChar, FORMAT);
        } catch (BadHanyuPinyinOutputFormatCombination e) {
            throw new RuntimeException(e);
        }

        if (pinyinArray != null && pinyinArray.length > 0) {
            return pinyinArray[0].substring(0, 1).toUpperCase();
        }
        return String.valueOf(firstChar).toUpperCase();
    }
}
