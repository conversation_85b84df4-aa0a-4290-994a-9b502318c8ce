package com.gw.common.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@Schema(description = "分页返回的内容")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PageBaseContentVo<T> {
    private List<T> list = new ArrayList<>();
    @Schema(description = "分页信息")
    private PaginationVo pagination;
}
