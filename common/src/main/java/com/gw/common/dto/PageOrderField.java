package com.gw.common.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Schema(description = "page的排序请求")
@AllArgsConstructor
@NoArgsConstructor  // 添加此注解以生成默认构造函数
public class PageOrderField {
    @Schema(description = "字段")
    private String key = "id";
    @Schema(description = "方向 1 升序，2 降序 ")
    private int direction = 2;

    @Override
    public String toString() {
        return key + " " + direction;
    }
}
