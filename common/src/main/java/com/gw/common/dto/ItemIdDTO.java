package com.gw.common.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Positive;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Schema(description = "实例ID")
@AllArgsConstructor
@NoArgsConstructor
public class ItemIdDTO {
    @Schema(description = "实例ID")
    @Positive(message = "实例ID必须大于0")
    private long id = 0;
}
