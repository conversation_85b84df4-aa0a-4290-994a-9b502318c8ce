package com.gw.common.membership.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 会员权益DTO 主要微服务之间调用的redis使用
 */
@Data
@Schema(description = "会员权益VO")
public class MembershipBenefitBaseVO {
    @Schema(description = "权益ID")
    private Long id;
    @JsonProperty("c")
    @Schema(description = "权益代码")
    private String code;
    @Schema(description = "权益类型: 1-功能权益, 2-资源权益, 3-服务权益")
    @JsonProperty("t")
    private Integer type;
    @Schema(description = "权益状态: 0-禁用, 1-启用")
    @JsonProperty("st")
    private Integer status;
    @Schema(description = "权益详情")
    @JsonProperty("dt")
    private BenefitDetailBaseVO details;

}