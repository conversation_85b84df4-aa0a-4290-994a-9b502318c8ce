package com.gw.common.membership.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "会员权益详情DTO")
public class BenefitDetailVO {
    /**
     * 每日通话次数，-1 表示无限制
     */
    @Schema(description = "每日通话次数，-1表示无限制")
    private int dailyLimitedChat;
    /**
     * 每日语音通话次数， -1 表示无限制
     */
    @Schema(description = "每日语音通话次数，-1表示无限制")
    private int dailyLimitedPhone;

    /**
     * 每日 AI生图 限制
     */
    @Schema(description = "每日AI生图限制次数 -1 表示无限制")
    private int dailyLimitedImage;

    /**
     * 云端备份大小
     */
    @Schema(description = "云端备份大小 -1 无限制")
    private int backupSize;

    /**
     * 对话回溯条数
     */
    @Schema(description = "对话回溯条数 -1 无限制")
    private int historyMessageCount;

    /**
     * 专属折扣数
     */
    @Schema(description = "专属折扣数")
    private int discountRate;

    /**
     * 记忆重置
     * 0-禁用, 1-启用
     */
    @Schema(description = "记忆重置，0-禁用，1-启用")
    private Integer memoryReset;

    /**
     * 会员标识
     */
    @Schema(description = "会员标识")
    private int membershipBadge;
}