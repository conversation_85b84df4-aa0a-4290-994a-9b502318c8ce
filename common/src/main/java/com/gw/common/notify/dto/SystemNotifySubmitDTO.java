package com.gw.common.notify.dto;

import com.gw.common.user.common.UserCommonConstant;
import lombok.*;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SystemNotifySubmitDTO extends SystemMessageCreateDTO {
    String username = UserCommonConstant.SYSTEM_USER_NAME;

    public SystemNotifySubmitDTO(String title, String message, Integer level) {
        super(title, level);
    }

    public SystemNotifySubmitDTO(String title, Integer level, String targetUsername) {
        super(title, level, targetUsername);
    }

    public SystemNotifySubmitDTO(String title, String content, Integer level, String targetUsername) {
        super(title, content, level, targetUsername);
    }

    public SystemNotifySubmitDTO(String title, Integer level, Long agentId, String targetUsername) {
        super(title, level, agentId, targetUsername);
    }

    public SystemNotifySubmitDTO(String title, String content, Integer level, Long agentId, String targetUsername) {
        super(title, content, level, agentId, targetUsername);
    }
}
