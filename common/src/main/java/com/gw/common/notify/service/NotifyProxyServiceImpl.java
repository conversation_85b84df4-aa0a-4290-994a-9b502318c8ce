package com.gw.common.notify.service;

import com.gw.common.dto.ResponseResult;
import com.gw.common.notify.client.NotifyClient;
import com.gw.common.notify.dto.InteractiveNotifySubmitDTO;
import com.gw.common.notify.dto.SystemNotifySubmitDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

@Service
@Log4j2
@RequiredArgsConstructor
public class NotifyProxyServiceImpl implements NotifyProxyService {
    private final NotifyClient notifyClient;

    @Override
    public void insertSystemMessage(SystemNotifySubmitDTO req) {
        ResponseResult<?> resp = notifyClient.autoCreateSystemMessage(req);
        if (resp.getCode() != 200) {
            log.error("notify-service autoCreateSystemMessage error:{}", resp.getMsg());
        }
    }

    @Override
    public void insertInteractiveMessage(InteractiveNotifySubmitDTO req) {
        ResponseResult<?> resp = notifyClient.autoCreateInteractiveMessage(req);
        if (resp.getCode() != 200) {
            log.error("notify-service autoCreateInteractiveMessage error:{}", resp.getMsg());
        }
    }
}
