package com.gw.common.notify.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.util.List;

/**
 * 创建系统消息DTO
 */
@Data
@Schema(description = "创建系统消息请求")
@NoArgsConstructor
@AllArgsConstructor
public class SystemMessageCreateDTO {
    @Schema(description = "消息标题")
    private String title = "";
    /**
     * 消息内容
     */
    @NotBlank(message = "消息内容不能为空")
    @Schema(description = "消息内容", requiredMode = Schema.RequiredMode.REQUIRED)
    private String content;

    /**
     * 消息类型：1-普通消息，2-重要消息，3-紧急消息
     */
    @NotNull(message = "消息类型不能为空")
    @Schema(description = "消息类型：1-普通消息，2-重要消息，3-紧急消息", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer level;

    /**
     * 是否关联智能体：0-否，1-是
     */
    @NotNull(message = "是否关联智能体不能为空")
    @Schema(description = "是否关联智能体：0-否，1-是", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer associatedWithAgent = 0;

    /**
     * 关联的智能体ID，当associatedWithAgent为1时必填
     */
    @Schema(description = "关联的智能体ID，当associatedWithAgent为1时必填")
    private Long agentId;

    /**
     * 消息有效期开始时间
     */
    @Schema(description = "消息有效期开始时间 不填默认为当前事件")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate validStartDate;

    /**
     * 消息有效期结束时间
     */
    @Schema(description = "消息有效期结束时间,不填默认为永久")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate validEndDate;

    /**
     * 针对的用户列表，为空或null表示发送给所有用户
     */
    @Schema(description = "针对的用户列表，为空或null表示发送给所有用户")
    private List<String> targetUsernames;

    public SystemMessageCreateDTO(String content, Integer level) {
        this.content = content;
        this.level = level;
        this.targetUsernames = null;
    }

    public SystemMessageCreateDTO(String title, Integer level, String targetUsername) {
        this.title = title;
        this.content = title;
        this.level = level;
        this.targetUsernames = List.of(targetUsername);
    }

    public SystemMessageCreateDTO(String title, String content, Integer level, String targetUsername) {
        this.title = title;
        this.content = content;
        this.level = level;
        this.targetUsernames = List.of(targetUsername);
    }

    public SystemMessageCreateDTO(String title, Integer level, Long agentId, String targetUsername) {
        this.title = content;
        this.content = title;
        this.level = level;
        this.agentId = agentId;
        this.associatedWithAgent = 1;
        this.targetUsernames = List.of(targetUsername);
    }

    public SystemMessageCreateDTO(String title, String content, Integer level, Long agentId, String targetUsername) {
        this.title = title;
        this.content = content;
        this.level = level;
        this.agentId = agentId;
        this.associatedWithAgent = 1;
        this.targetUsernames = List.of(targetUsername);
    }
} 