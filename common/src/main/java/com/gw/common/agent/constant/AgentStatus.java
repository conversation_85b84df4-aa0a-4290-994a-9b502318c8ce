package com.gw.common.agent.constant;

import lombok.Getter;

@Getter
public enum AgentStatus {
    CHECKING(1, "审核中"),
    CHECK_FAILED(-1, "审核失败"),
    CREATING(2, "创建中"),
    CREATE_FAILED(-2, "创建失败"),
    PUBLISHING(4, "发布中"),
    PUBLISH_FAILED(-4, "发布失败"),
    PUBLISHED(256, "已发布");

    private final int code;
    private final String description;

    AgentStatus(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public static AgentStatus fromCode(int code) {
        for (AgentStatus status : values()) {
            if (status.getCode() == code) {
                return status;
            }
        }
        throw new IllegalArgumentException("Unknown agent status code: " + code);
    }
}