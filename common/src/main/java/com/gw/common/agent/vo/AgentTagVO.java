package com.gw.common.agent.vo;


import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Positive;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Schema(description = "智能体标签VO")
@AllArgsConstructor
@NoArgsConstructor
public class AgentTagVO {
    @Schema(description = "标签ID")
    private Long id;
    @Schema(description = "标签名称")
    @NotBlank(message = "标签名称不能为空")
    private String name;
    @Schema(description = "标签分类：1-背景标签，2-性格标签")
    @Positive(message = "标签分类不能为空")
    private Integer category;
    @Schema(description = "标签描述")
    private String description;

}
