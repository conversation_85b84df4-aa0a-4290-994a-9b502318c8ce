package com.gw.common.agent.vo;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "智能体详情")
@NoArgsConstructor
@AllArgsConstructor
public class AgentDetailVO extends AgentVO {
    @Schema(description = "标签列表")
    private List<AgentTagVO> tags;
    private AgentProfileVO profile;
    private AgentTypeVO type;

}
