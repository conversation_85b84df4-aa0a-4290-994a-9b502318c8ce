package com.gw.common.agent.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@Schema(description = "智能体信息")
@NoArgsConstructor
@AllArgsConstructor
public class AgentVO {
    public String creator;
    public String updater;
    @Schema(description = "推荐指数，0不推荐")
    Integer recommendIdx = 1;
    private Long id;
    @Schema(description = "智能体名称")
    private String name;
    @Schema(description = "智能体身份")
    private String identity;
    @Schema(description = "性别：1-男，2-女，3-其他")
    private Integer gender;
    @Schema(description = "头像地址")
    @NotBlank(message = "头像地址不能为空")
    private String avatarUrl;
    @Schema(description = "背景图缩略图地址")
    private String bgThumbnailUrl;
    @Schema(description = "背景图地址")
    private String bgUrl;
    private String introduction;
    private Integer status;
    @Schema(description = "是否公开：1-私密，2-公开")
    private Integer isPublic;
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
    /**
     * 是否已收藏：0-未收藏，1-已收藏
     */
    @Schema(description = "是否已收藏：0-未收藏，1-已收藏")
    private Integer isFavorite = 0;
    @Schema(description = "是否已点赞：0-未点赞，1-已点赞")
    private Integer isLike = 0;
    private Long typeId;
    @Schema(description = "点赞数")
    private Integer likeCount = 0;
    @Schema(description = "评论数")
    private Integer commentCount = 0;
    @Schema(description = "收藏数")
    private Integer favoriteCount = 0;
    private String remoteBotId;
    @Schema(description = "上架状态：1-上架，2-下架")
    private Integer shelfStatus = 1;
    @Schema(description = "上下架原因")
    private String shelfReason;
    /**
     * 使用人数
     */
    @Schema(description = "用户使用人数")
    private Integer userCount;
    @Schema(description = "热度值")
    private Integer popularity;
    @Schema(description = "敏感词检测结果")
    private String securityCheckResult;
    @Schema(description = "平台：1-coze，2-火山")
    private Integer platform = 1;
    /**
     * 模型名称
     */
    @Schema(description = "模型名称")
    private String model = "";
}
