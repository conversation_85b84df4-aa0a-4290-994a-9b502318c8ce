package com.gw.common.agent.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 火山方舟设置VO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class VolcanoArkSettingVO {

    // Getter和Setter方法（如果Lombok @Data注解不起作用）
    /**
     * API Key
     */
    private String apiKey;

    /**
     * 服务基础URL
     */
    private String baseUrl;

    /**
     * 默认模型ID
     */
    private String modelId;
    private String compressModelId = "ep-20250706151033-n28bn";
    /**
     * 连接超时时间（毫秒）
     */
    private Integer connectTimeout;

    /**
     * 读取超时时间（毫秒）
     */
    private Integer readTimeout;

    /**
     * 重试次数
     */
    private Integer retryTimes;

}