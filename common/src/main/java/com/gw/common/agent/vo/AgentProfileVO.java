package com.gw.common.agent.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class AgentProfileVO {
    @Schema(description = "背景故事")
    private String background;
    /**
     * 聊天开场白
     */
    @Schema(description = "聊天开场白")
    private String prologue;
    /**
     * 建议问题
     */
    @Schema(description = "聊天开场白建议问题")
    private List<String> suggestedQuestions;
    @Schema(description = "与用户关系类型")
    private String relationshipType;
    @Schema(description = "兴趣爱好")
    private List<String> interests;
    @Schema(description = "专业领域")
    private List<String> expertiseAreas;
    @Schema(description = "对话风格：1-正式，2-随意，3-幽默，4-温柔等")
    private Integer conversationStyle;
    @Schema(description = "回复长度偏好 0-简短，1-中等，2-详细")
    private Integer responseLength;
    @Schema(description = "提示模板")
    private String promptTemplate;
    @Schema(description = "禁忌话题")
    private List<String> tabooTopics;
    @Schema(description = "音色ID")
    private String voiceId;
}
