package com.gw.common.agent.service;

import com.gw.common.agent.vo.AgentStoryBaseVO;
import com.gw.common.agent.vo.AgentStorySceneBaseVO;
import com.gw.common.agent.vo.UserInteractionStoryVO;

import java.time.Duration;
import java.util.List;
import java.util.Map;

public interface AgentStoryProxyService {
    void putAgentStoryMapToCache(String cacheKey, Map<Long, AgentStoryBaseVO> storyMap, Duration expire);

    Map<Long, AgentStoryBaseVO> getStoryBaseMapFromCache(String cacheKey);

    AgentStoryBaseVO findStoryBaseByIdFromCache(String cacheKey, Long id);

    void updateStoryBaseToCacheIfCacheExist(String cacheKey, Long id, AgentStoryBaseVO vo, Duration expire);

    void deleteStoryBaseKeyFromCache(String cacheKey, Long id);

    void deleteStoryBaseFromCache(String cacheKey);

    AgentStoryBaseVO getStoryInfo(String cacheKey, Long storyId);


    Map<Long, AgentStoryBaseVO> getAgentsByIds(String cacheKey, List<Long> storyIds);

    UserInteractionStoryVO getUserInteraction(String username);


    boolean recordStoryUsage(Long agentId, String username);

    boolean recCurrentSceneUse(Long storyId, Long sceneId, String username);

    AgentStorySceneBaseVO getStorySceneInfo(Long sceneId, String cacheKey);

}
