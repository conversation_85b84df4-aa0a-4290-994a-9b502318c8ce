package com.gw.common.user.service;

import com.gw.common.user.vo.UserBaseContentVo;
import com.gw.common.user.vo.UserStatisticsVO;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

public interface UserProxyService {
    void putUserMapToCache(String cacheKey, Map<String, UserBaseContentVo> userMap, Duration expire);

    Map<String, UserBaseContentVo> getUserMapFromCache(String cacheKey);
    Map<String, UserBaseContentVo> findAllUserMapByUsernames(String cacheKey, List<String> usernames);
    UserBaseContentVo findByUsernameFromCache(String cacheKey, String username);

    void updateToCacheIfCacheExist(String cacheKey, String username, UserBaseContentVo vo, Duration expire);

    Map<String, UserBaseContentVo> findAllUserMap(String cacheKey);

    List<UserBaseContentVo> findAllUserList(String cacheKey);

    UserBaseContentVo findByUsername(String cacheKey, String username);

    UserBaseContentVo getUserBaseInfo(String username);


    UserStatisticsVO getUserStatistics();

    /**
     * 统计指定时间范围内的新增用户数
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 新增用户数
     */
    int countNewUsersByTimeRange(LocalDateTime startTime, LocalDateTime endTime);
    int countActiveUsersByTimeRange(LocalDateTime startTime, LocalDateTime endTime);
}
