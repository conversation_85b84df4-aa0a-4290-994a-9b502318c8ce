package com.gw.common.user.service;

import com.alibaba.fastjson2.JSON;
import com.gw.common.dto.ResponseResult;
import com.gw.common.dto.TimeRangeDTO;
import com.gw.common.user.client.UserServiceClient;
import com.gw.common.user.dto.QueryFromUsernameDTO;
import com.gw.common.user.vo.UserBaseContentVo;
import com.gw.common.user.vo.UserStatisticsVO;
import lombok.extern.log4j.Log4j2;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;

@Service
@Log4j2
public class UserProxyServiceImpl implements UserProxyService {


    private final UserServiceClient userServiceClient;
    private final RedisTemplate<String, Object> redisTemplate;

    public UserProxyServiceImpl(UserServiceClient userServiceClient, RedisTemplate<String, Object> redisTemplate) {
        this.userServiceClient = userServiceClient;
        this.redisTemplate = redisTemplate;
    }

    @Override
    public void putUserMapToCache(String cacheKey, Map<String, UserBaseContentVo> userMap, Duration expire) {
        Map<String, String> cacheMap = new LinkedHashMap<>();
        userMap.forEach((k, v) -> {
            cacheMap.put(k, JSON.toJSONString(v));
        });
        redisTemplate.opsForHash().putAll(cacheKey, cacheMap);
        if (expire != null) {
            redisTemplate.expire(cacheKey, expire);
        }
    }

    @Override
    public Map<String, UserBaseContentVo> getUserMapFromCache(String cacheKey) {
        Map<Object, Object> entries = redisTemplate.opsForHash().entries(cacheKey);
        if (entries.isEmpty()) {
            return null;
        }
        Map<String, UserBaseContentVo> result = new HashMap<>();
        entries.forEach((k, v) -> {
            String jsonStr = (String) v;
            UserBaseContentVo vo = JSON.parseObject(jsonStr, UserBaseContentVo.class);
            result.put((String) k, vo);
        });
        return result;
    }

    @Override
    public Map<String, UserBaseContentVo> findAllUserMapByUsernames(String cacheKey, List<String> usernames) {
        if (usernames == null || usernames.isEmpty()) {
            return new HashMap<>();
        }

        var cachedResult = getUserMapFromCache(cacheKey);

        // 如果缓存为空，直接查询所有用户
        if (cachedResult == null || cachedResult.isEmpty()) {
            ResponseResult<Map<String, UserBaseContentVo>> resp = userServiceClient.findAllBase(usernames);
            if (resp.getCode() != 200) {
                log.error("获取用户信息失败: {}", resp.getMsg());
                return new HashMap<>();
            }
            return resp.getData();
        }

        // 检查缓存中是否包含所有请求的用户名
        List<String> missingUsernames = new ArrayList<>();
        Map<String, UserBaseContentVo> result = new HashMap<>();

        for (String username : usernames) {
            if (cachedResult.containsKey(username)) {
                result.put(username, cachedResult.get(username));
            } else {
                missingUsernames.add(username);
            }
        }

        // 如果有缺失的用户名，查询缺失的部分
        if (!missingUsernames.isEmpty()) {
            ResponseResult<Map<String, UserBaseContentVo>> resp = userServiceClient.findAllBase(missingUsernames);
            if (resp.getCode() == 200 && resp.getData() != null) {
                Map<String, UserBaseContentVo> missingData = resp.getData();
                result.putAll(missingData);

                // 将新查询到的数据更新到缓存中
                for (Map.Entry<String, UserBaseContentVo> entry : missingData.entrySet()) {
                    updateToCacheIfCacheExist(cacheKey, entry.getKey(), entry.getValue(), null);
                }
            } else {
                log.error("获取缺失用户信息失败: {}", resp.getMsg());
            }
        }

        return result;
    }

    @Override
    public UserBaseContentVo findByUsernameFromCache(String cacheKey, String username) {
        Object entry = redisTemplate.opsForHash().get(cacheKey, username);
        if (entry != null) {
            String jsonStr = (String) entry;
            return JSON.parseObject(jsonStr, UserBaseContentVo.class);
        }
        return null;
    }

    @Override
    public void updateToCacheIfCacheExist(String cacheKey, String username, UserBaseContentVo vo, Duration expire) {
        try {
            Boolean hasKey = redisTemplate.hasKey(cacheKey);
            if (hasKey) {
                redisTemplate.opsForHash().put(cacheKey, username, JSON.toJSONString(vo));
                if (expire != null) {
                    redisTemplate.expire(cacheKey, expire);
                }
            }
        } catch (Exception e) {
            log.error("更新 Redis cache时发生异常: {}", e.getMessage());
        }
    }

    @Override
    public Map<String, UserBaseContentVo> findAllUserMap(String cacheKey) {
        // Try to get all entries from Redis hash
        var result = getUserMapFromCache(cacheKey);
        if (result != null && !result.isEmpty()) {
            return result;
        }
        // Cache miss - fetch from service
        ResponseResult<Map<String, UserBaseContentVo>> resp = userServiceClient.findAllBase();
        if (resp.getCode() != 200) {
            log.error("获取用户信息失败: {}", resp.getMsg());
            return new HashMap<>();
        }
        return resp.getData();
    }

    @Override
    public List<UserBaseContentVo> findAllUserList(String cacheKey) {
        Map<String, UserBaseContentVo> map = findAllUserMap(cacheKey);
        return new ArrayList<>(map.values());
    }

    @Override
    public UserBaseContentVo findByUsername(String cacheKey, String username) {
        var vo = findByUsernameFromCache(cacheKey, username);
        if (vo != null) {
            return vo;
        }
        QueryFromUsernameDTO queryDTO = new QueryFromUsernameDTO();
        queryDTO.setUsername(username);

        ResponseResult<UserBaseContentVo> resp = userServiceClient.getUserInfoFromUsername(queryDTO);
        if (resp.getCode() != 200) {
            log.error("获取用户信息失败: {}", resp.getMsg());
            return null;
        }

        return resp.getData();
    }

    @Override
    public UserBaseContentVo getUserBaseInfo(String username) {
        QueryFromUsernameDTO queryDTO = new QueryFromUsernameDTO();
        queryDTO.setUsername(username);

        ResponseResult<UserBaseContentVo> resp = userServiceClient.getUserInfoFromUsername(queryDTO);
        if (resp.getCode() != 200) {
            log.error("获取用户基本信息失败: {}", resp.getMsg());
            return null;
        }
        return resp.getData();
    }

    @Override
    public UserStatisticsVO getUserStatistics() {
        ResponseResult<UserStatisticsVO> resp = userServiceClient.getUserStatistics();
        if (resp.getCode() != 200) {
            log.error("获取用户统计信息失败: {}", resp.getMsg());
            return new UserStatisticsVO();
        }
        return resp.getData();
    }

    @Override
    public int countNewUsersByTimeRange(LocalDateTime startTime, LocalDateTime endTime) {
        ResponseResult<Integer> resp = userServiceClient.countNewUsersByTimeRange(new TimeRangeDTO(startTime, endTime));
        if (resp.getCode() != 200) {
            log.error("获取时间范围内新增用户数失败: {}", resp.getMsg());
            return 0;
        }
        return resp.getData();
    }
    @Override
    public int countActiveUsersByTimeRange(LocalDateTime startTime, LocalDateTime endTime) {
        ResponseResult<Integer> resp = userServiceClient.countActiveUsersByTimeRange(new TimeRangeDTO(startTime, endTime));
        if (resp.getCode() != 200) {
            log.error("获取时间范围内活跃用户数失败: {}", resp.getMsg());
            return 0;
        }
        return resp.getData();
    }
}
