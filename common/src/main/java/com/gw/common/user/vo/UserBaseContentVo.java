package com.gw.common.user.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Schema(description = "用户基本数据")
public class UserBaseContentVo {
    @Schema(description = "用户名")
    @JsonProperty("u")
    private String username;
    @JsonProperty("n")
    private String nickname;
    @Schema(description = "电话")
    @JsonProperty("p")
    private String phone;
    @Schema(description = "性别：1-男，2-女，3-其他")
    @JsonProperty("s")
    private Integer gender;
    @Schema(description = "用户简介")
    @JsonProperty("ir")
    private String introduce;
    @JsonProperty("wx")
    private String wxOpenId;
    @JsonProperty("iy")
    private String identify;
    @JsonProperty("av")
    private String avatar;

}
