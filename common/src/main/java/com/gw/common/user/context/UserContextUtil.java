package com.gw.common.user.context;

import lombok.experimental.UtilityClass;

import java.util.Collections;
import java.util.List;

@UtilityClass
public class UserContextUtil {

    public Long getCurrentUserId() {
        UserContext context = UserContext.get();
        return context != null ? Long.parseLong(context.getUserId()) : null;
    }

    public String getCurrentUsername() {
        UserContext context = UserContext.get();
        return context != null ? context.getUsername() : null;
    }

    public String getCurrentRealName() {
        UserContext context = UserContext.get();
        return context != null ? context.getRealName() : null;
    }

    public List<String> getCurrentUserRoles() {
        UserContext context = UserContext.get();
        return context != null ? context.getRoles() : Collections.emptyList();
    }

    public boolean hasRole(String role) {
        List<String> roles = getCurrentUserRoles();
        return roles != null && roles.contains(role);
    }
}
