package com.gw.membership.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static com.baomidou.mybatisplus.annotation.IdType.AUTO;

/**
 * 用户支出明细实体
 */
@Data
@TableName("t_user_expense_record")
@NoArgsConstructor
@AllArgsConstructor
public class UserExpenseRecordEntity {
    @TableId(type = AUTO)
    private Long id;
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime = LocalDateTime.now();
    @TableLogic
    private Integer deleted = 0;
    /**
     * 用户名
     */
    private String username;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 支出类型: 1-会员购买, 2-智能体购买, 3-其他消费
     */
    private Integer expenseType;

    /**
     * 支出金额
     */
    private BigDecimal amount;

    /**
     * 支出描述
     */
    private String description;

    /**
     * 支付方式: 1-支付宝, 2-微信, 3-Apple支付, 4-银行卡
     */
    private Integer paymentMethod;

    /**
     * 交易流水号
     */
    private String transactionId;

    /**
     * 状态:  1-成功, 2-退款,3-失败
     */
    private Integer status;
}