package com.gw.membership.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gw.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 用户会员历史记录实体
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_user_membership_history")
public class UserMembershipHistoryEntity extends BaseEntity {

    /**
     * 用户ID
     */
    private String username;

    /**
     * 操作类型: 1-新开通, 2-续费, 3-赠送, 4-管理员操作, 5-邀请获得, 6-过期, 7-取消, 8-套餐变更 9 新用户注册
     */
    private Integer operationType;

    /**
     * 相关订单ID 如果是赠送 ，则为空
     */
    private Long orderId;

    /**
     * 套餐ID 如果为空，表示没有关联套餐
     */
    private Long packageId;

    /**
     * 套餐名称 如果为空，表示没有关联套餐
     */
    private String packageName;

    /**
     * 变更前会员状态: 0-非会员, 1-会员
     */
    private Integer beforeStatus;

    /**
     * 变更后会员状态: 0-非会员, 1-会员
     */
    private Integer afterStatus;

    /**
     * 变更前到期时间
     */
    private LocalDateTime beforeExpireTime;

    /**
     * 变更后到期时间
     */
    private LocalDateTime afterExpireTime;

    /**
     * 操作人
     */
    private String operator;


    /**
     * 邀请人用户名 只针对邀请人有效
     */
    private String inviteUsername;

    /**
     * 备注
     */
    private String remark;
} 