package com.gw.membership.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gw.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 套餐权益关联实体
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_package_benefit_relation")
public class PackageBenefitRelationEntity extends BaseEntity {

    /**
     * 套餐ID
     */
    private Long packageId;

    /**
     * 权益ID
     */
    private Long benefitId;

    /**
     * 权益值
     */
    private String benefitValue;

    /**
     * 关联状态: 0-禁用, 1-启用
     */
    private Integer status;
}