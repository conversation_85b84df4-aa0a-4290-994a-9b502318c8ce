package com.gw.membership.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gw.membership.entity.RewardConfigEntity;
import com.gw.membership.mapper.RewardConfigMapper;
import com.gw.membership.service.RewardConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
@Log4j2
public class RewardConfigServiceImpl extends ServiceImpl<RewardConfigMapper, RewardConfigEntity> implements RewardConfigService {
    @Override
    public RewardConfigEntity findByCode(String code) {
        return this.baseMapper.findByCode(code);
    }

    @Override
    public void update(RewardConfigEntity entity) {
        this.baseMapper.updateById(entity);
    }

    @Override
    public List<RewardConfigEntity> findAll() {
        return this.baseMapper.findAll();
    }
}
