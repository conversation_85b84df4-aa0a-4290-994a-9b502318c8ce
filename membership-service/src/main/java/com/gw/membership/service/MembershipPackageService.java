package com.gw.membership.service;

import com.gw.membership.entity.MembershipBenefitEntity;
import com.gw.membership.entity.MembershipPackageEntity;

import java.util.List;

/**
 * 会员套餐服务接口
 */
public interface MembershipPackageService {
    MembershipPackageEntity findByName(String name);

    MembershipPackageEntity findById(Long id);

    boolean existsByName(String name);

    boolean existsByNameAndIdNot(String name, Long id);

    void createPackage(MembershipPackageEntity entity);


    void updatePackage(MembershipPackageEntity entity);


    void deletePackage(MembershipPackageEntity entity);


    /**
     * 获取所有会员套餐
     *
     * @return 套餐列表
     */
    List<MembershipPackageEntity> findAllPackages();

    /**
     * 获取所有上架的会员套餐
     *
     * @return 上架的套餐列表
     */
    List<MembershipPackageEntity> getAllActivePackages();

    /**
     * 获取推荐的会员套餐
     *
     * @return 推荐的套餐列表
     */
    List<MembershipPackageEntity> getRecommendedPackages();

    /**
     * 根据类型获取会员套餐
     *
     * @param type 套餐类型
     * @return 套餐列表
     */
    List<MembershipPackageEntity> getPackagesByType(Integer type);


    void updatePackageStatus(MembershipPackageEntity entity, Integer status);

    /**
     * 关联套餐和权益
     *
     * @param packageId 套餐ID
     * @param benefits  权益列表
     * @return 是否成功
     */

    boolean associateBenefits(Long packageId, List<MembershipBenefitEntity> benefits);

    /**
     * 移除套餐关联的权益
     *
     * @param packageId  套餐ID
     * @param benefitIds 权益ID列表
     * @return 是否成功
     */
    boolean removeBenefits(Long packageId, List<Long> benefitIds);
}