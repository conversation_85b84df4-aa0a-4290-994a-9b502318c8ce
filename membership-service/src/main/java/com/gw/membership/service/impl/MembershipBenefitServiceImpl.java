package com.gw.membership.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gw.common.exception.EntityNotFoundException;
import com.gw.membership.constant.MemberLocalCacheConstant;
import com.gw.membership.entity.MembershipBenefitEntity;
import com.gw.membership.mapper.MembershipBenefitMapper;
import com.gw.membership.service.MembershipBenefitService;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 会员权益服务实现类
 */
@Service
@RequiredArgsConstructor
@Log4j2
public class MembershipBenefitServiceImpl extends ServiceImpl<MembershipBenefitMapper, MembershipBenefitEntity>
        implements MembershipBenefitService {
    @Transactional
    @Override
    @CacheEvict(value = MemberLocalCacheConstant.MEMBERSHIP_BENEFIT_VALUE, allEntries = true)
    public void createBenefit(MembershipBenefitEntity entity) {

        this.baseMapper.insert(entity);
    }

    @Override
    public boolean existsByCodeAndVipLevel(String code, Integer vipLevel) {
        return this.baseMapper.findFirstByCodeAndVipLevel(code, vipLevel).isPresent();
    }

    @Override
    @Cacheable(value = MemberLocalCacheConstant.MEMBERSHIP_BENEFIT_VALUE, key = "#root.methodName + ':' + #code + ':' + #vipLevel")
    public MembershipBenefitEntity findFirstByCodeAndVipLevel(String code, Integer vipLevel) {
        return this.baseMapper.findFirstByCodeAndVipLevel(code, vipLevel)
                .orElse(null);
    }

    @Override
    public boolean existsByNameAndIdNot(String code, Long id) {
        return this.baseMapper.findByCodeAndIdNot(code, id).isPresent();
    }

    @Override
    public MembershipBenefitEntity findById(Long id) {
        return this.baseMapper.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("权益不存在"));
    }

    @Override
    public MembershipBenefitEntity selectById(Long id) {
        return this.baseMapper.selectById(id);
    }

    @Override
    @Transactional
    @CacheEvict(value = MemberLocalCacheConstant.MEMBERSHIP_BENEFIT_VALUE, allEntries = true)
    public void updateBenefit(MembershipBenefitEntity entity) {
        this.baseMapper.updateById(entity);
    }

    @Transactional
    @Override
    @CacheEvict(value = MemberLocalCacheConstant.MEMBERSHIP_BENEFIT_VALUE, allEntries = true)
    public void deleteBenefit(MembershipBenefitEntity entity) {


        // 逻辑删除权益
        entity.setDeleted(1);
        entity.setUpdateTime(LocalDateTime.now());
        this.baseMapper.deleteById(entity);

    }

    @Override
    public Map<Integer, List<MembershipBenefitEntity>> findAllByVipLevel() {
        List<MembershipBenefitEntity> entities = this.baseMapper.findAllActive();
        return entities.stream()
                .collect(Collectors.groupingBy(MembershipBenefitEntity::getVipLevel));
    }


    @Override
    public List<MembershipBenefitEntity> findAll() {
        return this.baseMapper.findAllActive();
    }

    @Override
    public List<MembershipBenefitEntity> findAllShow() {
        return this.baseMapper.findAllActiveAndShow();
    }

    @Override
    public Map<Long, MembershipBenefitEntity> findAllActiveBenefitsMap() {
        return this.baseMapper.findAllActive().stream()
                .collect(Collectors.toMap(MembershipBenefitEntity::getId, entity -> entity));
    }

    @Override
    public Map<Long, MembershipBenefitEntity> findAllMap() {
        return this.baseMapper.findAll().stream()
                .collect(Collectors.toMap(MembershipBenefitEntity::getId, entity -> entity));
    }

    @Override
    @Cacheable(value = MemberLocalCacheConstant.MEMBERSHIP_BENEFIT_VALUE, key = "#root.methodName")
    public List<MembershipBenefitEntity> findAllActiveBenefits() {
        return this.baseMapper.findAllActive();
    }

    @Override
    @Cacheable(value = MemberLocalCacheConstant.MEMBERSHIP_BENEFIT_VALUE, key = "#root.methodName + ':' + #vipLevel")
    public List<MembershipBenefitEntity> findAllActiveAndVipLevel(Integer vipLevel) {
        return this.baseMapper.findAllActiveAndVipLevel(vipLevel);
    }

    @Override
    @Cacheable(value = MemberLocalCacheConstant.MEMBERSHIP_BENEFIT_VALUE, key = "#root.methodName + ':' + #type")
    public List<MembershipBenefitEntity> getBenefitsByType(Integer type) {
        return this.baseMapper.selectByType(type);
    }


    @Transactional
    @Override
    @CacheEvict(value = MemberLocalCacheConstant.MEMBERSHIP_BENEFIT_VALUE, allEntries = true)
    public boolean updateBenefitStatus(MembershipBenefitEntity entity, Integer status) {
        // 更新状态
        entity.setStatus(status);
        entity.setUpdateTime(LocalDateTime.now());
        this.baseMapper.updateById(entity);

        return true;
    }

    @Override
    public List<MembershipBenefitEntity> findByIds(List<Long> ids) {
        return this.baseMapper.findByIds(ids);
    }

    @Override
    @Cacheable(value = MemberLocalCacheConstant.MEMBERSHIP_BENEFIT_VALUE, key = "#root.methodName + ':' + #vipLevel")
    public List<MembershipBenefitEntity> findByVipLevel(int vipLevel) {
        return this.baseMapper.findByVipLevel(vipLevel);
    }
}