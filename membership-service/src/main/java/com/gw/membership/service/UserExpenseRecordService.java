package com.gw.membership.service;

import com.github.pagehelper.PageInfo;
import com.gw.membership.dto.UserExpenseRecordQueryDTO;
import com.gw.membership.entity.MembershipOrderEntity;
import com.gw.membership.entity.UserExpenseRecordEntity;

/**
 * 用户支出明细服务接口
 */
public interface UserExpenseRecordService {

    /**
     * 创建支出记录
     */
    void createExpenseRecord(UserExpenseRecordEntity entity);

    /**
     * 更新支出记录
     */
    void updateExpenseRecord(UserExpenseRecordEntity entity);

    /**
     * 获取支出记录详情
     */
    UserExpenseRecordEntity getExpenseRecordById(Long id);

    /**
     * 分页查询用户支出记录
     */
    PageInfo<UserExpenseRecordEntity> page(int pageNum, int pageSize, UserExpenseRecordQueryDTO require);

    /**
     * 删除支出记录
     */
    boolean deleteExpenseRecord(Long id);

    /**
     * 根据订单支付结果创建用户支出记录
     *
     * @param orderEntity 订单实体
     * @return 创建的支出记录
     */
    UserExpenseRecordEntity createExpenseRecordFromOrder(MembershipOrderEntity orderEntity);
} 