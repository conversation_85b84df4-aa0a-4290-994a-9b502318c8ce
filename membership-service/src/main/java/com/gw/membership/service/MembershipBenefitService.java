package com.gw.membership.service;

import com.gw.membership.entity.MembershipBenefitEntity;

import java.util.List;
import java.util.Map;

/**
 * 会员权益服务接口
 */
public interface MembershipBenefitService {

    void createBenefit(MembershipBenefitEntity entity);

    boolean existsByCodeAndVipLevel(String code, Integer vipLevel);


    MembershipBenefitEntity findFirstByCodeAndVipLevel(String code, Integer vipLevel);

    boolean existsByNameAndIdNot(String code, Long id);

    MembershipBenefitEntity findById(Long id);

    MembershipBenefitEntity selectById(Long id);

    void updateBenefit(MembershipBenefitEntity entity);

    void deleteBenefit(MembershipBenefitEntity entity);

    Map<Integer, List<MembershipBenefitEntity>> findAllByVipLevel();


    List<MembershipBenefitEntity> findAll();

    List<MembershipBenefitEntity> findAllShow();

    Map<Long, MembershipBenefitEntity> findAllActiveBenefitsMap();

    Map<Long, MembershipBenefitEntity> findAllMap();

    /**
     * 获取所有启用的会员权益
     *
     * @return 启用的权益列表
     */
    List<MembershipBenefitEntity> findAllActiveBenefits();

    List<MembershipBenefitEntity> findAllActiveAndVipLevel(Integer vipLevel);

    /**
     * 根据类型获取会员权益
     *
     * @param type 权益类型
     * @return 权益列表
     */
    List<MembershipBenefitEntity> getBenefitsByType(Integer type);


    boolean updateBenefitStatus(MembershipBenefitEntity entity, Integer status);


    List<MembershipBenefitEntity> findByIds(List<Long> ids);

    List<MembershipBenefitEntity> findByVipLevel(int vipLevel);
}