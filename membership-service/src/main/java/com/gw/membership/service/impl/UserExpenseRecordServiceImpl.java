package com.gw.membership.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageInfo;
import com.gw.membership.dto.UserExpenseRecordQueryDTO;
import com.gw.membership.entity.MembershipOrderEntity;
import com.gw.membership.entity.UserExpenseRecordEntity;
import com.gw.membership.mapper.UserExpenseRecordMapper;
import com.gw.membership.service.UserExpenseRecordService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import static com.github.pagehelper.page.PageMethod.startPage;

/**
 * 用户支出明细服务实现
 */
@Service
@RequiredArgsConstructor
public class UserExpenseRecordServiceImpl extends ServiceImpl<UserExpenseRecordMapper, UserExpenseRecordEntity> implements UserExpenseRecordService {


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createExpenseRecord(UserExpenseRecordEntity entity) {
        this.baseMapper.insert(entity);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateExpenseRecord(UserExpenseRecordEntity entity) {
        this.baseMapper.updateById(entity);

    }

    @Override
    public UserExpenseRecordEntity getExpenseRecordById(Long id) {
        return this.baseMapper.selectById(id);
    }

    @Override
    public PageInfo<UserExpenseRecordEntity> page(int pageNum, int pageSize, UserExpenseRecordQueryDTO query) {
        startPage(pageNum, pageSize);
        List<UserExpenseRecordEntity> list = this.baseMapper.page(query);
        return new PageInfo<>(list);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteExpenseRecord(Long id) {
        UserExpenseRecordEntity entity = new UserExpenseRecordEntity();
        entity.setId(id);
        entity.setDeleted(1);

        var ret = this.baseMapper.updateById(entity) > 0;
        this.baseMapper.deleteById(entity);
        return ret;
    }

    /**
     * 根据订单支付结果创建用户支出记录
     *
     * @param orderEntity 订单实体
     * @return 创建的支出记录
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public UserExpenseRecordEntity createExpenseRecordFromOrder(MembershipOrderEntity orderEntity) {
        if (orderEntity == null) {
            return null;
        }

        // 创建支出记录实体
        UserExpenseRecordEntity expenseRecord = new UserExpenseRecordEntity();
        expenseRecord.setUsername(orderEntity.getUsername());
        expenseRecord.setOrderId(orderEntity.getId());
        expenseRecord.setExpenseType(1); // 1-会员购买
        expenseRecord.setAmount(orderEntity.getPayAmount());
        expenseRecord.setDescription("购买会员套餐: " + orderEntity.getPackageName());
        expenseRecord.setPaymentMethod(orderEntity.getPayMethod());
        expenseRecord.setTransactionId(orderEntity.getTradeNo());
        expenseRecord.setStatus(1); // 1-成功

        // 保存记录
        this.baseMapper.insert(expenseRecord);

        return expenseRecord;
    }
} 