package com.gw.membership.service;

import com.gw.membership.entity.UserMembershipEntity;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 用户会员服务接口
 */
public interface UserMembershipService {

    /**
     * 初始化用户会员信息
     *
     * @param username 用户ID
     * @return 是否成功
     */
    UserMembershipEntity findOrInitUserMembership(String username);

    boolean existMembershipByUsername(String username);
    Map<String, UserMembershipEntity> findByUsernames(List<String> username);
    UserMembershipEntity checkAndUpdateMembershipStatus(UserMembershipEntity userMembership);

    /**
     * 开通会员
     *
     * @param username  用户ID
     * @param packageId 套餐ID
     * @param orderId   订单ID
     * @param days      有效天数
     * @return 是否成功
     */
    boolean activateMembershipFromPackage(String username, Long packageId, Long orderId, Integer days);

    /**
     * 续费会员
     *
     * @param username  用户ID
     * @param packageId 套餐ID
     * @param orderId   订单ID
     * @param days      有效天数
     * @return 是否成功
     */
    boolean renewMembershipFromPackage(String username, Long packageId, Long orderId, Integer days);

    /**
     * 取消会员
     *
     * @param username 用户ID
     * @return 是否成功
     */
    boolean cancelMembership(String username);

    Boolean checkUserMembership(String username);

    /**
     * 更新自动续费状态
     *
     * @param username  用户ID
     * @param autoRenew 是否自动续费
     * @return 是否成功
     */
    boolean updateAutoRenew(String username, Boolean autoRenew);

    /**
     * 检查会员是否过期
     *
     * @param username 用户ID
     * @return 是否过期
     */
    boolean checkMembershipExpired(String username);

    boolean giftMembership(String username, Integer days, String reason, String operator);

    /*
      处理过期会员
     */

    /**
     * 获取即将过期的会员
     *
     * @param days 剩余天数
     * @return 即将过期的会员列表
     */
    List<UserMembershipEntity> getSoonToExpireMembers(Integer days);

    /**
     * 通过管理员手动延长会员有效期
     *
     * @param username   用户ID
     * @param extendDays 延长天数
     * @return 是否成功
     */
    boolean extendMembershipFromSystem(String username, Integer extendDays);

    /**
     * 增加会员积分
     *
     * @param username 用户ID
     * @param points   积分
     * @param reason   原因
     * @return 是否成功
     */
    boolean addMemberPoints(String username, Integer points, String reason);

    /**
     * 消费会员积分
     *
     * @param username 用户ID
     * @param points   积分
     * @param reason   原因
     * @return 是否成功
     */
    boolean consumeMemberPoints(String username, Integer points, String reason);

    void newUserRegister(String username);

    void checkAndUpdateExpiration(UserMembershipEntity userMembership, LocalDateTime now);

    UserMembershipEntity copyUserMembership(UserMembershipEntity source);

    int calculateValidDays(Integer rewardType, Integer rewardValue);


    /**
     * 计算指定时间范围内的收款金额
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 收款金额
     */
    double calculateTodayIncome(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 统计VIP用户总数
     *
     * @return VIP用户总数
     */
    int countTotalVipUsers();

    /**
     * 计算历史收款总金额
     *
     * @return 历史收款总金额
     */
    double calculateTotalIncome();
}