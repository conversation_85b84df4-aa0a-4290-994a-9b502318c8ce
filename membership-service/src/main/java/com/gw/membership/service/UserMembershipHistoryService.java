package com.gw.membership.service;

import com.github.pagehelper.PageInfo;
import com.gw.membership.entity.UserMembershipEntity;
import com.gw.membership.entity.UserMembershipHistoryEntity;

import java.util.List;

/**
 * 用户会员历史记录服务接口
 */
public interface UserMembershipHistoryService {

    /**
     * 记录会员状态变更历史
     *
     * @param beforeMembership 变更前会员状态
     * @param afterMembership  变更后会员状态
     * @param operationType    操作类型
     * @param orderId          相关订单ID
     * @param source           操作来源
     * @param operator         操作人
     * @param inviteUsername   邀请人用户名
     * @param remark           备注
     * @return 是否成功
     */
    boolean recordMembershipHistory(
            UserMembershipEntity beforeMembership,
            UserMembershipEntity afterMembership,
            Integer operationType,
            Long orderId,
            String operator,
            String inviteUsername,
            String remark
    );

    /**
     * 记录会员开通历史
     *
     * @param membership     会员信息
     * @param orderId        订单ID
     * @param operationType  操作类型
     * @param source         操作来源
     * @param operator       操作人
     * @param inviteUsername 邀请人用户名
     * @param remark         备注
     * @return 是否成功
     */
    boolean recordActivation(
            UserMembershipEntity membership,
            Long orderId,
            Integer operationType,
            Integer source,
            String operator,
            String inviteUsername,
            String remark
    );

    /**
     * 记录会员过期历史
     *
     * @param membership 会员信息
     * @return 是否成功
     */
    boolean recordExpiration(UserMembershipEntity membership);

    /**
     * 获取用户会员历史记录
     *
     * @param username 用户ID
     * @return 历史记录列表
     */
    List<UserMembershipHistoryEntity> getUserMembershipHistory(String username);

    /**
     * 分页获取用户会员历史记录
     *
     * @param username 用户ID
     * @param pageNum  页码
     * @param pageSize 每页大小
     * @return 分页历史记录
     */
    PageInfo<UserMembershipHistoryEntity> getUserMembershipHistoryPage(String username, int pageNum, int pageSize);
} 