package com.gw.membership.controller;

import com.alibaba.fastjson2.JSON;
import com.gw.common.dto.ItemIdDTO;
import com.gw.common.dto.ResponseResult;
import com.gw.common.exception.BusinessExceptionCode;
import com.gw.common.membership.dto.BenefitCanUseQueryDTO;
import com.gw.common.membership.dto.BenefitUsageRecordDTO;
import com.gw.common.membership.vo.MembershipBenefitVO;
import com.gw.common.user.context.UserContextUtil;
import com.gw.common.vo.BenefitCanUseVO;
import com.gw.common.vo.BenefitRemainingNumVO;
import com.gw.membership.constant.MemberConstants;
import com.gw.membership.dto.MembershipBenefitDTO;
import com.gw.membership.dto.MembershipBenefitModifyDTO;
import com.gw.membership.dto.MembershipBenefitUpdateDTO;
import com.gw.membership.entity.MembershipBenefitEntity;
import com.gw.membership.entity.UserMembershipEntity;
import com.gw.membership.mapper.ModelMapperConvert;
import com.gw.membership.service.MemberBenefitUsageService;
import com.gw.membership.service.MembershipBenefitService;
import com.gw.membership.service.UserMembershipService;
import com.gw.membership.vo.BenefitCategoryDetailVO;
import com.gw.membership.vo.BenefitCategoryVO;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.modelmapper.ModelMapper;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 后台设置会员权益控制器
 */
@RestController
@RequestMapping("/api/v1/membership/admin/benefit")
@RequiredArgsConstructor
@Tag(name = "后台设置会员权益服务接口", description = "后台会员权益服务接口相关API")
@Log4j2
public class MembershipBenefitController {
    private final UserMembershipService userMembershipService;
    private final MembershipBenefitService benefitService;
    private final MemberBenefitUsageService benefitUsageService;

    public void fillEntity(MembershipBenefitDTO benefitDTO, MembershipBenefitEntity entity) {
        ModelMapper modelmapper = ModelMapperConvert.getBaseModelMapper();
        modelmapper.map(benefitDTO, entity);
    }

    /**
     * 创建会员权益
     */
    @Operation(summary = "创建会员权益", description = "创建会员权益")
    @PostMapping("")
    @Hidden
    public ResponseResult<?> createBenefit(@RequestBody @Valid MembershipBenefitDTO benefitDTO) {
        boolean ret = benefitService.existsByCodeAndVipLevel(benefitDTO.getCode(), benefitDTO.getVipLevel());
        if (ret) {
            return ResponseResult.failure(BusinessExceptionCode.FAIL_CODE, "会员权益代码已存在");
        }
        String username = UserContextUtil.getCurrentUsername();
        MembershipBenefitEntity entity = new MembershipBenefitEntity();
        entity.setCreator(username);
        entity.setCreateTime(LocalDateTime.now());
        entity.setUpdater(username);
        entity.setUpdateTime(LocalDateTime.now());
        fillEntity(benefitDTO, entity);
        benefitService.createBenefit(entity);
        return ResponseResult.success(null);
    }

    /**
     * 更新会员权益
     */
    @Operation(summary = "更新会员权益", description = "更新会员权益")
    @PostMapping("update")
    @Hidden
    public ResponseResult<?> updateBenefit(
            @RequestBody @Valid MembershipBenefitUpdateDTO benefitDTO) {
        boolean ret = benefitService.existsByNameAndIdNot(benefitDTO.getCode(), benefitDTO.getId());
        if (ret) {
            return ResponseResult.failure(BusinessExceptionCode.FAIL_CODE, "会员权益代码已存在");
        }
        MembershipBenefitEntity entity = benefitService.findById(benefitDTO.getId());
        String username = UserContextUtil.getCurrentUsername();
        entity.setUpdater(username);
        entity.setUpdateTime(LocalDateTime.now());
        fillEntity(benefitDTO, entity);
        benefitService.updateBenefit(entity);
        return ResponseResult.success(null);
    }

    /**
     * 更新会员权益
     */
    @Operation(summary = "修改会员权益参数", description = "修改会员权益参数")
    @PostMapping("modify_params")

    public ResponseResult<?> modifyParams(
            @RequestBody @Valid MembershipBenefitModifyDTO benefitDTO) {
        MembershipBenefitEntity entity = benefitService.findById(benefitDTO.getId());

        String username = UserContextUtil.getCurrentUsername();
        entity.setUpdater(username);
        entity.setUpdateTime(LocalDateTime.now());
        entity.setQuotaValue(benefitDTO.getQuotaValue());
        entity.setResetCycle(benefitDTO.getResetCycle());
        benefitService.updateBenefit(entity);
        return ResponseResult.success(null);
    }

    /**
     * 删除会员权益
     */
    @Operation(summary = "删除会员权益", description = "删除会员权益")
    @PostMapping("delete")
    @Hidden
    public ResponseResult<?> deleteBenefit(@RequestBody @Valid ItemIdDTO params) {
        MembershipBenefitEntity entity = benefitService.findById(params.getId());
        String username = UserContextUtil.getCurrentUsername();
        entity.setUpdater(username);
        entity.setUpdateTime(LocalDateTime.now());
        benefitService.deleteBenefit(entity);
        return ResponseResult.success(null);
    }

    /**
     * 查询所有会员权益
     */
    @Operation(summary = "查询所有权益分类详情", description = "查询所有权益分类详情")
    @GetMapping("/all_detail")
    public ResponseResult<List<BenefitCategoryDetailVO>> findAllDetail() {
        List<BenefitCategoryVO> categoryVOS = MemberConstants.BENEFIT_CATEGORY_LIST.stream().toList();
        Map<Integer, List<MembershipBenefitEntity>> benefitsMap = benefitService.findAllByVipLevel();
        List<BenefitCategoryDetailVO> list = new ArrayList<>();
        ModelMapper modelMapper = ModelMapperConvert.getBaseModelMapper();
        for (BenefitCategoryVO categoryVO : categoryVOS) {
            BenefitCategoryDetailVO vo = new BenefitCategoryDetailVO(categoryVO);
            List<MembershipBenefitEntity> entities = benefitsMap.get(categoryVO.getVipLevel()).stream()
                    .sorted(Comparator.comparing(MembershipBenefitEntity::getSortOrder))
                    .collect(Collectors.toList());
            vo.setBenefits(entities.stream().map(entity -> modelMapper.map(entity, MembershipBenefitVO.class)).toList());
            list.add(vo);
        }
        return ResponseResult.success(list);
    }

    /**
     * 查询所有会员权益
     */
    @Operation(summary = "查询所有会员权益分类", description = "查询所有会员权益分类")
    @GetMapping("/all")
    public ResponseResult<List<BenefitCategoryVO>> findAllShow() {
        List<BenefitCategoryVO> categoryVOS = MemberConstants.BENEFIT_CATEGORY_LIST.stream().filter(item -> item.getIsShow() == 1).toList();

        return ResponseResult.success(categoryVOS);
    }

    /**
     * 查询所有会员权益
     */
    @Operation(summary = "会员权益是否能使用", description = "会员权益是否能使用")
    @PostMapping("/query_can_use")
    public ResponseResult<BenefitCanUseVO> queryCanUse(@RequestBody BenefitCanUseQueryDTO query) {
        log.info("查询是否能用参数: {}", JSON.toJSONString(query));

        UserMembershipEntity userMembership = userMembershipService.findOrInitUserMembership(query.getUsername());
        MembershipBenefitEntity benefit = benefitService.findFirstByCodeAndVipLevel(query.getBenefitCode(), userMembership.getVipLevel());
        if (userMembership.getVipLevel() > 0) {
            userMembershipService.checkAndUpdateMembershipStatus(userMembership);
        }
//        log.info("会员: {}", JSON.toJSONString(userMembership));

//        log.info("权益: {}", JSON.toJSONString(benefit));
        if (benefit == null || benefit.getQuotaValue() == 0) {
            return ResponseResult.success(new BenefitCanUseVO(false));
        }
        if (benefit.getQuotaValue() < 0) {
            return ResponseResult.success(new BenefitCanUseVO(true));
        }
        int number = benefitUsageService.getTodayUserBenefitUsageCount(userMembership.getUsername(), query.getBenefitCode());
        log.info("使用次数: {} 权益 {}", number, benefit.getQuotaValue());
        return ResponseResult.success(new BenefitCanUseVO(number < benefit.getQuotaValue()));
    }

    /**
     * 查询所有会员权益
     */
    @Operation(summary = "权益使用记录", description = "权益使用记录")
    @PostMapping("/usage_record")
    public ResponseResult<BenefitRemainingNumVO> usageRecord(@RequestBody BenefitUsageRecordDTO params) {
        log.info("权益使用记录请求参数: {}", JSON.toJSONString(params));

        UserMembershipEntity userMembership = userMembershipService.findOrInitUserMembership(params.getUsername());
        MembershipBenefitEntity benefit = benefitService.findFirstByCodeAndVipLevel(params.getBenefitCode(), userMembership.getVipLevel());

        // 检查权益是否存在
        if (benefit == null) {
            log.error("未找到权益配置，用户: {}, 权益代码: {}, VIP等级: {}",
                    params.getUsername(), params.getBenefitCode(), userMembership.getVipLevel());
            return ResponseResult.failure(BusinessExceptionCode.FAIL_CODE, "权益配置不存在");
        }

        Integer useCount = benefitUsageService.recordBenefitUsage(params.getUsername(), params.getBenefitCode(), params.getCount());

        // 计算剩余次数
        int remainingCount = benefit.getQuotaValue() - useCount;
        log.info("权益使用记录完成，用户: {}, 权益: {}, 已使用: {}, 剩余: {}",
                params.getUsername(), params.getBenefitCode(), useCount, remainingCount);

        return ResponseResult.success(new BenefitRemainingNumVO(remainingCount));
    }
} 