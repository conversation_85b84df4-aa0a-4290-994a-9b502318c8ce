package com.gw.membership.controller;


import com.fasterxml.jackson.databind.ObjectMapper;
import com.gw.common.dto.ResponseResult;
import com.gw.common.exception.BusinessExceptionCode;
import com.gw.common.membership.constant.MemberCacheConstant;
import com.gw.common.membership.dto.MembershipQueryDTO;
import com.gw.common.membership.dto.NewUserRegisterDTO;
import com.gw.common.membership.vo.MembershipBenefitBaseVO;
import com.gw.common.membership.vo.UserMembershipBaseVO;
import com.gw.common.membership.vo.UserMembershipVO;
import com.gw.common.user.constant.UserCommonCacheConstant;
import com.gw.common.user.context.UserContextUtil;
import com.gw.common.user.service.UserProxyService;
import com.gw.common.user.vo.UserBaseContentVo;
import com.gw.membership.config.CacheProperties;
import com.gw.membership.constant.MemberConstants;
import com.gw.membership.dto.MemberPointsDTO;
import com.gw.membership.dto.MembershipGiftAdminDTO;
import com.gw.membership.entity.MembershipBenefitEntity;
import com.gw.membership.entity.UserMembershipEntity;
import com.gw.membership.mapper.ModelMapperConvert;
import com.gw.membership.service.MembershipBenefitService;
import com.gw.membership.service.UserMembershipService;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.modelmapper.ModelMapper;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 会员服务控制器
 */
@RestController
@RequestMapping("/api/v1/membership")
@RequiredArgsConstructor
@Tag(name = "会员服务接口", description = "会员服务接口相关API")
@Log4j2
public class MembershipController {
    private final UserMembershipService userMembershipService;
    private final RedisTemplate<String, Object> redisTemplate;
    private final CacheProperties cacheProperties;
    private final MembershipBenefitService benefitService;
    private final ObjectMapper objectMapper;
    private final UserProxyService userProxyService;

    /**
     * 获取我的用户会员信息
     */
    @Operation(summary = "获取我的会员信息", description = "获取当前登录用户的会员信息")
    @GetMapping("/my")
    public ResponseResult<UserMembershipVO> getUserMembership() {
        String username = UserContextUtil.getCurrentUsername();

        UserMembershipEntity entity = userMembershipService.findOrInitUserMembership(username);
        ModelMapper modelMapper = ModelMapperConvert.getMemberModelMapper();
        entity = userMembershipService.checkAndUpdateMembershipStatus(entity);
        UserMembershipVO vo = modelMapper.map(entity, UserMembershipVO.class);
        return ResponseResult.success(vo);
    }

    @Operation(summary = "根据用户名查询会员信息", description = "内部接口-根据用户名查询指定用户的会员信息")
    @PostMapping("internal/query_by_username")
    @Hidden
    public ResponseResult<UserMembershipVO> getMembershipByUsername(@RequestBody MembershipQueryDTO params) {
        String username = params.getUsername();
        UserMembershipEntity entity = userMembershipService.findOrInitUserMembership(username);
        log.info("{}会员{}是否过期", username, entity.getVipLevel());

        ModelMapper modelMapper = ModelMapperConvert.getMemberModelMapper();
        entity = userMembershipService.checkAndUpdateMembershipStatus(entity);
        UserMembershipVO vo = modelMapper.map(entity, UserMembershipVO.class);
        return ResponseResult.success(vo);
    }

    private void fillBenefitBaseListVo(UserMembershipBaseVO vo, UserMembershipEntity entity) {
        ModelMapper modelMapper = ModelMapperConvert.getMemberModelMapper();
        if (entity.getPackageDetail() != null) {
            List<MembershipBenefitBaseVO> benefits = entity.getPackageDetail().getBenefitDetails().stream()
                    .map(benefit -> modelMapper.map(benefit, MembershipBenefitBaseVO.class))
                    .toList();
            vo.setBenefits(benefits);
        } else if (entity.getVipLevel() == null || entity.getVipLevel() == 0) {
            List<MembershipBenefitEntity> entities = benefitService.findAllActiveAndVipLevel(MemberConstants.NORMALE_LEVEL);

            vo.setBenefits(entities.stream()
                    .map(benefit -> modelMapper.map(benefit, MembershipBenefitBaseVO.class))
                    .toList());

        } else {
            List<MembershipBenefitEntity> entities = benefitService.findAllActiveAndVipLevel(MemberConstants.VIP_LEVEL);

            vo.setBenefits(entities.stream()
                    .map(benefit -> modelMapper.map(benefit, MembershipBenefitBaseVO.class))
                    .toList());
        }
    }

    @Operation(summary = "根据用户名查询会员信息", description = "内部接口-根据用户名查询指定用户的会员信息")
    @PostMapping("internal/query_base_by_username")
    public ResponseResult<UserMembershipBaseVO> getMembershipBaseContentByUsername(@RequestBody MembershipQueryDTO params) {
        String username = params.getUsername();
        String cacheName = cacheProperties.getCacheName(MemberCacheConstant.MEMBERSHIP_BASE_CACHE_KEY + ":" + username);

        // 首先尝试从缓存中获取数据
        Object cachedObject = redisTemplate.opsForValue().get(cacheName);
        UserMembershipBaseVO cachedValue = null;

        if (cachedObject != null) {
            try {
                // 如果是直接的UserMembershipBaseVO对象
                if (cachedObject instanceof UserMembershipBaseVO) {
                    cachedValue = (UserMembershipBaseVO) cachedObject;
                }
                // 如果是Map类型（通常是从Redis反序列化的结果）
                else if (cachedObject instanceof Map) {
                    cachedValue = objectMapper.convertValue(cachedObject, UserMembershipBaseVO.class);
                }

                // 如果成功获取到缓存数据，直接返回
                if (cachedValue != null) {
                    return ResponseResult.success(cachedValue);
                }
            } catch (Exception e) {
                // 反序列化失败，忽略并继续从数据库加载
                // 可以添加日志记录
                System.err.println("Failed to deserialize cached value: " + e.getMessage());
            }
        }

        // 缓存中没有数据或反序列化失败，从数据库加载
        UserMembershipEntity entity = userMembershipService.findOrInitUserMembership(username);
        ModelMapper modelMapper = ModelMapperConvert.getMemberModelMapper();
        entity = userMembershipService.checkAndUpdateMembershipStatus(entity);
        UserMembershipBaseVO vo = modelMapper.map(entity, UserMembershipBaseVO.class);
        fillBenefitBaseListVo(vo, entity);

        // 存入缓存
        redisTemplate.opsForValue().set(cacheName, vo, cacheProperties.getCacheExpiration(MemberCacheConstant.MEMBERSHIP_CATEGORY_KEY));

        return ResponseResult.success(vo);
    }

    /**
     * 新用户
     */
    @Operation(summary = "新用户注册，赠会员", description = "新用户注册，赠会员")
    @PostMapping("/new_user_reg")
    public ResponseResult<?> newUserReg(@RequestBody @Valid NewUserRegisterDTO params) {
        String username = params.getUsername();
        userMembershipService.newUserRegister(username);
        return ResponseResult.success(null);
    }

    @Operation(summary = "后台赠送会员", description = "管理员后台赠送会员")
    @PostMapping("/admin/gift")
    public ResponseResult<?> adminGiftMembership(@RequestBody @Valid MembershipGiftAdminDTO params) {
        String operator = UserContextUtil.getCurrentUsername();
        UserBaseContentVo user = userProxyService.findByUsername(cacheProperties.getCacheName(UserCommonCacheConstant.USER_BASE_MAP_CACHE_KEY), params.getUsername());
        if (user == null) {
            return ResponseResult.failure(BusinessExceptionCode.FAIL_CODE, "用户不存在");
        }
        if (params.getReason() == null) {
            params.setReason("管理员" + operator + "后台赠送");
        }

        boolean result = userMembershipService.giftMembership(
                params.getUsername(),
                params.getDays(),
                params.getReason(),
                operator
        );

        return ResponseResult.success(result);
    }

    /**
     * 增加会员积分
     */
    @Operation(summary = "增加会员积分", description = "为指定用户增加会员积分")
    @PostMapping("/points/add")
    public ResponseResult<?> addMemberPoints(@RequestBody @Valid MemberPointsDTO params) {
        String operator = UserContextUtil.getCurrentUsername();
        String reason = params.getReason();
        if (reason == null) {
            reason = "管理员" + operator + "手动添加";
        }

        boolean result = userMembershipService.addMemberPoints(
                params.getUsername(),
                params.getPoints(),
                reason
        );
        return ResponseResult.success(result);
    }

    /**
     * 消费会员积分
     */
    @Operation(summary = "消费会员积分", description = "消费指定用户的会员积分")
    @PostMapping("/points/consume")
    public ResponseResult<?> consumeMemberPoints(@RequestBody @Valid MemberPointsDTO params) {
        String operator = UserContextUtil.getCurrentUsername();
        String reason = params.getReason();
        if (reason == null) {
            reason = "管理员" + operator + "手动扣除";
        }

        boolean result = userMembershipService.consumeMemberPoints(
                params.getUsername(),
                params.getPoints(),
                reason
        );
        return ResponseResult.success(result);
    }


}