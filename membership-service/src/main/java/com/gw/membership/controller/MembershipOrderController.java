package com.gw.membership.controller;

import com.alibaba.fastjson2.JSONObject;
import com.github.pagehelper.PageInfo;
import com.gw.common.dto.ItemIdDTO;
import com.gw.common.dto.PageBaseRequest;
import com.gw.common.dto.ResponseResult;
import com.gw.common.exception.BusinessException;
import com.gw.common.user.constant.UserCommonCacheConstant;
import com.gw.common.user.context.UserContextUtil;
import com.gw.common.user.service.UserProxyService;
import com.gw.common.user.vo.UserBaseContentVo;
import com.gw.common.vo.PageBaseContentVo;
import com.gw.common.vo.PaginationVo;
import com.gw.membership.config.CacheProperties;
import com.gw.membership.constant.OrderConstants;
import com.gw.membership.dto.*;
import com.gw.membership.entity.InvitationCodeEntity;
import com.gw.membership.entity.MembershipOrderEntity;
import com.gw.membership.entity.MembershipPackageEntity;
import com.gw.membership.entity.UserExpenseRecordEntity;
import com.gw.membership.mapper.ModelMapperConvert;
import com.gw.membership.service.InvitationService;
import com.gw.membership.service.MembershipOrderService;
import com.gw.membership.service.MembershipPackageService;
import com.gw.membership.service.UserExpenseRecordService;
import com.gw.membership.util.PaymentTraceLogger;
import com.gw.membership.vo.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.modelmapper.ModelMapper;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.gw.common.exception.BusinessExceptionCode.FAIL_CODE;

/**
 * 会员订单服务控制器
 */
@RestController
@RequestMapping("/api/v1/membership/orders")
@RequiredArgsConstructor
@Log4j2
@Tag(name = "会员订单服务接口", description = "会员订单服务接口相关API")
public class MembershipOrderController {
    private final InvitationService inviteService;
    private final MembershipOrderService membershipOrderService;
    private final UserExpenseRecordService userExpenseRecordService;
    private final MembershipPackageService packageService;
    private final UserProxyService userProxyService;
    private final CacheProperties cacheProperties;

    /**
     * 填充订单实体信息
     */
    private void fillOrderEntity(MembershipOrderDTO req, MembershipOrderEntity entity) {
        // 验证和获取套餐信息
        MembershipPackageEntity packageEntity = packageService.findById(req.getPackageId());
        if (packageEntity == null || packageEntity.getStatus() == 0) {
            throw new BusinessException("该套餐已下架或不存在");
        }

        // 处理邀请码
        if (StringUtils.isNotBlank(req.getInvitationCode())) {
            InvitationCodeEntity codeEntity = inviteService.getUserInvitationCode(req.getInvitationCode());
            if (codeEntity == null || codeEntity.getStatus() == 0) {
                throw new BusinessException("无效的邀请码");
            }
            entity.setInvitationCode(req.getInvitationCode());
            entity.setSourceUsername(codeEntity.getUsername());
        }

        // 设置订单基本信息
        entity.setOrderNo(membershipOrderService.generateOrderNo());
        entity.setPackageId(req.getPackageId());
        entity.setPackageName(packageEntity.getName());

        // 计算金额
        BigDecimal amount = packageEntity.getPrice();
        BigDecimal payAmount = packageEntity.getDiscountPrice() != null && packageEntity.getDiscountPrice().compareTo(BigDecimal.ZERO) > 0
                ? packageEntity.getDiscountPrice()
                : packageEntity.getPrice();
        BigDecimal discountAmount = amount.subtract(payAmount);

        entity.setAmount(amount);
        entity.setPayAmount(payAmount);
        entity.setDiscountAmount(discountAmount);

        // 设置订单状态为未支付
        entity.setStatus(OrderConstants.STATUS_UNPAID);

        // 设置订单类型
        Integer orderType = entity.getOrderType();
        entity.setOrderType(orderType != null ? orderType : 1); // 默认为新购

        // 设置支付方式
        entity.setPayMethod(req.getPayMethod());

        // 设置有效期(天) - 根据套餐类型计算
        int validDays = calculateValidDays(packageEntity);
        entity.setValidDays(validDays);

        // 设置备注
        entity.setRemark(req.getRemark());

        // 设置创建时间和更新时间
        LocalDateTime now = LocalDateTime.now();
        entity.setCreateTime(now);
        entity.setUpdateTime(now);
    }

    /**
     * 计算套餐有效期天数
     */
    private int calculateValidDays(MembershipPackageEntity packageEntity) {
        Integer packageType = packageEntity.getType();
        return switch (packageType) {
            case 1 -> 30;     // 包月
            case 2 -> 90;     // 包季
            case 3 -> 365;    // 包年
            case 4 -> 36500;  // 终身(100年)
            default -> packageEntity.getValidDays() != null ? packageEntity.getValidDays() : 0;
        };
    }

    /**
     * 创建会员订单
     */
    @Operation(summary = "创建会员订单", description = "创建会员订单")
    @PostMapping("")
    public ResponseResult<MembershipOrderVO> createOrder(@RequestBody @Valid MembershipOrderDTO orderDTO) {
        String username = UserContextUtil.getCurrentUsername();
        MembershipOrderEntity entity = new MembershipOrderEntity();

        try {
            fillOrderEntity(orderDTO, entity);
            entity.setCreator(username);
            entity.setUpdater(username);
            entity.setUsername(username);

            MembershipOrderEntity result = membershipOrderService.createOrder(entity);
            ModelMapper modelMapper = ModelMapperConvert.getMembershipOrderModelMapper();
            MembershipOrderVO vo = modelMapper.map(result, MembershipOrderVO.class);

            return ResponseResult.success(vo);
        } catch (BusinessException e) {
            log.error("创建订单失败: {}", e.getMessage());
            return ResponseResult.failure(e.getMessage());
        } catch (Exception e) {
            log.error("创建订单异常", e);
            return ResponseResult.failure("创建订单失败，请稍后重试");
        }
    }

    /**
     * 获取订单详情
     */
    @Operation(summary = "获取订单详情", description = "获取订单详情")
    @PostMapping("/get")
    public ResponseResult<MembershipOrderVO> getOrderDetail(@RequestBody @Valid ItemIdDTO itemIdDTO) {
        try {
            MembershipOrderEntity entity = membershipOrderService.findOrderById(itemIdDTO.getId());
            if (entity == null) {
                return ResponseResult.failure("订单不存在");
            }

            ModelMapper modelMapper = ModelMapperConvert.getMembershipOrderModelMapper();
            MembershipOrderVO vo = modelMapper.map(entity, MembershipOrderVO.class);

            return ResponseResult.success(vo);
        } catch (Exception e) {
            log.error("获取订单详情失败", e);
            return ResponseResult.failure("获取订单详情失败");
        }
    }

    @Operation(summary = "后台使能订单", description = "后台使能订单")
    @PostMapping("/active")
    public ResponseResult<?> activeOrder(@RequestBody @Valid ActiveOrderDTO params) {
        String orderNo = params.getOrderNo();
        String operator = UserContextUtil.getCurrentUsername();
        log.info("后台激活订单请求: orderNo={}, operator={}", orderNo, operator);

        try {
            membershipOrderService.backendActivateOrder(orderNo, operator);
            return ResponseResult.success("订单已激活成功");
        } catch (BusinessException e) {
            log.error("后台激活订单失败: {}", e.getMessage());
            return ResponseResult.failure(e.getMessage());
        } catch (Exception e) {
            log.error("后台激活订单异常: orderNo={}", orderNo, e);
            return ResponseResult.failure("激活订单异常: " + e.getMessage());
        }
    }

    /**
     * 获取用户订单列表
     */
    @Operation(summary = "获取我的用户订单列表", description = "获取用户订单列表")
    @PostMapping("/page/my")
    public ResponseResult<PageBaseContentVo<MembershipOrderVO>> getMyOrders(@RequestBody @Valid PageBaseRequest<MemberShipOrderQueryDTO> params) {
        try {
            String username = UserContextUtil.getCurrentUsername();
            if (params.getFilter() == null) {
                params.setFilter(new MemberShipOrderQueryDTO());
            }

            params.getFilter().setUsername(username);
            PageInfo<MembershipOrderEntity> orderPages = membershipOrderService.page(
                    params.getCurrent(),
                    params.getPageSize(),
                    params.getFilter()
            );

            return buildOrderPageResponse(orderPages);
        } catch (Exception e) {
            log.error("获取我的订单列表失败", e);
            return ResponseResult.failure("获取订单列表失败");
        }
    }

    @Operation(summary = "后台获取用户订单列表", description = "后台获取用户订单列表")
    @PostMapping("/admin/page")
    public ResponseResult<PageBaseContentVo<MembershipOrderVO>> adminGetOrders(@RequestBody @Valid PageBaseRequest<MemberShipOrderQueryDTO> params) {
        try {
            if (params.getFilter() == null) {
                params.setFilter(new MemberShipOrderQueryDTO());
            }

            MemberShipOrderQueryDTO filter = params.getFilter();
            if (StringUtils.isNotBlank(filter.getQueryValue())) {
                List<String> matchedUsernames = findMatchingUsers(filter.getQueryValue());

                if (matchedUsernames.isEmpty()) {
                    // 没有匹配的用户，返回空结果
                    return ResponseResult.success(new PageBaseContentVo<>(new ArrayList<>(), new PaginationVo(0, 1, params.getPageSize())));
                }

                filter.setUsernames(matchedUsernames);
            }

            PageInfo<MembershipOrderEntity> orderPages = membershipOrderService.page(
                    params.getCurrent(),
                    params.getPageSize(),
                    filter
            );

            return buildOrderPageResponse(orderPages);
        } catch (Exception e) {
            log.error("后台获取用户订单列表失败", e);
            return ResponseResult.failure("获取订单列表失败");
        }
    }

    /**
     * 查找匹配查询条件的用户
     */
    private List<String> findMatchingUsers(String queryValue) {

        List<UserBaseContentVo> users = userProxyService.findAllUserList(cacheProperties.getCacheName(UserCommonCacheConstant.USER_BASE_MAP_CACHE_KEY));
        String lowerQuery = queryValue.toLowerCase();

        return users.stream()
                .filter(user -> isUserMatching(user, lowerQuery))
                .map(UserBaseContentVo::getUsername)
                .collect(Collectors.toList());
    }

    /**
     * 检查用户是否匹配查询条件
     */
    private boolean isUserMatching(UserBaseContentVo user, String queryValue) {
        return (user.getIdentify() != null && user.getIdentify().toLowerCase().contains(queryValue)) ||
                (user.getPhone() != null && user.getPhone().toLowerCase().contains(queryValue)) ||
                (user.getUsername() != null && user.getUsername().toLowerCase().contains(queryValue)) ||
                (user.getNickname() != null && user.getNickname().toLowerCase().contains(queryValue));
    }

    /**
     * 构建订单分页响应
     */
    private ResponseResult<PageBaseContentVo<MembershipOrderVO>> buildOrderPageResponse(PageInfo<MembershipOrderEntity> orderPages) {
        ModelMapper modelMapper = ModelMapperConvert.getMembershipOrderModelMapper();
        PaginationVo pagination = new PaginationVo(
                orderPages.getTotal(),
                orderPages.getPageNum(),
                orderPages.getPageSize());

        if (orderPages.getTotal() > 0) {
            List<MembershipOrderVO> rows = orderPages.getList().stream()
                    .map(order -> modelMapper.map(order, MembershipOrderVO.class))
                    .collect(Collectors.toList());

            return ResponseResult.success(new PageBaseContentVo<>(rows, pagination));
        }

        return ResponseResult.success(new PageBaseContentVo<>(new ArrayList<>(), pagination));
    }
    @Operation(summary = "查询IOS是否可以支付", description = "查询IOS是否可以支付")
    @GetMapping("/query/iot_pay_enable")
    public ResponseResult<IOSPayQueryVO> getIOSPayQuery(){
        return ResponseResult.success(new IOSPayQueryVO());
    }
    /**
     * 获取用户支出明细
     */
    @Operation(summary = "获取我的用户订单支出明细列表", description = "我的订单支出明细列表")
    @PostMapping("/expenses/page/my")
    public ResponseResult<PageBaseContentVo<UserExpenseRecordVO>> getMyExpensesPage(@RequestBody @Valid PageBaseRequest<UserExpenseRecordQueryDTO> params) {
        try {
            String username = UserContextUtil.getCurrentUsername();
            if (params.getFilter() == null) {
                params.setFilter(new UserExpenseRecordQueryDTO());
            }

            params.getFilter().setUsername(username);
            PageInfo<UserExpenseRecordEntity> expenses = userExpenseRecordService.page(
                    params.getCurrent(),
                    params.getPageSize(),
                    params.getFilter()
            );

            ModelMapper modelMapper = ModelMapperConvert.getOrderExpenseModelMapper();
            PaginationVo pagination = new PaginationVo(
                    expenses.getTotal(),
                    expenses.getPageNum(),
                    expenses.getPageSize());

            if (expenses.getTotal() > 0) {
                List<UserExpenseRecordVO> rows = expenses.getList().stream()
                        .map(record -> modelMapper.map(record, UserExpenseRecordVO.class))
                        .collect(Collectors.toList());

                return ResponseResult.success(new PageBaseContentVo<>(rows, pagination));
            }

            return ResponseResult.success(new PageBaseContentVo<>(new ArrayList<>(), pagination));
        } catch (Exception e) {
            log.error("获取支出明细失败", e);
            return ResponseResult.failure("获取支出明细失败");
        }
    }

    /**
     * 微信小程序支付
     */
    @Operation(summary = "为订单生成微信小程序支付参数", description = "为订单生成微信小程序支付参数")
    @PostMapping("/wxpay")
    public ResponseResult<WxPayParamsVO> wxPayment(@Valid @RequestBody WxPayRequestDTO request) {
        String username = UserContextUtil.getCurrentUsername();
        String orderNo = request.getOrderNo();
        // 启动支付流程跟踪
        String traceId = PaymentTraceLogger.startTrace(orderNo, "微信小程序支付");

        try {
            // 验证订单是否属于当前用户
            PaymentTraceLogger.traceStep(traceId, "订单验证", "验证订单归属权");
            MembershipOrderEntity order = membershipOrderService.getOrderDetail(orderNo);
            if (order == null) {
                PaymentTraceLogger.traceError(traceId, orderNo, "订单不存在", null);
                return ResponseResult.failure(FAIL_CODE.getCode(), "订单不存在");
            }

            if (!order.getUsername().equals(username)) {
                PaymentTraceLogger.traceError(traceId, orderNo, "无权操作此订单", null);
                return ResponseResult.failure(FAIL_CODE.getCode(), "无权操作此订单");
            }

            PaymentTraceLogger.traceStep(traceId, "用户信息", "获取用户微信OpenID");
            UserBaseContentVo user = userProxyService.findByUsername(cacheProperties.getCacheName(UserCommonCacheConstant.USER_BASE_MAP_CACHE_KEY), username);
            if (user == null) {
                log.error("获取用户信息失败: username = {}", username);
                PaymentTraceLogger.traceError(traceId, orderNo, "获取用户信息失败", null);
                return ResponseResult.failure(FAIL_CODE.getCode(), "获取用户信息失败: " + username);
            }

            if (StringUtils.isBlank(user.getWxOpenId())) {
                PaymentTraceLogger.traceError(traceId, orderNo, "用户未绑定微信", null);
                return ResponseResult.failure(FAIL_CODE.getCode(), "请先绑定微信账号");
            }

            try {
                // 获取JSAPI支付需要的预支付交易会话标识(prepay_id)
                PaymentTraceLogger.traceStep(traceId, "获取预支付ID", "调用微信支付接口");
                String prepayId = membershipOrderService.getWxJsapiPrepayId(orderNo, user.getWxOpenId());

                // 构建微信小程序支付参数
                PaymentTraceLogger.traceStep(traceId, "生成支付参数", "生成签名等支付参数");
                Map<String, Object> payParams = membershipOrderService.buildWxJsapiPayParams(prepayId);

                WxPayParamsVO payParamsVO = new WxPayParamsVO(payParams);
                PaymentTraceLogger.completeTrace(traceId, orderNo, "成功");
                return ResponseResult.success(payParamsVO);
            } catch (Exception e) {
                PaymentTraceLogger.traceError(traceId, orderNo, "生成支付参数失败: " + e.getMessage(), e);
                return ResponseResult.failure(FAIL_CODE.getCode(), "生成支付参数失败: " + e.getMessage());
            }
        } catch (Exception e) {
            PaymentTraceLogger.traceError(traceId, orderNo, "支付流程异常: " + e.getMessage(), e);
            return ResponseResult.failure(FAIL_CODE.getCode(), "支付请求处理失败: " + e.getMessage());
        }
    }

    /**
     * 微信支付回调通知
     */
    @PostMapping("/wxpay/notify")
    public Map<String, String> wxPayNotify(@RequestBody String notifyData, @RequestHeader Map<String, String> headers) {
        log.info("接收到微信支付回调通知");
        Map<String, String> response = new HashMap<>();
        String orderNo = null;
        String tradeNo;

        try {
            // 1. 获取验证需要的参数
            Map<String, String> headerParams = extractWxPayHeaders(headers);
            String timestamp = headerParams.get("timestamp");
            String nonce = headerParams.get("nonce");
            String signature = headerParams.get("signature");
            String serialNo = headerParams.get("serialNo");

            // 验证参数完整性
            if (timestamp == null || nonce == null || signature == null || serialNo == null) {
                logHeaderError(headers);
                response.put("code", "FAIL");
                response.put("message", "缺少必要的请求头参数");
                return response;
            }

            // 2. 验证签名
            boolean isSignValid = membershipOrderService.verifyNotifySign(serialNo, timestamp, nonce, signature, notifyData);
            if (!isSignValid) {
                log.error("微信支付回调通知签名验证失败");
                response.put("code", "FAIL");
                response.put("message", "签名验证失败");
                return response;
            }

            // 3. 解密回调数据
            String decryptData = membershipOrderService.decryptNotifyData(notifyData);
            log.info("解密后的回调数据: {}", decryptData);

            // 4. 解析回调数据
            JSONObject jsonData = JSONObject.parseObject(decryptData);
            if (jsonData == null) {
                log.error("微信支付回调数据格式错误，解析JSON失败");
                response.put("code", "FAIL");
                response.put("message", "回调数据格式错误");
                return response;
            }

            orderNo = jsonData.getString("out_trade_no");
            tradeNo = jsonData.getString("transaction_id");
            String tradeState = jsonData.getString("trade_state");
            String payTime = jsonData.getString("success_time");

            // 记录支付回调信息
            if (orderNo != null && tradeNo != null) {
                PaymentTraceLogger.traceCallback(orderNo, tradeNo, "状态:" + tradeState);
            }

            // 验证必要字段
            if (orderNo == null || tradeNo == null || tradeState == null) {
                log.error("微信支付回调数据缺少必要字段: orderNo={}, tradeNo={}, tradeState={}",
                        orderNo, tradeNo, tradeState);
                response.put("code", "FAIL");
                response.put("message", "回调数据缺少必要字段");
                return response;
            }

            // 5. 处理支付结果
            log.info("处理支付结果: orderNo={}, tradeNo={}, tradeState={}", orderNo, tradeNo, tradeState);

            if ("SUCCESS".equals(tradeState)) {
                boolean success = membershipOrderService.handlePaymentCallback(orderNo, tradeNo, payTime);
                if (success) {
                    PaymentTraceLogger.completeTrace(null, orderNo, "支付成功并完成会员激活");
                    response.put("code", "SUCCESS");
                    response.put("message", "成功");
                } else {
                    PaymentTraceLogger.traceError(null, orderNo, "处理支付回调业务失败", null);
                    log.error("处理支付回调业务失败: orderNo={}, tradeNo={}", orderNo, tradeNo);
                    response.put("code", "FAIL");
                    response.put("message", "处理支付结果失败");
                }
            } else {
                log.warn("支付未成功，状态: {}, 订单号: {}", tradeState, orderNo);
                response.put("code", "SUCCESS"); // 即使不是成功状态也返回成功，避免微信重复通知
                response.put("message", "已接收");
            }
            return response;
        } catch (Exception e) {
            if (orderNo != null) {
                PaymentTraceLogger.traceError(null, orderNo, "处理微信支付回调通知失败", e);
            }
            log.error("处理微信支付回调通知失败", e);
            response.put("code", "FAIL");
            response.put("message", "系统处理异常");
            return response;
        }
    }

    /**
     * 提取微信支付回调头信息
     */
    private Map<String, String> extractWxPayHeaders(Map<String, String> headers) {
        Map<String, String> result = new HashMap<>();

        String timestamp = headers.get("Wechatpay-Timestamp");
        if (timestamp == null) {
            timestamp = headers.get("wechatpay-timestamp");
        }
        result.put("timestamp", timestamp);

        String nonce = headers.get("Wechatpay-Nonce");
        if (nonce == null) {
            nonce = headers.get("wechatpay-nonce");
        }
        result.put("nonce", nonce);

        String signature = headers.get("Wechatpay-Signature");
        if (signature == null) {
            signature = headers.get("wechatpay-signature");
        }
        result.put("signature", signature);

        String serialNo = headers.get("Wechatpay-Serial");
        if (serialNo == null) {
            serialNo = headers.get("wechatpay-serial");
        }
        result.put("serialNo", serialNo);

        return result;
    }

    /**
     * 记录请求头错误信息
     */
    private void logHeaderError(Map<String, String> headers) {
        log.error("微信支付回调缺少必要的请求头参数");
        log.error("Headers received - keys and values: {}", headers.entrySet().stream()
                .map(entry -> entry.getKey() + ": " + entry.getValue())
                .collect(Collectors.joining(", ")));
    }

    /**
     * 获取微信H5支付二维码URL
     */
    @Operation(summary = "获取微信H5支付二维码URL", description = "生成微信支付二维码链接")
    @PostMapping("/wxpay/qrcode")
    public ResponseResult<WxPayQrCodeVO> getWxQrCodePayUrl(@Valid @RequestBody WxPayRequestDTO request) {
        String username = UserContextUtil.getCurrentUsername();
        String orderNo = request.getOrderNo();
        String traceId = PaymentTraceLogger.startTrace(orderNo, "微信H5二维码支付");

        try {
            // 验证订单是否属于当前用户
            PaymentTraceLogger.traceStep(traceId, "订单验证", "验证订单归属权");
            MembershipOrderEntity order = membershipOrderService.getOrderDetail(orderNo);
            if (order == null) {
                PaymentTraceLogger.traceError(traceId, orderNo, "订单不存在", null);
                return ResponseResult.failure(FAIL_CODE.getCode(), "订单不存在");
            }

            if (!order.getUsername().equals(username)) {
                PaymentTraceLogger.traceError(traceId, orderNo, "无权操作此订单", null);
                return ResponseResult.failure(FAIL_CODE.getCode(), "无权操作此订单");
            }

            // 获取微信H5支付二维码URL
            PaymentTraceLogger.traceStep(traceId, "生成二维码URL", "调用微信支付接口");
            String qrCodeUrl = membershipOrderService.getWxQrCodePayUrl(orderNo);

            if (StringUtils.isNotBlank(qrCodeUrl)) {
                PaymentTraceLogger.completeTrace(traceId, orderNo, "成功生成二维码URL");
                return ResponseResult.success(new WxPayQrCodeVO(qrCodeUrl));
            } else {
                PaymentTraceLogger.traceError(traceId, orderNo, "生成二维码URL失败", null);
                return ResponseResult.failure(FAIL_CODE.getCode(), "生成二维码URL失败");
            }
        } catch (Exception e) {
            PaymentTraceLogger.traceError(traceId, orderNo, "生成二维码URL异常: " + e.getMessage(), e);
            return ResponseResult.failure(FAIL_CODE.getCode(), "生成二维码URL异常: " + e.getMessage());
        }
    }
} 