package com.gw.membership.controller;

import com.gw.common.dto.ItemIdDTO;
import com.gw.common.dto.ResponseResult;
import com.gw.common.exception.BusinessException;
import com.gw.common.exception.BusinessExceptionCode;
import com.gw.common.user.context.UserContextUtil;
import com.gw.membership.constant.MemberConstants;
import com.gw.membership.dto.MembershipPackageDTO;
import com.gw.membership.dto.MembershipPackageQueryByTypeDTO;
import com.gw.membership.dto.MembershipPackageStatusDTO;
import com.gw.membership.dto.MembershipPackageUpdateDTO;
import com.gw.membership.entity.MembershipBenefitEntity;
import com.gw.membership.entity.MembershipPackageEntity;
import com.gw.membership.mapper.ModelMapperConvert;
import com.gw.membership.service.MembershipBenefitService;
import com.gw.membership.service.MembershipPackageService;
import com.gw.membership.vo.BenefitCategoryVO;
import com.gw.membership.vo.MembershipPackageVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.modelmapper.ModelMapper;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * 会员套餐控制器
 */
@RestController
@RequestMapping("/api/v1/membership/packages")
@RequiredArgsConstructor
@Tag(name = "会员套餐管理", description = "会员套餐相关API")
@Log4j2
public class MembershipPackageController {

    private final MembershipPackageService packageService;
    private final MembershipBenefitService benefitService;

    private void fillEntity(MembershipPackageEntity entity, MembershipPackageDTO req) {
        List<MembershipBenefitEntity> benefitEntities = benefitService.findByVipLevel(MemberConstants.VIP_LEVEL);
        if (benefitEntities.isEmpty()) {
            throw new BusinessException("参数里有不存在的会员权益");
        }
        entity.setBenefitDetails(benefitEntities);
        BeanUtils.copyProperties(req, entity);
        entity.setUpdateTime(LocalDateTime.now());

    }

    /**
     * 创建会员套餐
     */
    @Operation(summary = "创建会员套餐", description = "创建新的会员套餐")
    @PostMapping
    public ResponseResult<?> createPackage(@RequestBody @Valid MembershipPackageDTO packageDTO) {
        boolean ret = packageService.existsByName(packageDTO.getName());
        if (ret) {
            return ResponseResult.failure(BusinessExceptionCode.FAIL_CODE, "套餐名称已存在");
        }
        String username = UserContextUtil.getCurrentUsername();
        MembershipPackageEntity entity = new MembershipPackageEntity();
        fillEntity(entity, packageDTO);
        entity.setCreator(username);
        entity.setCreateTime(LocalDateTime.now());
        entity.setUpdater(username);
        packageService.createPackage(entity);
        return ResponseResult.success(null);
    }

    /**
     * 更新会员套餐
     */
    @Operation(summary = "更新会员套餐", description = "更新已有的会员套餐")
    @PostMapping("/update")
    public ResponseResult<?> updatePackage(
            @RequestBody @Valid MembershipPackageUpdateDTO packageDTO) {
        boolean ret = packageService.existsByNameAndIdNot(packageDTO.getName(), packageDTO.getId());
        if (ret) {
            return ResponseResult.failure(BusinessExceptionCode.FAIL_CODE, "套餐名称已存在");
        }
        MembershipPackageEntity entity = packageService.findById(packageDTO.getId());
        fillEntity(entity, packageDTO);
        String username = UserContextUtil.getCurrentUsername();
        entity.setUpdater(username);

        entity.setUpdateTime(LocalDateTime.now());
        packageService.updatePackage(entity);
        return ResponseResult.success(null);
    }

    /**
     * 删除会员套餐
     */
    @Operation(summary = "删除会员套餐", description = "删除指定的会员套餐")
    @PostMapping("delete")
    public ResponseResult<?> deletePackage(@RequestBody @Valid ItemIdDTO packageDTO) {
        MembershipPackageEntity entity = packageService.findById(packageDTO.getId());
        String username = UserContextUtil.getCurrentUsername();
        entity.setUpdater(username);
        entity.setUpdateTime(LocalDateTime.now());
        packageService.deletePackage(entity);
        return ResponseResult.success(null);
    }

    public void fillBenefits(MembershipPackageVO vo, MembershipPackageEntity entity) {
        BenefitCategoryVO categoryVO = MemberConstants.BENEFIT_CATEGORY_LIST.stream().filter(benefitCategory -> Objects.equals(benefitCategory.getVipLevel(), entity.getVipLevel())).findFirst().get();
        vo.setBenefits(List.of(categoryVO));
    }

    /**
     * 获取会员套餐详情
     */
    @Operation(summary = "获取会员套餐详情", description = "获取指定会员套餐的详细信息")
    @GetMapping("/get")
    public ResponseResult<MembershipPackageVO> getPackageDetail(
            @RequestBody @Valid ItemIdDTO packageDTO) {
        MembershipPackageEntity entity = packageService.findById(packageDTO.getId());
        ModelMapper mapper = ModelMapperConvert.getPackageModelMapper();
        MembershipPackageVO vo = mapper.map(entity, MembershipPackageVO.class);
        fillBenefits(vo, entity);
        return ResponseResult.success(vo);
    }

    /**
     * 获取所有会员套餐
     */
    @Operation(summary = "获取所有会员套餐", description = "获取所有会员套餐列表")
    @GetMapping("/get_all")
    public ResponseResult<List<MembershipPackageVO>> getAllPackages() {
        List<MembershipPackageEntity> packages = packageService.findAllPackages();
        ModelMapper mapper = ModelMapperConvert.getPackageModelMapper();
        List<MembershipPackageVO> vos = packages.stream().map(p -> mapper.map(p, MembershipPackageVO.class)).toList();
        vos.forEach(item -> {
            BenefitCategoryVO categoryVO = MemberConstants.BENEFIT_CATEGORY_LIST.stream().filter(benefitCategory -> Objects.equals(benefitCategory.getVipLevel(), item.getVipLevel())).findFirst().get();
            item.setBenefits(List.of(categoryVO));
        });
        return ResponseResult.success(vos);
    }

    /**
     * 获取所有上架的会员套餐
     */
    @Operation(summary = "获取所有上架的会员套餐", description = "获取所有上架状态的会员套餐")
    @GetMapping("/active")
    public ResponseResult<List<MembershipPackageVO>> getAllActivePackages() {
        List<MembershipPackageEntity> packages = packageService.getAllActivePackages();
        ModelMapper mapper = ModelMapperConvert.getPackageModelMapper();
        List<MembershipPackageVO> vos = packages.stream().map(p -> mapper.map(p, MembershipPackageVO.class)).toList();
        vos.forEach(item -> {
            BenefitCategoryVO categoryVO = MemberConstants.BENEFIT_CATEGORY_LIST.stream().filter(benefitCategory -> Objects.equals(benefitCategory.getVipLevel(), item.getVipLevel())).findFirst().get();
            item.setBenefits(List.of(categoryVO));
        });
        return ResponseResult.success(vos);
    }

    /**
     * 获取推荐的会员套餐
     */
    @Operation(summary = "获取推荐的会员套餐", description = "获取被推荐的会员套餐列表")
    @GetMapping("/recommended")
    public ResponseResult<List<MembershipPackageVO>> getRecommendedPackages() {
        List<MembershipPackageEntity> packages = packageService.getRecommendedPackages();
        ModelMapper mapper = ModelMapperConvert.getPackageModelMapper();
        List<MembershipPackageVO> vos = packages.stream().map(p -> mapper.map(p, MembershipPackageVO.class)).toList();
        vos.forEach(item -> {
            BenefitCategoryVO categoryVO = MemberConstants.BENEFIT_CATEGORY_LIST.stream().filter(benefitCategory -> Objects.equals(benefitCategory.getVipLevel(), item.getVipLevel())).findFirst().get();
            item.setBenefits(List.of(categoryVO));
        });
        return ResponseResult.success(vos);
    }

    /**
     * 根据类型获取会员套餐
     */
    @Operation(summary = "根据类型获取会员套餐", description = "获取指定类型的会员套餐")
    @GetMapping("/get-by-type")
    public ResponseResult<List<MembershipPackageVO>> getPackagesByType(
            @RequestBody @Valid MembershipPackageQueryByTypeDTO params) {
        List<MembershipPackageEntity> packages = packageService.getPackagesByType(params.getType());
        ModelMapper mapper = ModelMapperConvert.getPackageModelMapper();
        List<MembershipPackageVO> vos = packages.stream().map(p -> mapper.map(p, MembershipPackageVO.class)).toList();
        vos.forEach(item -> {
            BenefitCategoryVO categoryVO = MemberConstants.BENEFIT_CATEGORY_LIST.stream().filter(benefitCategory -> Objects.equals(benefitCategory.getVipLevel(), item.getVipLevel())).findFirst().get();
            item.setBenefits(List.of(categoryVO));
        });
        return ResponseResult.success(vos);
    }

    /**
     * 更新套餐状态
     */
    @Operation(summary = "更新套餐状态", description = "更新指定套餐的状态")
    @PutMapping("/update-status")
    public ResponseResult<?> updatePackageStatus(@RequestBody @Valid MembershipPackageStatusDTO params) {
        MembershipPackageEntity entity = packageService.findById(params.getId());
        packageService.updatePackageStatus(entity, params.getStatus());
        return ResponseResult.success(null);
    }
}