package com.gw.membership.controller;

import com.gw.common.dto.ResponseResult;
import com.gw.common.dto.TimeRangeDTO;
import com.gw.membership.service.UserMembershipService;
import com.gw.membership.vo.MembershipStatisticsVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * 会员统计控制器
 */
@RestController
@RequestMapping("/api/v1/membership/statistics")
@RequiredArgsConstructor
@Tag(name = "会员统计", description = "会员相关统计API")
public class StatisticsController {

    private final UserMembershipService userMembershipService;

    /**
     * 计算指定时间范围内的收款金额
     */
    @PostMapping("/income")
    @Operation(summary = "计算指定时间范围内的收款金额")
    public ResponseResult<Double> calculateIncomeByTimeRange(@RequestBody TimeRangeDTO req) {
        double income = userMembershipService.calculateTodayIncome(req.getStartTime(), req.getEndTime());
        return ResponseResult.success(income);
    }

    /**
     * 统计VIP用户总数
     */
    @GetMapping("/vip-count")
    @Operation(summary = "统计VIP用户总数")
    public ResponseResult<Integer> countTotalVipUsers() {
        int count = userMembershipService.countTotalVipUsers();
        return ResponseResult.success(count);
    }

    /**
     * 计算历史收款总金额
     */
    @GetMapping("/total-income")
    @Operation(summary = "计算历史收款总金额")
    public ResponseResult<Double> calculateTotalIncome() {
        double totalIncome = userMembershipService.calculateTotalIncome();
        return ResponseResult.success(totalIncome);
    }

    /**
     * 获取会员统计数据
     */
    @GetMapping("/summary")
    @Operation(summary = "获取会员统计数据", description = "获取今日收款金额、VIP用户总数、历史收款总金额等统计数据")
    public ResponseResult<MembershipStatisticsVO> getMembershipStatistics() {
        // 获取今日时间范围
        LocalDateTime todayStart = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);
        LocalDateTime todayEnd = LocalDateTime.of(LocalDate.now(), LocalTime.MAX);

        // 计算今日收款金额
        double todayIncome = userMembershipService.calculateTodayIncome(todayStart, todayEnd);

        // 统计VIP用户总数
        int totalVipUsers = userMembershipService.countTotalVipUsers();

        // 计算历史收款总金额
        double totalIncome = userMembershipService.calculateTotalIncome();

        // 构建VO对象
        MembershipStatisticsVO statisticsVO = MembershipStatisticsVO.builder()
                .todayIncome(todayIncome)
                .totalVipUsers(totalVipUsers)
                .totalIncome(totalIncome)
                .build();

        return ResponseResult.success(statisticsVO);
    }
} 