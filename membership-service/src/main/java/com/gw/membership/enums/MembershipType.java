package com.gw.membership.enums;

import lombok.Getter;

/**
 * 会员套餐类型枚举
 */
@Getter
public enum MembershipType {

    /**
     * 按日计算
     */
    DAILY(0),

    /**
     * 包月
     */
    MONTHLY(1),

    /**
     * 包季
     */
    QUARTERLY(2),

    /**
     * 包年
     */
    YEARLY(3),

    /**
     * 终身会员
     */
    LIFETIME(255);

    private final int code;

    MembershipType(int code) {
        this.code = code;
    }

    public int getCode() {
        return code;
    }
} 