package com.gw.membership.config;

import org.modelmapper.ModelMapper;
import org.modelmapper.convention.MatchingStrategies;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * ModelMapper配置类
 */
@Configuration
public class ModelMapperConfig {

    /**
     * 创建ModelMapper Bean
     *
     * @return ModelMapper实例
     */
    @Bean
    public ModelMapper modelMapper() {
        ModelMapper modelMapper = new ModelMapper();

        // 配置ModelMapper
        modelMapper.getConfiguration()
                .setMatchingStrategy(MatchingStrategies.STRICT) // 使用严格匹配策略
                .setSkipNullEnabled(true); // 跳过空值

        return modelMapper;
    }
}
