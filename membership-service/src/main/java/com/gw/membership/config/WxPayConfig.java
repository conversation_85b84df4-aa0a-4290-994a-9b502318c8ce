package com.gw.membership.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 微信支付配置
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "wx.pay")
public class WxPayConfig {

    /**
     * 微信小程序appId
     */
    private String appId;

    /**
     * 微信支付商户号
     */
    private String mchId;

    /**
     * 微信支付商户API证书序列号
     */
    private String mchSerialNo;

    /**
     * 微信支付商户私钥文件路径
     */
    private String privateKeyPath;

    /**
     * 微信支付商户API v3密钥
     */
    private String apiV3Key;

    /**
     * 支付结果通知地址
     */
    private String notifyUrl;
} 