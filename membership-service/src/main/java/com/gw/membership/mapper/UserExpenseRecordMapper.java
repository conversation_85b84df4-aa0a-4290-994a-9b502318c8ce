package com.gw.membership.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gw.membership.dto.UserExpenseRecordQueryDTO;
import com.gw.membership.entity.UserExpenseRecordEntity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 用户支出明细Mapper
 */
@Mapper
public interface UserExpenseRecordMapper extends BaseMapper<UserExpenseRecordEntity> {
    List<UserExpenseRecordEntity> page(UserExpenseRecordQueryDTO query);
} 