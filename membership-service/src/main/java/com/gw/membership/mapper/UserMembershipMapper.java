package com.gw.membership.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gw.membership.dto.UserMembershipQueryDTO;
import com.gw.membership.entity.UserMembershipEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 用户会员Mapper接口
 */
@Mapper
public interface UserMembershipMapper extends BaseMapper<UserMembershipEntity> {

    /**
     * 根据用户ID查询会员信息
     *
     * @param username 用户ID
     * @return 用户会员信息
     */
    @Select("SELECT * FROM t_user_membership WHERE username = #{username} AND deleted = 0 LIMIT 1")
    UserMembershipEntity selectByUsername(@Param("username") String username);

    @Select("SELECT * FROM t_user_membership WHERE username = #{username} AND deleted = 0 LIMIT 1")
    Optional<UserMembershipEntity> findByUsername(String username);


    /**
     * 查询即将过期的会员
     *
     * @param expireStart 过期开始时间
     * @param expireEnd   过期结束时间
     * @return 即将过期的会员列表
     */
    @Select("SELECT * FROM t_user_membership WHERE status = 1 AND expire_time BETWEEN #{expireStart} AND #{expireEnd} AND deleted = 0")
    List<UserMembershipEntity> selectSoonToExpire(@Param("expireStart") LocalDateTime expireStart,
                                                  @Param("expireEnd") LocalDateTime expireEnd);

    /**
     * 查询已过期的会员
     *
     * @param now 当前时间
     * @return 已过期的会员列表
     */
    @Select("SELECT * FROM t_user_membership WHERE status = 1 AND expire_time < #{now} AND deleted = 0")
    List<UserMembershipEntity> selectExpired(@Param("now") LocalDateTime now);

    /**
     * 更新会员状态为非会员
     *
     * @param username 用户ID
     * @return 影响行数
     */
    @Update("UPDATE t_user_membership SET status = 0, update_time = NOW() WHERE username = #{username} AND deleted = 0")
    int updateStatusToNonMember(@Param("username") String username);

    List<UserMembershipEntity> page(UserMembershipQueryDTO query);

    /**
     * 计算指定时间范围内的收款金额
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 收款金额
     */
    @Select("SELECT COALESCE(SUM(amount), 0) FROM t_user_expense_record WHERE create_time BETWEEN #{startTime} AND #{endTime} AND status = 1 AND deleted = 0")
    double calculateIncomeByTimeRange(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 统计VIP用户总数
     *
     * @return VIP用户总数
     */
    @Select("SELECT COUNT(*) FROM t_user_membership WHERE vip_level > 0 AND deleted = 0")
    int countTotalVipUsers();

    /**
     * 计算历史收款总金额
     *
     * @return 历史收款总金额
     */
    @Select("SELECT COALESCE(SUM(amount), 0) FROM t_user_expense_record WHERE status = 1 AND deleted = 0")
    double calculateTotalIncome();

    /**
     * 批量根据用户名查询会员信息
     *
     * @param usernames 用户名列表
     * @return 用户会员信息列表
     */
    @Select("<script>SELECT * FROM t_user_membership " +
            "WHERE username IN " +
            "<foreach collection='usernames' item='username' open='(' separator=',' close=')'>" +
            "#{username}" +
            "</foreach>" +
            " AND deleted = 0</script>")
    List<UserMembershipEntity> selectByUsernames(@Param("usernames") List<String> usernames);
}