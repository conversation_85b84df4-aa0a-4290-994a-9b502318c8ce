package com.gw.membership.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gw.membership.dto.MemberShipOrderQueryDTO;
import com.gw.membership.entity.MembershipOrderEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

/**
 * 会员订单Mapper接口
 */
@Mapper
public interface MembershipOrderMapper extends BaseMapper<MembershipOrderEntity> {
    @Select("SELECT * FROM t_membership_order WHERE id = #{id} AND deleted = 0 LIMIT 1")
    Optional<MembershipOrderEntity> findById(Long id);

    /**
     * 根据订单号查询订单
     *
     * @param orderNo 订单号
     * @return 订单实体
     */
    @Select("SELECT * FROM t_membership_order WHERE order_no = #{orderNo} AND deleted = 0 LIMIT 1")
    MembershipOrderEntity selectByOrderNo(@Param("orderNo") String orderNo);

    /**
     * 更新订单状态
     *
     * @param orderNo 订单号
     * @param status  订单状态
     * @param tradeNo 支付流水号
     * @return 影响行数
     */
    @Update("UPDATE t_membership_order SET status = #{status}, trade_no = #{tradeNo}, pay_time = NOW() " +
            "WHERE order_no = #{orderNo} AND deleted = 0")
    int updateOrderStatus(@Param("orderNo") String orderNo, @Param("status") Integer status,
                          @Param("tradeNo") String tradeNo);

    /**
     * 获取用户的所有订单
     *
     * @param username 用户ID
     * @return 订单列表
     */
    @Select("SELECT * FROM t_membership_order WHERE username = #{username} AND deleted = 0 ORDER BY create_time DESC")
    List<MembershipOrderEntity> selectByUsername(@Param("username") String username);

    List<MembershipOrderEntity> page(MemberShipOrderQueryDTO query);

    /**
     * 统计用户的消费总额
     *
     * @return 消费总额
     */
    @Select("SELECT COALESCE(SUM(pay_amount), 0) FROM t_membership_order WHERE username = #{username} AND status = 1 AND deleted = 0")
    BigDecimal sumUserPayAmount(@Param("username") Long username);
}