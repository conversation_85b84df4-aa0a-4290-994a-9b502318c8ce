package com.gw.membership.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class InvitationHistoryVO {

    @Schema(description = "邀请人用户名")
    private String inviter;
    
    @Schema(description = "被邀请人用户名")
    private String invitee;
    @Schema(description = "邀请人身份ID")
    private String inviterIdentify;

    @Schema(description = "被邀请人身份ID")
    private String inviteeIdentify;
    @Schema(description = "邀请码")
    private String invitationCode;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime = LocalDateTime.now();
}
