package com.gw.membership.vo;

import com.gw.common.membership.vo.MembershipBenefitVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class BenefitCategoryDetailVO extends BenefitCategoryVO {
    private List<MembershipBenefitVO> benefits;

    public BenefitCategoryDetailVO(BenefitCategoryVO vo) {
        this.setId(vo.getId());
        this.setCode(vo.getCode());
        this.setName(vo.getName());
        this.setVipLevel(vo.getVipLevel());
        this.setIsShow(vo.getIsShow());
    }
}
