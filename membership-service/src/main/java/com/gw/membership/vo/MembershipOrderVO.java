package com.gw.membership.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 会员订单DTO
 */
@Data
@Schema(description = "会员订单DTO")
public class MembershipOrderVO {

    /**
     * 订单ID，创建时不需要传
     */
    @Schema(description = "订单ID")
    private Long id;
    @Schema(description = "订单号")
    private String orderNo;
    /**
     * 套餐ID
     */
    @NotNull(message = "套餐ID不能为空")
    @Schema(description = "套餐ID")
    private Long packageId;

    /**
     * 支付方式: 2-支付宝, 1-微信, 3-Apple支付, 4-银行卡
     */
    @NotNull(message = "支付方式不能为空")
    @Schema(description = "支付方式: 1-微信, 2-支付宝,  3-Apple支付, 4-银行卡")
    private Integer payMethod;

    /**
     * 优惠码
     */
    @Schema(description = "优惠码")
    private String couponCode;

    /**
     * 订单类型: 1-购买, 2-续费, 3-赠送, 4-后台补录, 5-邀请赠送
     */
    @Schema(description = "订单类型: 1-购买, 2-续费, 3-赠送, 4-后台补录, 5-邀请赠送")
    private Integer orderType;

//    /**
//     * 客户端类型: 1-Web, 2-iOS, 3-Android
//     */
//    @Schema(description = "客户端类型: 1-Web, 2-iOS, 3-Android")
//    private Integer clientType;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;

    /**
     * 订单金额(元)，后端计算
     */
    @Schema(description = "订单金额(元)，后端计算")
    private BigDecimal amount;

    /**
     * 支付金额(元)，后端计算
     */
    @Schema(description = "支付金额(元)，后端计算")
    private BigDecimal payAmount;

    /**
     * 优惠金额(元)，后端计算
     */
    @Schema(description = "优惠金额(元)，后端计算")
    private BigDecimal discountAmount;

    /**
     * 邀请码
     */
    @Schema(description = "邀请码")
    private String invitationCode;

    /**
     * 操作人ID（后台赠送/补录时使用）
     */
    @Schema(description = "操作人ID（后台赠送/补录时使用）")
    private Long operatorId;

    /**
     * 订单状态: 1-未支付, 2-已支付, 3-已取消, 4-已退款
     */
    @Schema(description = "订单状态: 1-未支付, 2-已支付, 3-已取消, 4-已退款 5-退款申请中，6-已过期")
    private Integer status;

    /**
     * 用户名（后台操作时需要指定）
     */
    private String username;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
    @Schema(description = "交易单号")
    private String tradeNo;
}