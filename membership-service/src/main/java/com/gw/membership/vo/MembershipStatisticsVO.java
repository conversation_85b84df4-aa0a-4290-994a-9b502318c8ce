package com.gw.membership.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 会员统计数据VO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "会员统计数据")
public class MembershipStatisticsVO {

    @Schema(description = "今日收款金额")
    private Double todayIncome;

    @Schema(description = "VIP用户总数")
    private Integer totalVipUsers;

    @Schema(description = "历史收款总金额")
    private Double totalIncome;
} 