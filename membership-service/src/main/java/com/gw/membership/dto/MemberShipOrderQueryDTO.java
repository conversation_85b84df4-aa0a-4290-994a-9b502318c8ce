package com.gw.membership.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Schema(description = "会员订单查询DTO")
public class MemberShipOrderQueryDTO {
    @Schema(description = "用户名")
    @JsonIgnore
    private String username;

    private List<String> usernames;
    @Schema(description = "通用查询值 模糊匹配 用户名，昵称 id 电话")
    private String queryValue;

    @Schema(description = "订单号")
    private String orderNo;

    @Schema(description = "套餐ID")
    private Long packageId;

    @Schema(description = "套餐名称")
    @JsonIgnore
    private String packageName;

    @Schema(description = "支付时间查询的支付开始时间")
    private LocalDateTime startPayTime;

    @Schema(description = "支付时间查询的结束时间")
    private LocalDateTime endPayTime;

    @Schema(description = "订单创建时间查询的开始时间")

    private LocalDateTime startCreateTime;

    @Schema(description = "订单创建时间查询的结束时间")

    private LocalDateTime endCreateTime;

    @Schema(description = "最小金额")
    private BigDecimal minAmount;

    @Schema(description = "最大金额")
    private BigDecimal maxAmount;

    @Schema(description = "最小支付金额")
    private BigDecimal minPayAmount;

    @Schema(description = "最大支付金额")
    private BigDecimal maxPayAmount;

    /**
     * 订单状态: 1-未支付, 2-已支付, 3-已取消, 4-已退款
     */
    @Schema(description = "订单状态: 0-全部 1-未支付, 2-已支付, 3-已取消, 4-已退款 5-退款申请中，6-已过期")
    private Integer status;

    @Schema(description = "订单类型: 1-购买, 2-续费, 3-赠送, 4-后台补录, 5-邀请赠送 小于0表示全部")
    private Integer orderType;

    @Schema(description = "支付方式: 1-微信, 2-支付宝, 3-Apple支付, 4-银行卡 小于0表示全部")
    private Integer payMethod;

    @Schema(description = "邀请人用户名")
    private String sourceUsername;

    @Schema(description = "邀请码")
    private String invitationCode;

    @Schema(description = "操作人")
    private String operator;

    @Schema(description = "交易流水号")
    private String tradeNo;
}
