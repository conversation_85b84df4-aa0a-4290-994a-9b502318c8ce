package com.gw.membership.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "奖励配置")
public class RewardConfigDTO {
    @Schema(description = "奖励编码")
    private String code;
    @Schema(description = "奖励名称")
    private String name;
    /**
     * 奖励类型: 1-天数, 2-月, 3-季度, 4-年
     */
    @Schema(description = "奖励类型: 1-天数, 2-月, 3-季度, 4-年")
    private Integer rewardType;

    /**
     * 邀请奖励值（对应reward_type的数量）
     */
    @Schema(description = "邀请奖励值（对应reward_type的数量）")
    private Integer rewardValue;


    @Schema(description = "状态: 0-禁用，1-启用")
    private Integer status = 1;
}
