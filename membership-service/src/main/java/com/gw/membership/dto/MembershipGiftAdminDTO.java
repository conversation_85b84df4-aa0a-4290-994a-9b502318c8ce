package com.gw.membership.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.Data;


@Data
@Schema(description = "会员赠送请求参数")
public class MembershipGiftAdminDTO {

    /**
     * 用户名（后台操作时需要指定）
     */
    @Schema(description = "赠送人的用户名")
    @NotNull(message = "赠送人不能为空")
    private String username;


    /**
     * 天数
     */
    @Schema(description = "赠送天数")
    @Positive(message = "赠送天数不能小于0")
    private Integer days;

    /**
     * 操作原因/备注
     */
    @Schema(description = "操作原因/备注")
    private String reason;
}