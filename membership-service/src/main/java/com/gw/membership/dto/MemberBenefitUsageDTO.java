package com.gw.membership.dto;

import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 用户会员权益使用记录DTO
 */
@Data
public class MemberBenefitUsageDTO {

    private Long id;

    /**
     * 用户ID
     */
    private String username;

    /**
     * 权益ID
     */
    private Long benefitId;

    /**
     * 权益名称
     */
    private String benefitName;

    /**
     * 使用次数
     */
    private Integer useCount;

    /**
     * 每日限制次数
     */
    private Integer dailyLimit;

    /**
     * 每月限制次数
     */
    private Integer monthlyLimit;

    /**
     * 使用日期
     */
    private LocalDate useTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
} 