package com.gw.membership.dto;

import com.gw.membership.vo.MembershipPackageVO;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户会员DTO
 */
@Data
public class UserMembershipDTO {

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 当前套餐ID
     */
    private Long packageId;

    /**
     * 当前套餐名称
     */
    private String packageName;

    /**
     * 会员状态: 0-非会员, 1-会员
     */
    private Integer status;

    /**
     * 会员开始时间
     */
    private LocalDateTime startTime;

    /**
     * 会员到期时间
     */
    private LocalDateTime expireTime;

    /**
     * 是否过期
     */
    private Boolean isExpired;

    /**
     * 剩余天数
     */
    private Integer remainingDays;

    /**
     * 是否自动续费: 0-否, 1-是
     */
    private Integer autoRenew;

    /**
     * 会员等级
     */
    private Integer memberLevel;

    /**
     * 会员积分
     */
    private Integer memberPoints;


    private MembershipPackageVO packageDetail;
}