package com.gw.membership.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 会员权益DTO
 */
@Data
@Schema(description = "会员权益DTO")
public class MembershipBenefitDTO {
    /**
     * 权益名称
     */
    @Schema(description = "权益名称")
    @NotBlank(message = "权益名称不能为空")
    @Size(max = 50, message = "权益名称长度不能超过50个字符")
    private String name;

    /**
     * 权益代码
     */
    @Schema(description = "权益代码")
    @NotBlank(message = "权益代码不能为空")
    @Size(max = 50, message = "权益代码长度不能超过50个字符")
    private String code;
    /**
     * 重置周期: 1-日, 2-月, 3-周, 4-年 默认是1
     */
    @Schema(description = "重置周期: 1-日, 2-月, 3-周, 4-年 默认是1")
    private Integer resetCycle;

    /**
     * 配额值，默认 -1 表示无限配额
     */
    @Schema(description = "配额值，默认 -1 表示无限配额")
    private Integer quotaValue = 0;
    /**
     * 权益类型: 1-功能权益, 2-资源权益, 3-服务权益
     */
    @Schema(description = "权益类型: 1-功能权益, 2-资源权益, 3-服务权益")
    @NotNull(message = "权益类型不能为空")
    private Integer type;

    /**
     * 权益图标URL
     */
    @Schema(description = "权益图标URL")
    private String iconUrl;
    /**
     * 排序优先级
     */
    @Schema(description = "排序优先级")
    private Integer sortOrder = 0;
    /**
     * 权益状态: 0-禁用, 1-启用
     */
    @Schema(description = "权益状态: 0-禁用, 1-启用")
    private Integer status = 1;


    /**
     * 是否仅限VIP使用: 0-普通会员, 1-黄金会员
     */
    @Schema(description = "是否仅限VIP使用: 0-普通会员, 1-黄金会员")
    private Integer vipLevel;
}