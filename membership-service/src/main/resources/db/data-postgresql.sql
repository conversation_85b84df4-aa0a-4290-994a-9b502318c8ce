-- 初始化会员套餐数据
INSERT INTO t_membership_package
(name, description, price, discount_price, valid_days, type, status, sort_order, is_recommended, icon_url)
VALUES ('月度会员', '每月为您提供基础会员服务', 29.90, 29.90, 30, 1, 1, 1, 0, 'https://example.com/icons/monthly.png'),
       ('季度会员', '为您提供3个月的会员服务，比月度更优惠', 79.90, 79.90, 90, 2, 1, 2, 1,
        'https://example.com/icons/quarterly.png'),
       ('年度会员', '为您提供12个月的会员服务，超值优惠', 299.90, 259.90, 365, 3, 1, 3, 1,
        'https://example.com/icons/yearly.png'),
       ('终身会员', '一次付费，终身享受会员服务', 999.90, 799.90, 0, 4, 1, 4, 0,
        'https://example.com/icons/lifetime.png');

-- 初始化会员权益数据
INSERT INTO t_membership_benefit
    (name, code, description, type, icon_url, sort_order, status)
VALUES ('无限对话', 'UNLIMITED_CHAT', '与AI智能体无限次数对话', 1, 'https://example.com/icons/chat.png', 1, 1),
       ('高级模型', 'ADVANCED_MODEL', '使用最新的高级AI模型', 1, 'https://example.com/icons/model.png', 2, 1),
       ('优先响应', 'PRIORITY_RESPONSE', '享受优先响应，减少等待时间', 1, 'https://example.com/icons/priority.png', 3,
        1),
       ('多端同步', 'MULTI_DEVICE_SYNC', '多设备数据同步功能', 1, 'https://example.com/icons/sync.png', 4, 1),
       ('AI创作', 'AI_CREATION', 'AI辅助创作功能', 2, 'https://example.com/icons/creation.png', 5, 1),
       ('数据导出', 'DATA_EXPORT', '对话历史导出功能', 2, 'https://example.com/icons/export.png', 6, 1),
       ('定制智能体', 'CUSTOM_AGENT', '创建和定制自己的智能体', 2, 'https://example.com/icons/custom.png', 7, 1),
       ('客服支持', 'CUSTOMER_SUPPORT', '专属客服支持', 3, 'https://example.com/icons/support.png', 8, 1),
       ('每日积分', 'DAILY_POINTS', '每日额外积分奖励', 2, 'https://example.com/icons/points.png', 9, 1),
       ('会员徽章', 'MEMBER_BADGE', '专属会员徽章', 3, 'https://example.com/icons/badge.png', 10, 1);

-- 初始化套餐权益关联
-- 月度会员权益
INSERT INTO t_package_benefit_relation (package_id, benefit_id, status)
VALUES (1, 1, 1); -- 无限对话
INSERT INTO t_package_benefit_relation (package_id, benefit_id, status)
VALUES (1, 4, 1); -- 多端同步
INSERT INTO t_package_benefit_relation (package_id, benefit_id, status)
VALUES (1, 9, 1); -- 每日积分
INSERT INTO t_package_benefit_relation (package_id, benefit_id, status)
VALUES (1, 10, 1);
-- 会员徽章

-- 季度会员权益
INSERT INTO t_package_benefit_relation (package_id, benefit_id, status)
VALUES (2, 1, 1); -- 无限对话
INSERT INTO t_package_benefit_relation (package_id, benefit_id, status)
VALUES (2, 3, 1); -- 优先响应
INSERT INTO t_package_benefit_relation (package_id, benefit_id, status)
VALUES (2, 4, 1); -- 多端同步
INSERT INTO t_package_benefit_relation (package_id, benefit_id, status)
VALUES (2, 6, 1); -- 数据导出
INSERT INTO t_package_benefit_relation (package_id, benefit_id, status)
VALUES (2, 9, 1); -- 每日积分
INSERT INTO t_package_benefit_relation (package_id, benefit_id, status)
VALUES (2, 10, 1);
-- 会员徽章

-- 年度会员权益
INSERT INTO t_package_benefit_relation (package_id, benefit_id, status)
VALUES (3, 1, 1); -- 无限对话
INSERT INTO t_package_benefit_relation (package_id, benefit_id, status)
VALUES (3, 2, 1); -- 高级模型
INSERT INTO t_package_benefit_relation (package_id, benefit_id, status)
VALUES (3, 3, 1); -- 优先响应
INSERT INTO t_package_benefit_relation (package_id, benefit_id, status)
VALUES (3, 4, 1); -- 多端同步
INSERT INTO t_package_benefit_relation (package_id, benefit_id, status)
VALUES (3, 5, 1); -- AI创作
INSERT INTO t_package_benefit_relation (package_id, benefit_id, status)
VALUES (3, 6, 1); -- 数据导出
INSERT INTO t_package_benefit_relation (package_id, benefit_id, status)
VALUES (3, 8, 1); -- 客服支持
INSERT INTO t_package_benefit_relation (package_id, benefit_id, status)
VALUES (3, 9, 1); -- 每日积分
INSERT INTO t_package_benefit_relation (package_id, benefit_id, status)
VALUES (3, 10, 1);
-- 会员徽章

-- 终身会员权益
INSERT INTO t_package_benefit_relation (package_id, benefit_id, status)
VALUES (4, 1, 1); -- 无限对话
INSERT INTO t_package_benefit_relation (package_id, benefit_id, status)
VALUES (4, 2, 1); -- 高级模型
INSERT INTO t_package_benefit_relation (package_id, benefit_id, status)
VALUES (4, 3, 1); -- 优先响应
INSERT INTO t_package_benefit_relation (package_id, benefit_id, status)
VALUES (4, 4, 1); -- 多端同步
INSERT INTO t_package_benefit_relation (package_id, benefit_id, status)
VALUES (4, 5, 1); -- AI创作
INSERT INTO t_package_benefit_relation (package_id, benefit_id, status)
VALUES (4, 6, 1); -- 数据导出
INSERT INTO t_package_benefit_relation (package_id, benefit_id, status)
VALUES (4, 7, 1); -- 定制智能体
INSERT INTO t_package_benefit_relation (package_id, benefit_id, status)
VALUES (4, 8, 1); -- 客服支持
INSERT INTO t_package_benefit_relation (package_id, benefit_id, status)
VALUES (4, 9, 1); -- 每日积分
INSERT INTO t_package_benefit_relation (package_id, benefit_id, status)
VALUES (4, 10, 1); -- 会员徽章