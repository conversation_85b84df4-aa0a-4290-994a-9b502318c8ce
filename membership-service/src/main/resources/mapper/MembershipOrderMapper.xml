<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gw.membership.mapper.MembershipOrderMapper">
    <!-- 基础映射结果集 -->
    <resultMap id="BaseResultMap" type="com.gw.membership.entity.MembershipOrderEntity">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="order_no" jdbcType="VARCHAR" property="orderNo"/>
        <result column="username" jdbcType="VARCHAR" property="username"/>
        <result column="package_id" jdbcType="BIGINT" property="packageId"/>
        <result column="package_name" jdbcType="VARCHAR" property="packageName"/>
        <result column="order_amount" jdbcType="DECIMAL" property="orderAmount"/>
        <result column="pay_amount" jdbcType="DECIMAL" property="payAmount"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="pay_method" jdbcType="INTEGER" property="payMethod"/>
        <result column="trade_no" jdbcType="VARCHAR" property="tradeNo"/>
        <result column="pay_time" jdbcType="TIMESTAMP" property="payTime"/>
        <result column="deleted" jdbcType="INTEGER" property="deleted"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <!-- 分页查询订单列表 -->
    <select id="page" resultMap="BaseResultMap">
        SELECT * FROM t_membership_order
        <where>
            deleted = 0
            <choose>
                <when test="username != null and username != ''">
                    AND username = #{username}
                </when>
                <when test="usernames != null and usernames.size() > 0">
                    AND username IN
                    <foreach collection="usernames" item="name" open="(" separator="," close=")">
                        #{name}
                    </foreach>
                </when>
            </choose>
            <if test="orderNo != null and orderNo != ''">
                AND order_no = #{orderNo}
            </if>
            <if test="packageId != null and packageId > 0">
                AND package_id = #{packageId}
            </if>
            <if test="packageName != null and packageName != ''">
                AND package_name ILIKE CONCAT('%', #{packageName}, '%')
            </if>
            <if test="status != null and status >0">
                AND status = #{status}
            </if>
            <if test="orderType != null and orderType > 0">
                AND order_type = #{orderType}
            </if>
            <if test="payMethod != null and payMethod > 0">
                AND pay_method = #{payMethod}
            </if>
            <if test="sourceUsername != null and sourceUsername != ''">
                AND source_username = #{sourceUsername}
            </if>
            <if test="invitationCode != null and invitationCode != ''">
                AND invitation_code = #{invitationCode}
            </if>
            <if test="operator != null and operator != ''">
                AND operator = #{operator}
            </if>
            <if test="minAmount != null ">
                AND amount >= #{minAmount}
            </if>
            <if test="maxAmount != null">
                AND amount &lt; #{maxAmount}
            </if>
            <if test="minPayAmount != null">
                AND pay_amount >= #{minPayAmount}
            </if>
            <if test="maxPayAmount != null">
                AND pay_amount &lt; #{maxPayAmount}
            </if>
            <if test="startCreateTime != null">
                AND create_time >= #{startCreateTime}
            </if>
            <if test="endCreateTime != null">
                AND create_time &lt;= #{endCreateTime}
            </if>
            <if test="startPayTime != null">
                AND pay_time >= #{startPayTime}
            </if>
            <if test="endPayTime != null">
                AND pay_time &lt; #{endPayTime}
            </if>
            <if test="tradeNo != null and tradeNo != ''">
                AND trade_no = #{tradeNo}
            </if>
        </where>
        ORDER BY create_time DESC
    </select>
</mapper>