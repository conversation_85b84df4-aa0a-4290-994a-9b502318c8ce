# WebSocket连接健壮性修复

## 问题描述

在高并发或网络波动的情况下，WebSocket连接经常被误判为无效而断开，主要表现为：

1. **错误日志**: `send() has already been called` - 表明响应式WebSocket会话的send()方法被多次调用
2. **会话元数据不存在或无效**: 会话验证过于严格，导致正常连接被误判为无效
3. **连接频繁断开**: 实际连接存在但被系统清理

## 根本原因分析

1. **重复调用send()方法**: 在`registerNewReactiveSession`和`tryRecreateSessionMetadata`中都创建了消息流，导致多次调用`session.send()`
2. **会话验证过于严格**: `isValid()`方法在网络波动时容易误判会话为无效
3. **缺少会话恢复机制**: 当会话被误判为无效时，没有尝试恢复或重新验证
4. **心跳机制不够健壮**: 心跳失败后直接清理会话，没有重试机制

## 修复方案

### 1. 参考RefactoredMultiChatWebSocketHandler的设计模式

借鉴`multi-chat-service`中`RefactoredMultiChatWebSocketHandler`的成功实现：
- 会话管理器负责创建和管理消息流
- 避免在多个地方重复调用`session.send()`
- 使用统一的消息流建立机制

### 2. 增强会话验证逻辑

#### 修改前（严格验证）:
```java
public boolean isValid() {
    return session != null && session.isOpen() &&
            active != null && active.get();
}
```

#### 修改后（宽松验证 + 容错机制）:
```java
public boolean isValid() {
    // 基本检查
    if (session == null || active == null) {
        return false;
    }
    
    // 检查活跃状态
    boolean isActive = active.get();
    
    // 检查会话状态，增加异常处理
    boolean sessionOpen = false;
    try {
        sessionOpen = session.isOpen();
    } catch (Exception e) {
        // 网络问题时给予容错时间
        if (isActive && System.currentTimeMillis() - lastActiveTime < 30000) {
            return true; // 30秒容错
        }
        return false;
    }
    
    // 会话关闭但最近有活动，可能是临时网络问题
    if (!sessionOpen && isActive) {
        long timeSinceLastActivity = System.currentTimeMillis() - lastActiveTime;
        if (timeSinceLastActivity < 10000) { // 10秒容错
            return true;
        }
    }
    
    return sessionOpen && isActive;
}
```

### 3. 会话恢复和修复机制

新增`getOrRecoverSessionMetadata`方法：
1. **首次验证**: 检查会话是否真的有效
2. **尝试修复**: 如果会话状态不一致，尝试修复
3. **重新创建**: 如果修复失败，重新创建会话元数据

### 4. 避免重复调用send()

#### 修改前的问题:
```java
// 在registerNewReactiveSession中
Disposable subscription = session.send(messageFlux.map(session::textMessage))

// 在tryRecreateSessionMetadata中又调用了registerNewReactiveSession
// 导致重复调用send()
```

#### 修改后的解决方案:
```java
// 1. 优先使用会话管理器
if (chatSessionManager != null) {
    chatSessionManager.addSession(username, session);
    // 使用会话管理器创建的元数据，避免重复创建消息流
}

// 2. 分离元数据创建和消息流建立
private void createSessionMetadataOnly(String username, WebSocketSession session, long currentTime) {
    // 仅创建元数据，不创建消息流
}

private void establishMessageFlow(String username, WebSocketSession session, SessionMetadata metadata) {
    // 专门负责建立消息流，检查是否已存在
    if (metadata.getSubscription() != null && !metadata.getSubscription().isDisposed()) {
        return; // 已存在，跳过
    }
    // 建立新的消息流
}
```

### 5. 增强心跳机制

#### 渐进式超时策略:
```java
// 根据心跳失败次数调整超时时间
if (heartbeatFailures == 0) {
    timeoutMs = heartbeatTimeoutSeconds * 1000L; // 正常超时
} else if (heartbeatFailures <= 2) {
    timeoutMs = (heartbeatTimeoutSeconds / 2) * 1000L; // 减半
} else {
    timeoutMs = (heartbeatTimeoutSeconds / 4) * 1000L; // 四分之一
}
```

#### 失败重试机制:
```java
// 心跳失败时不立即清理，而是增加失败计数
metadata.incrementHeartbeatFailure();

// 只有失败次数超过阈值才清理会话
if (metadata.getHeartbeatFailureCount() >= 3) {
    sessionManager.removeSession(username);
}
```

### 6. 会话健康检查

新增`SessionHealthChecker`组件：
- 定期检查会话一致性
- 自动修复不一致的会话状态
- 清理孤立的缓存记录

## 配置参数

新增健壮性配置 (`websocket.robustness.*`):

```properties
# 会话验证容错时间（毫秒）
websocket.robustness.session-validation-tolerance-ms=30000
# 会话关闭容错时间（毫秒）  
websocket.robustness.session-close-tolerance-ms=10000
# 心跳失败重试次数
websocket.robustness.heartbeat-failure-retry-count=3
# 启用会话自动恢复
websocket.robustness.enable-session-auto-recovery=true
# 启用渐进式心跳超时
websocket.robustness.enable-progressive-heartbeat-timeout=true
# 启用会话状态修复
websocket.robustness.enable-session-state-repair=true
```

## 测试验证

创建了`WebSocketSendFixTest`测试类，验证：
1. 不会重复调用`send()`方法
2. 会话恢复机制正常工作
3. 容错验证逻辑正确
4. 旧会话清理机制有效

## 部署建议

1. **逐步启用**: 先在测试环境验证，再逐步推广到生产环境
2. **监控指标**: 关注WebSocket连接数、断开率、重连率等指标
3. **日志级别**: 生产环境建议关闭详细的健壮性日志 (`enable-verbose-robustness-logging=false`)
4. **参数调优**: 根据实际网络环境调整容错时间和重试次数

## 预期效果

1. **减少误断**: 通过容错机制减少因网络波动导致的连接误断
2. **提高稳定性**: 会话恢复机制提高连接的稳定性
3. **降低错误率**: 避免`send() has already been called`等错误
4. **改善用户体验**: 减少用户需要手动重连的情况

## 兼容性说明

- 向后兼容现有配置
- 默认启用所有健壮性功能
- 可通过配置选择性禁用某些功能
- 不影响现有的WebSocket消息处理逻辑
