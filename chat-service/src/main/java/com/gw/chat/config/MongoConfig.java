package com.gw.chat.config;

import com.mongodb.ConnectionString;
import com.mongodb.MongoClientSettings;
import com.mongodb.WriteConcern;
import com.mongodb.client.MongoClient;
import com.mongodb.client.MongoClients;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.convert.converter.Converter;
import org.springframework.data.mongodb.config.AbstractMongoClientConfiguration;
import org.springframework.data.mongodb.config.EnableMongoAuditing;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.convert.MongoCustomConversions;
import org.springframework.data.mongodb.repository.config.EnableMongoRepositories;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.TimeZone;

/**
 * MongoDB配置类
 */
@Configuration
@EnableMongoRepositories(basePackages = "com.gw.chat.repository")
@EnableMongoAuditing
public class MongoConfig extends AbstractMongoClientConfiguration {

    @Value("${spring.data.mongodb.uri}")
    private String mongoUri;

    @Value("${spring.data.mongodb.timezone:Asia/Shanghai}")
    private String timezone;

    @NotNull
    @Override
    protected String getDatabaseName() {
        return "chat_service";
    }

    @NotNull
    @Override
    public MongoClient mongoClient() {
        ConnectionString connectionString = new ConnectionString(mongoUri);
        MongoClientSettings mongoClientSettings = MongoClientSettings.builder()
                .applyConnectionString(connectionString)
                .retryWrites(false)
                .writeConcern(WriteConcern.ACKNOWLEDGED)
                .build();
        return MongoClients.create(mongoClientSettings);
    }

    @Bean
    public MongoTemplate mongoTemplate() {
        MongoTemplate mongoTemplate = new MongoTemplate(mongoClient(), getDatabaseName());
        // 禁用事务会话支持
        mongoTemplate.setSessionSynchronization(null);
        return mongoTemplate;
    }

    @Override
    protected void configureClientSettings(MongoClientSettings.Builder builder) {
        super.configureClientSettings(builder);
        // 设置时区为配置的时区（默认为东八区/中国标准时间）
        TimeZone.setDefault(TimeZone.getTimeZone(timezone));
    }

    @NotNull
    @Bean
    @Override
    public MongoCustomConversions customConversions() {
        List<Converter<?, ?>> converters = new ArrayList<>();

        // 添加LocalDateTime到Date的转换器，使用配置的时区
        converters.add(new LocalDateTimeToDateConverter());
        // 添加Date到LocalDateTime的转换器，使用配置的时区
        converters.add(new DateToLocalDateTimeConverter());

        return new MongoCustomConversions(converters);
    }

    /**
     * 将LocalDateTime转换为Date，使用配置的时区
     */
    class LocalDateTimeToDateConverter implements Converter<LocalDateTime, Date> {
        @Override
        public Date convert(LocalDateTime source) {
            return source == null ? null : Date.from(source.atZone(ZoneId.of(timezone)).toInstant());
        }
    }

    /**
     * 将Date转换为LocalDateTime，使用配置的时区
     */
    class DateToLocalDateTimeConverter implements Converter<Date, LocalDateTime> {
        @Override
        public LocalDateTime convert(Date source) {
            return source == null ? null
                    : ZonedDateTime.ofInstant(source.toInstant(), ZoneId.of(timezone)).toLocalDateTime();
        }
    }
}