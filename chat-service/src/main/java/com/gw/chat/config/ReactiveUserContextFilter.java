package com.gw.chat.config;

import com.gw.common.user.context.UserContext;
import lombok.extern.log4j.Log4j2;
import org.springframework.core.Ordered;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.server.ServerWebExchange;
import org.springframework.web.server.WebFilter;
import org.springframework.web.server.WebFilterChain;
import reactor.core.publisher.Mono;

import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Base64;

/**
 * 响应式用户上下文过滤器
 * 处理WebFlux环境中的用户上下文，从请求头中提取用户信息
 */
@Component
@Log4j2
public class ReactiveUserContextFilter implements WebFilter, Ordered {

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, WebFilterChain chain) {
        String userId = exchange.getRequest().getHeaders().getFirst("X-User-Id");
        String username = exchange.getRequest().getHeaders().getFirst("X-User-Name");
        String encodedRealName = exchange.getRequest().getHeaders().getFirst("X-User-RealName");
        String roles = exchange.getRequest().getHeaders().getFirst("X-User-Roles");

        log.debug("从请求头中获取用户信息: userId={}, username={}, roles={}", userId, username, roles);

        UserContext userContext = new UserContext();
        userContext.setUserId(userId);
        userContext.setUsername(username);

        if (encodedRealName != null) {
            try {
                userContext
                        .setRealName(new String(Base64.getDecoder().decode(encodedRealName), StandardCharsets.UTF_8));
                log.debug("解码用户真实姓名: {}", userContext.getRealName());
            } catch (IllegalArgumentException e) {
                userContext.setRealName(encodedRealName);
                log.warn("无法将真实姓名解码为Base64，使用原始值: {}", encodedRealName);
            }
        }

        if (StringUtils.hasText(roles)) {
            userContext.setRoles(Arrays.asList(roles.split(",")));
        }

        // 将用户上下文存储到ReactiveContext中
        return chain.filter(exchange)
                .contextWrite(ctx -> ctx.put(UserContext.class, userContext));
    }

    @Override
    public int getOrder() {
        return Ordered.HIGHEST_PRECEDENCE + 10;
    }
}