package com.gw.chat.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 聊天历史配置属性
 * 用于配置不同用户类型的历史消息限制
 */
@Configuration
@ConfigurationProperties(prefix = "chat.history")
@Data
public class ChatHistoryConfig {

    /**
     * VIP用户历史消息限制
     * 默认值：100
     */
    private Integer vipLimit = 100;

    /**
     * 普通用户历史消息限制
     * 默认值：50
     */
    private Integer normalLimit = 50;

    /**
     * VIP用户TTL（生存时间）
     * 默认值：604800秒（7天）
     */
    private Integer vipTTL = 604800;

    /**
     * 普通用户TTL（生存时间）
     * 默认值：7200（2小时）
     */
    private Integer normalTTL = 7200;

    /**
     * 根据VIP等级获取历史消息限制
     *
     * @param vipLevel VIP等级，大于0表示VIP用户
     * @return 历史消息限制数量
     */
    public Integer getLimitByVipLevel(Integer vipLevel) {
        if (vipLevel != null && vipLevel > 0) {
            return vipLimit;
        }
        return normalLimit;
    }

    /**
     * 根据VIP等级获取TTL
     *
     * @param vipLevel VIP等级，大于0表示VIP用户
     * @return TTL值（秒）
     */
    public Integer getTTLByVipLevel(Integer vipLevel) {
        if (vipLevel != null && vipLevel > 0) {
            return vipTTL;
        }
        return normalTTL;
    }
}
