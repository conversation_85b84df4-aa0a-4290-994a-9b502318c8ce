package com.gw.chat.config;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.stats.CacheStats;
import com.gw.chat.websocket.SessionMetadata;
import lombok.Data;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.time.Duration;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * WebSocket缓存配置
 * 使用Caffeine框架提供高性能的本地缓存
 */
@Configuration
@Log4j2
public class WebSocketCacheConfig {

    @Value("${websocket.cache.session-max-size:2000}")
    private int sessionCacheMaxSize;

    @Value("${websocket.cache.session-expire-minutes:60}")
    private int sessionCacheExpireMinutes;

    @Value("${websocket.cache.lock-max-size:2000}")
    private int lockCacheMaxSize;

    @Value("${websocket.cache.lock-expire-minutes:60}")
    private int lockCacheExpireMinutes;

    @Value("${websocket.cache.active-time-max-size:2000}")
    private int activeTimeCacheMaxSize;

    @Value("${websocket.cache.active-time-expire-minutes:60}")
    private int activeTimeCacheExpireMinutes;

    @Value("${websocket.cache.stats-max-size:2000}")
    private int statsCacheMaxSize;

    @Value("${websocket.cache.stats-expire-minutes:60}")
    private int statsCacheExpireMinutes;

    /**
     * 会话缓存
     * 存储用户名到会话元数据的映射
     */
    @Bean(name = "sessionCache")
    public Cache<String, SessionMetadata> sessionCache() {
        return Caffeine.newBuilder()
                .maximumSize(sessionCacheMaxSize)
                .expireAfterAccess(Duration.ofMinutes(sessionCacheExpireMinutes))
                .recordStats()
                .build();
    }

    /**
     * 会话锁缓存
     * 存储会话ID到StampedLock的映射
     */
    @Bean(name = "sessionLockCache")
    public Cache<String, Object> sessionLockCache() {
        return Caffeine.newBuilder()
                .maximumSize(lockCacheMaxSize)
                .expireAfterAccess(Duration.ofMinutes(lockCacheExpireMinutes))
                .recordStats()
                .build();
    }

    /**
     * 用户活跃时间缓存
     * 存储用户名到最后活跃时间的映射
     */
    @Bean(name = "userActiveTimeCache")
    public Cache<String, Long> userActiveTimeCache() {
        return Caffeine.newBuilder()
                .maximumSize(activeTimeCacheMaxSize)
                .expireAfterAccess(Duration.ofMinutes(activeTimeCacheExpireMinutes))
                .recordStats()
                .build();
    }

    /**
     * 会话统计信息缓存
     * 存储会话ID到会话统计信息的映射
     */
    @Bean(name = "sessionStatsCache")
    public Cache<String, SessionStats> sessionStatsCache() {
        return Caffeine.newBuilder()
                .maximumSize(statsCacheMaxSize)
                .expireAfterAccess(Duration.ofMinutes(statsCacheExpireMinutes))
                .recordStats()
                .build();
    }

    /**
     * 会话统计信息类
     */
    @Data
    public static class SessionStats {
        private final String username;
        private final long creationTime;
        private final AtomicInteger messageCount = new AtomicInteger(0);
        private long lastActiveTime;

        public SessionStats(String username) {
            this.username = username;
            this.creationTime = System.currentTimeMillis();
            this.lastActiveTime = this.creationTime;
        }

        public void incrementMessageCount() {
            messageCount.incrementAndGet();
            this.lastActiveTime = System.currentTimeMillis();
        }

        public int getMessageCount() {
            return messageCount.get();
        }

        public long getSessionAgeMs() {
            return System.currentTimeMillis() - creationTime;
        }

        public long getIdleTimeMs() {
            return System.currentTimeMillis() - lastActiveTime;
        }
    }

    /**
     * 缓存统计信息
     */
    public static class CacheMetrics {
        private final String cacheName;
        private final long size;
        private final long hitCount;
        private final long missCount;
        private final double hitRate;
        private final long evictionCount;

        public CacheMetrics(String cacheName, long size, CacheStats stats) {
            this.cacheName = cacheName;
            this.size = size;
            this.hitCount = stats.hitCount();
            this.missCount = stats.missCount();
            this.hitRate = stats.hitRate();
            this.evictionCount = stats.evictionCount();
        }

        @Override
        public String toString() {
            return String.format("%s: size=%d, hits=%d, misses=%d, hitRate=%.2f%%, evictions=%d",
                    cacheName, size, hitCount, missCount, hitRate * 100, evictionCount);
        }
    }
}