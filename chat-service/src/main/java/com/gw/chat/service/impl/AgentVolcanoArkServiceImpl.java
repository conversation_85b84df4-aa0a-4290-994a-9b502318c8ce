package com.gw.chat.service.impl;

import com.alibaba.fastjson2.JSONArray;
import com.gw.chat.config.SpeechConfig;
import com.gw.chat.config.VolcanoArkConfig;
import com.gw.chat.constant.ChatConstant;
import com.gw.chat.dto.CreateRemoteSessionParamsDTO;
import com.gw.chat.dto.MessageContentDTO;
import com.gw.chat.exception.VolcanoContentParsingException;
import com.gw.chat.service.AgentCozeRemoteService;
import com.gw.chat.service.AgentRemoteVolvanoService;
import com.gw.chat.utils.ChatUtils;
import com.gw.chat.vo.AIResponseVO;
import com.gw.chat.vo.ChatContextVO;
import com.gw.chat.vo.TextToSpeechVO;
import com.gw.common.agent.vo.VolcanoArkSettingVO;
import com.gw.common.util.StringUtils;
import com.gw.common.util.UploadFileUtil;
import com.volcengine.ark.runtime.Const;
import com.volcengine.ark.runtime.exception.ArkHttpException;
import com.volcengine.ark.runtime.model.Usage;
import com.volcengine.ark.runtime.model.completion.chat.ChatCompletionRequest;
import com.volcengine.ark.runtime.model.completion.chat.ChatCompletionResult;
import com.volcengine.ark.runtime.model.completion.chat.ChatMessage;
import com.volcengine.ark.runtime.model.completion.chat.ChatMessageRole;
import com.volcengine.ark.runtime.model.context.CreateContextRequest;
import com.volcengine.ark.runtime.model.context.CreateContextResult;
import com.volcengine.ark.runtime.model.context.TruncationStrategy;
import com.volcengine.ark.runtime.model.context.chat.ContextChatCompletionRequest;
import com.volcengine.ark.runtime.service.ArkService;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.attribute.FileTime;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Stream;

/**
 * 基于火山方舟大模型服务平台的AgentRemoteService实现
 * 支持上下文缓存功能，优化了性能和错误处理
 *
 * 优化特性：
 * - 单例ArkService实例管理
 * - 统一的回调处理机制
 * - 异步文件清理
 * - 统一的异常处理
 */
@Service("agentVolcanoArkService")
@Log4j2
@RequiredArgsConstructor
public class AgentVolcanoArkServiceImpl implements AgentRemoteVolvanoService {

    // 配置常量
    private static final int DEFAULT_COMPRESS_MAX_TOKENS = 16000;
    private static final int DEFAULT_MAX_TOKENS = 320;
    private static final double DEFAULT_TEMPERATURE = 0.7;
    private static final String DEFAULT_AUDIO_DURATION = "0:00";

    // 特殊响应标识
    private static final String RESOURCE_NOT_FOUND_RESPONSE = "ResourceNotFound";
    private static final String CONTENT_TOO_LONG_RESPONSE = "ContentTooLong";

    // 上下文缓存配置
    private static final int MAX_WINDOW_TOKENS = 32768;
    private static final int ROLLING_WINDOW_TOKENS = 4096;

    // 依赖注入
    private final SpeechConfig speechConfig;
    private final VolcanoArkConfig volcanoArkConfig;
    private final AgentCozeRemoteService agentCozeRemoteService;

    // ArkService 实例缓存，避免重复创建
    private final Object arkServiceLock = new Object();
    private volatile ArkService arkServiceInstance;

    /**
     * 从ChatCompletionResult中提取响应内容
     * 统一的响应内容解析逻辑，包含完整的错误检查
     */
    private static String getResponseContent(ChatCompletionResult response) throws VolcanoContentParsingException {
        if (response == null || response.getChoices() == null || response.getChoices().isEmpty()) {
            throw VolcanoContentParsingException.emptyResponse();
        }

        var choice = response.getChoices().get(0);
        if ("length".equals(choice.getFinishReason())) {
            log.warn("响应内容过长被截断: {}", response);
            throw VolcanoContentParsingException.contentTooLong();
        }

        var responseMessage = choice.getMessage();
        if (responseMessage == null || responseMessage.getContent() == null) {
            throw VolcanoContentParsingException.emptyContent();
        }

        return String.valueOf(responseMessage.getContent());
    }

    /**
     * 异步计算目录大小
     */
    @Async("taskExecutor")
    public CompletableFuture<Long> calculateDirectorySizeAsync(Path directory) {
        return CompletableFuture.supplyAsync(() -> calculateDirectorySize(directory));
    }

    /**
     * 计算目录大小
     */
    private long calculateDirectorySize(Path directory) {
        try {
            return Files.walk(directory)
                    .filter(Files::isRegularFile)
                    .mapToLong(path -> {
                        try {
                            return Files.size(path);
                        } catch (IOException e) {
                            log.error("获取文件大小时出错: {}", path, e);
                            return 0L;
                        }
                    })
                    .sum();
        } catch (IOException e) {
            log.error("计算目录大小时出错: {}", directory, e);
            return 0L;
        }
    }

    /**
     * 异步清理旧文件
     */
    @Async("taskExecutor")
    public CompletableFuture<Void> cleanupOldFilesAsync(Path directory) {
        return CompletableFuture.runAsync(() -> cleanupOldFiles(directory));
    }

    /**
     * 清理旧文件
     */
    private void cleanupOldFiles(Path directory) {
        try (Stream<Path> pathStream = Files.walk(directory)) {
            // 先收集文件信息，避免在排序时文件被删除
            List<FileInfo> fileInfos = pathStream
                    .filter(Files::isRegularFile)
                    .map(path -> {
                        try {
                            return new FileInfo(path, Files.getLastModifiedTime(path));
                        } catch (IOException e) {
                            // 文件可能在检查过程中被删除，跳过此文件
                            log.debug("文件在清理扫描过程中不再存在: {}", path);
                            return null;
                        }
                    })
                    .filter(Objects::nonNull)  // 过滤掉null值
                    .sorted(Comparator.comparing(FileInfo::lastModifiedTime))
                    .toList();

            // 删除最旧的文件直到目录大小低于限制
            long currentSize = calculateDirectorySize(directory);
            int deletedCount = 0;

            for (FileInfo fileInfo : fileInfos) {
                if (currentSize <= speechConfig.getMaxDirSize()) {
                    break;
                }

                Path file = fileInfo.path();
                try {
                    // 再次检查文件是否存在，因为可能在收集信息后被删除
                    if (!Files.exists(file)) {
                        log.debug("文件在清理过程中不再存在: {}", file);
                        continue;
                    }

                    long fileSize = Files.size(file);
                    Files.delete(file);
                    currentSize -= fileSize;
                    deletedCount++;
                    log.debug("删除旧文件: {}", file);
                } catch (IOException e) {
                    log.error("删除文件时出错: {}", file, e);
                }
            }

            if (deletedCount > 0) {
                log.info("清理完成，删除了 {} 个旧文件，当前目录大小: {} bytes", deletedCount, currentSize);
            }
        } catch (IOException e) {
            log.error("清理旧文件时出错: {}", directory, e);
        }
    }

    /**
     * 文件信息记录类，用于安全地处理文件排序
     */
    private record FileInfo(Path path, FileTime lastModifiedTime) {}

    /**
     * 获取火山方舟服务实例（使用单例模式避免重复创建）
     */
    private ArkService getArkService() {
        if (arkServiceInstance == null) {
            synchronized (arkServiceLock) {
                if (arkServiceInstance == null) {
                    VolcanoArkSettingVO setting = getVolcanoArkSetting();
                    arkServiceInstance = ArkService.builder()
                            .apiKey(setting.getApiKey())
                            .baseUrl(setting.getBaseUrl())
                            .timeout(Duration.ofMillis(setting.getReadTimeout()))
                            .connectTimeout(Duration.ofMillis(setting.getConnectTimeout()))
                            .retryTimes(setting.getRetryTimes())
                            .build();
                    log.info("火山方舟，配置: baseUrl={}, modelId={}, timeout={}ms",
                            setting.getBaseUrl(), setting.getModelId(), setting.getReadTimeout());
                }
            }
        }
        return arkServiceInstance;
    }

    /**
     * 获取火山方舟配置
     */
    private VolcanoArkSettingVO getVolcanoArkSetting() {
        VolcanoArkSettingVO setting = new VolcanoArkSettingVO();
        setting.setApiKey(volcanoArkConfig.getApiKey());
        setting.setBaseUrl(volcanoArkConfig.getBaseUrl());
        setting.setModelId(volcanoArkConfig.getDefaultModelId());
        setting.setConnectTimeout(volcanoArkConfig.getConnectTimeout());
        setting.setReadTimeout(volcanoArkConfig.getReadTimeout());
        setting.setRetryTimes(volcanoArkConfig.getRetryTimes());
        setting.setCompressModelId(volcanoArkConfig.getCompressModelId());
        return setting;
    }

    /**
     * 创建上下文缓存
     * 优化的上下文缓存创建逻辑，使用常量配置
     */
    private CreateContextResult createContextCache(String modelId, List<ChatMessage> systemMessages) throws Exception {
        ArkService arkService = getArkService();

        // 使用常量配置构建截断策略
        TruncationStrategy truncationStrategy = TruncationStrategy.builder()
                .type(Const.TRUNCATION_STRATEGY_TYPE_ROLLING_TOKENS)
                .maxWindowTokens(MAX_WINDOW_TOKENS)
                .rollingWindowTokens(ROLLING_WINDOW_TOKENS)
                .rollingTokens(true)
                .build();

        // 构建请求
        CreateContextRequest request = CreateContextRequest.builder()
                .model(modelId)
                .mode(Const.CONTEXT_MODE_SESSION)
                .messages(systemMessages)
                .truncationStrategy(truncationStrategy)
                .ttl((int) volcanoArkConfig.getContextCacheTtl())
                .build();

        log.debug("创建上下文缓存请求: modelId={}, systemMessagesCount={}, ttl={}",
                modelId, systemMessages.size(), volcanoArkConfig.getContextCacheTtl());

        try {
            CreateContextResult result = arkService.createContext(request);
            log.info("创建上下文缓存成功: contextId={}", result.getId());
            return result;
        } catch (ArkHttpException e) {
            log.error("创建上下文缓存失败: code={}, message={}", e.code, e.getMessage());
            if ("ResourceNotFound".equals(e.code) || e.getMessage().contains("404")) {
                log.warn("检测到上下文缓存功能不可用");
            }
            throw new Exception("创建上下文缓存失败: " + e.getMessage(), e);
        }
    }

    /**
     * 统一的回调处理方法
     * 减少重复的回调处理代码
     */
    private CreateRemoteSessionParamsDTO executeCallback(VolcanoChatCallback callback,
                                                        String conversationId,
                                                        SystemContext systemContext,
                                                        String errorDetails,
                                                        Function<VolcanoChatCallback, CreateRemoteSessionParamsDTO> callbackFunction) {
        if (callback == null) {
            return null;
        }

        try {
            CreateRemoteSessionParamsDTO sessionParams = callbackFunction.apply(callback);
            if (sessionParams != null && sessionParams.getContexts() != null) {
                log.info("回调成功返回系统消息，数量: {}", sessionParams.getContexts().size());
                sessionParams.getContexts().forEach(item ->
                    log.debug("系统消息内容: {}", item.getContent()));
            }
            return sessionParams;
        } catch (Exception callbackException) {
            log.error("回调执行异常: conversationId={}, error={}", conversationId, callbackException.getMessage());
            return null;
        }
    }

    /**
     * 创建SystemContext的统一方法
     */
    private SystemContext createSystemContext(String contextCacheId, List<ChatContextVO> message) {
        return new SystemContext(contextCacheId, message);
    }

    /**
     * 处理ResourceNotFound错误的统一方法
     */
    private String handleResourceNotFound(String conversationId, String contextCacheId,
                                        List<ChatContextVO> message, List<ChatMessage> messages,
                                        VolcanoChatCallback callback) throws Exception {
        log.info("上下文缓存资源未找到，触发回调: contextCacheId={}", contextCacheId);

        SystemContext systemContext = createSystemContext(contextCacheId, message);
        CreateRemoteSessionParamsDTO sessionParams = executeCallback(
            callback, conversationId, systemContext, "上下文缓存资源未找到",
            cb -> cb.onResourceNotFound(conversationId, systemContext, "上下文缓存资源未找到")
        );

        if (sessionParams == null || sessionParams.getContexts() == null) {
            throw new Exception("上下文缓存功能不可用");
        }

        // 重新创建上下文缓存
        String newContextCacheId = createConversation(sessionParams.getContexts());
        return chatWithContextCache(newContextCacheId, messages, callback);
    }

    /**
     * 处理ContentTooLong错误的统一方法
     */
    private String handleContentTooLong(String conversationId, String contextCacheId,
                                      List<ChatContextVO> message, List<ChatMessage> messages,
                                      VolcanoChatCallback callback) throws Exception {
        log.info("内容过长，触发回调重建上下文: contextCacheId={}", contextCacheId);

        SystemContext systemContext = createSystemContext(contextCacheId, message);
        CreateRemoteSessionParamsDTO sessionParams = executeCallback(
            callback, conversationId, systemContext, "内容过长，需要重建上下文",
            cb -> cb.onContentTooLong(conversationId, systemContext, "内容过长，需要重建上下文")
        );

        if (sessionParams == null || sessionParams.getContexts() == null) {
            log.error("内容过长回调未返回有效的系统消息，抛出异常");
            throw VolcanoContentParsingException.contentTooLong();
        }

        // 重新创建上下文缓存
        String newContextCacheId = createConversation(sessionParams.getContexts());
        String responseContent = chatWithContextCache(newContextCacheId, messages, callback);

        // 如果重试后仍然是内容过长，直接抛出异常（只重试一次）
        if (CONTENT_TOO_LONG_RESPONSE.equals(responseContent)) {
            log.error("重新创建上下文缓存后仍然内容过长，抛出异常");
            throw VolcanoContentParsingException.contentTooLong();
        }

        return responseContent;
    }

    @Override
    public String createConversation(List<MessageContentDTO> messages) {
        String conversationId = UUID.randomUUID().toString();
        log.info("创建火山方舟会话: conversationId={}, systemMessagesCount={}",
                conversationId, messages != null ? messages.size() : 0);

        try {
            VolcanoArkSettingVO setting = getVolcanoArkSetting();
            List<ChatMessage> systemMessages = buildSystemMessages(messages);

            // 尝试创建上下文缓存
            CreateContextResult contextResult = createContextCache(setting.getModelId(), systemMessages);

            log.info("创建火山方舟会话成功 - conversationId={}, contextCacheId={}",
                    conversationId, contextResult.getId());
            return contextResult.getId();

        } catch (Exception e) {
            log.warn("创建火山方舟上下文缓存失败，回退到传统模式: conversationId={}, error={}",
                    conversationId, e.getMessage());
            return conversationId;
        }
    }

    /**
     * 构建系统消息列表
     * 提取的通用方法，用于创建ChatMessage列表
     */
    private List<ChatMessage> buildSystemMessages(List<MessageContentDTO> messages) {
        List<ChatMessage> systemMessages = new ArrayList<>();
        if (messages != null) {
            messages.stream()
                    .filter(msg -> msg != null && StringUtils.hasText(msg.getContent()))
                    .forEach(msg -> systemMessages.add(ChatMessage.builder()
                            .role(ChatMessageRole.SYSTEM)
                            .content(msg.getContent())
                            .build()));
        }
        return systemMessages;
    }

    /**
     * 使用上下文缓存进行对话
     * 优化的上下文缓存对话方法，简化了响应处理逻辑
     */
    private String chatWithContextCache(String contextCacheId, List<ChatMessage> messages, VolcanoChatCallback callback) throws Exception {
        ArkService arkService = getArkService();
        VolcanoArkSettingVO setting = getVolcanoArkSetting();

        ContextChatCompletionRequest request = ContextChatCompletionRequest.builder()
                .model(setting.getModelId())
                .contextId(contextCacheId)
                .messages(messages)
                .maxTokens(DEFAULT_MAX_TOKENS)
                .temperature(DEFAULT_TEMPERATURE)
                .stream(false)
                .build();

        log.debug("上下文缓存对话请求: contextId={}, messagesCount={}, maxTokens={}",
                contextCacheId, messages.size(), DEFAULT_MAX_TOKENS);

        try {
            var response = arkService.createContextChatCompletion(request);
            return processContextChatResponse(response, contextCacheId, callback);

        } catch (VolcanoContentParsingException e) {
            return handleVolcanoContentException(e, contextCacheId);
        } catch (ArkHttpException e) {
            return handleArkHttpException(e, contextCacheId);
        }
    }

    /**
     * 处理上下文聊天响应
     * 直接处理响应对象，保持与原始代码一致的风格
     */
    private String processContextChatResponse(Object response, String contextCacheId, VolcanoChatCallback callback) throws Exception {
        if (response == null) {
            log.warn("上下文缓存对话响应为空: contextId={}", contextCacheId);
            throw new Exception("响应内容为空");
        }

        // 使用反射方式安全地处理响应，避免类型依赖问题
        try {
            // 获取choices
            var choicesMethod = response.getClass().getMethod("getChoices");
            @SuppressWarnings("unchecked")
            var choices = (java.util.List<Object>) choicesMethod.invoke(response);

            if (choices == null || choices.isEmpty()) {
                log.warn("上下文缓存对话响应choices为空: contextId={}", contextCacheId);
                throw new Exception("响应choices为空");
            }

            // 处理usage信息
            try {
                var usageMethod = response.getClass().getMethod("getUsage");
                Usage usage = (Usage) usageMethod.invoke(response);
                if (usage != null && usage.getPromptTokens() > DEFAULT_MAX_TOKENS && callback != null) {
                    callback.onChatFinish(usage);
                }
            } catch (Exception e) {
                log.debug("获取usage信息失败，忽略: {}", e.getMessage());
            }

            // 获取第一个choice
            var choice = choices.get(0);
            var finishReasonMethod = choice.getClass().getMethod("getFinishReason");
            String finishReason = (String) finishReasonMethod.invoke(choice);

            if ("length".equals(finishReason)) {
                log.warn("内容过长，触发重建上下文机制: contextId={}", contextCacheId);
                return CONTENT_TOO_LONG_RESPONSE;
            }

            // 获取消息内容
            var messageMethod = choice.getClass().getMethod("getMessage");
            var message = messageMethod.invoke(choice);

            if (message != null) {
                var contentMethod = message.getClass().getMethod("getContent");
                var content = contentMethod.invoke(message);
                if (content != null) {
                    String contentStr = String.valueOf(content);
                    log.debug("上下文缓存对话成功: contextId={}, contentLength={}", contextCacheId, contentStr.length());
                    return contentStr;
                }
            }

            log.warn("上下文缓存对话响应消息为空: contextId={}", contextCacheId);
            throw new Exception("响应消息内容为空");

        } catch (Exception e) {
            if (e.getMessage().contains("响应") || e.getMessage().contains("choices") || e.getMessage().contains("消息")) {
                throw e; // 重新抛出我们自己的异常
            }
            log.error("处理上下文聊天响应时出错: contextId={}, error={}", contextCacheId, e.getMessage());
            throw new Exception("处理响应失败: " + e.getMessage(), e);
        }
    }

    /**
     * 处理VolcanoContentParsingException
     */
    private String handleVolcanoContentException(VolcanoContentParsingException e, String contextCacheId) throws VolcanoContentParsingException {
        if ("CONTENT_TOO_LONG".equals(e.getErrorCode())) {
            log.warn("内容过长异常，返回特殊标识: contextId={}", contextCacheId);
            return CONTENT_TOO_LONG_RESPONSE;
        }
        throw e;
    }

    /**
     * 处理ArkHttpException
     */
    private String handleArkHttpException(ArkHttpException e, String contextCacheId) throws VolcanoContentParsingException {
        log.error("上下文缓存对话异常: contextId={}, code={}, message={}", contextCacheId, e.code, e.getMessage());

        if ("ResourceNotFound".equals(e.code)) {
            log.warn("上下文缓存资源未找到: contextId={}", contextCacheId);
            return RESOURCE_NOT_FOUND_RESPONSE;
        }
        throw VolcanoContentParsingException.emptyResponse();
    }

    /**
     * 文本转语音功能
     */
    @Override
    public TextToSpeechVO textConvertSpeech(String text, String voiceId) {
        if (!StringUtils.hasText(text)) {
            log.warn("文本转语音失败：文本内容为空");
            return createEmptyTextToSpeechVO();
        }

        log.info("火山方舟文本转语音 - 文本长度: {}, 音色ID: {}", text.length(), voiceId);

        String content = StringUtils.removeParenthesisContent(text);
        log.debug("处理后的文本长度: {}", content.length());

        Path uploadPath = UploadFileUtil.buildUploadPath(null, speechConfig.getTempPath());

        // 异步检查目录大小并在需要时清理
        CompletableFuture<Long> sizeCheckFuture = calculateDirectorySizeAsync(uploadPath);
        sizeCheckFuture.thenAccept(currentSize -> {
            if (currentSize >= speechConfig.getMaxDirSize()) {
                log.info("目录大小 {} 超过限制 {}，开始异步清理旧文件", currentSize, speechConfig.getMaxDirSize());
                cleanupOldFilesAsync(uploadPath);
            }
        }).exceptionally(throwable -> {
            log.error("检查目录大小时出错", throwable);
            return null;
        });

        try {
            // TODO: 实际的语音合成逻辑需要集成火山方舟的语音服务
            // 这里可以集成火山引擎的语音合成API
            TextToSpeechVO vo = createEmptyTextToSpeechVO();

            log.warn("火山方舟语音合成功能尚未完全实现，返回空结果");
            return vo;
        } catch (Exception e) {
            log.error("处理语音文件时出错: text={}, voiceId={}", text, voiceId, e);
            return createEmptyTextToSpeechVO();
        }
    }

    /**
     * 创建空的文本转语音结果
     */
    private TextToSpeechVO createEmptyTextToSpeechVO() {
        TextToSpeechVO vo = new TextToSpeechVO();
        vo.setUrl(""); // 暂时返回空URL
        vo.setDuration(DEFAULT_AUDIO_DURATION); // 暂时返回0时长
        return vo;
    }

    /**
     * 构建聊天消息列表
     */
    private List<ChatMessage> buildChatMessages(List<ChatContextVO> contextMessages) {
        List<ChatMessage> messages = new ArrayList<>();

        if (contextMessages == null) {
            return messages;
        }

        for (ChatContextVO item : contextMessages) {
            if (item.getRole() == null || !StringUtils.hasText(item.getContent())) {
                continue;
            }

            ChatMessageRole role = mapToArkRole(item.getRole());
            if (role != null) {
                messages.add(ChatMessage.builder()
                        .role(role)
                        .content(item.getContent())
                        .build());
            }
        }

        return messages;
    }

    /**
     * 映射角色到火山方舟SDK的角色类型
     */
    private ChatMessageRole mapToArkRole(String role) {
        return switch (role) {
            case ChatConstant.CHAT_USER_ROLE -> ChatMessageRole.USER;
            case ChatConstant.CHAT_ASSISTANT_ROLE -> ChatMessageRole.ASSISTANT;
            case ChatConstant.CHAT_SYSTEM_ROLE -> ChatMessageRole.SYSTEM;
            default -> {
                log.warn("未知的消息角色: {}", role);
                yield null;
            }
        };
    }

    /**
     * 构建AI响应对象
     */
    private AIResponseVO buildAIResponse(String contextCacheId, String responseContent, boolean audio, String voiceId) {
        AIResponseVO aiResponse = new AIResponseVO();
        aiResponse.setRemoteContextId(contextCacheId);
        aiResponse.setStatus("completed");
        aiResponse.setCreatedAt(LocalDateTime.now());
        aiResponse.setCompletedAt(LocalDateTime.now());

        List<ChatContextVO> contexts = new ArrayList<>();

        // 处理语音转换
        String audioUrl = null;
        String audioDuration = "";
        if (audio && StringUtils.hasText(voiceId)) {
            try {

                String content = ChatUtils.removeBracketContent(responseContent);
                
                // 判断content是否为空字符串或去掉中英文符号、空格后为空字符串
                String cleanedContent = content.replaceAll("[\\p{P}\\p{S}\\s]+", ""); // 去掉标点符号、符号和空格
                if (cleanedContent.isEmpty()) {
                    log.info("处理后的内容为空，跳过语音转换: originalContent={}, cleanedContent={}", content, cleanedContent);
                    // 跳过语音转换，audioUrl和audioDuration保持为null和空字符串
                } else {
                    TextToSpeechVO speechResult = agentCozeRemoteService.textConvertSpeech(content, voiceId,false);

                    if (speechResult != null) {
                        audioUrl = speechResult.getUrl();
                        audioDuration = speechResult.getDuration();
                    }
                }

//                TextToSpeechVO audioInfo = textConvertSpeech(content, voiceId);
            } catch (Exception e) {
                log.error("语音转换失败: voiceId={}, error={}", voiceId, e.getMessage());
            }
        }

        ChatContextVO context = ChatContextVO.builder()
                .role(ChatConstant.CHAT_ASSISTANT_ROLE)
                .type(ChatConstant.CHAT_MSG_ANSWER_TYPE)
                .content(responseContent)
                .contentType(ChatConstant.CHAT_CONTENT_TYPE_TEXT)
                .audioUrl(audioUrl)
                .audioDuration(audioDuration)
                .build();

        contexts.add(context);
        aiResponse.setContexts(contexts);
        aiResponse.setFollowMsg(new JSONArray());

        return aiResponse;
    }




    @Override
    public AIResponseVO sendChatMessage(String conversationId, List<ChatContextVO> message,
                                        boolean audio, String voiceId, VolcanoChatCallback callback) throws Exception {

        validateSendChatMessageParams(conversationId, message);

        log.debug("火山方舟发送聊天消息（带回调） - conversationId={}, messagesCount={}, audio={}, voiceId={}",
                conversationId, message != null ? message.size() : 0, audio, voiceId);

        return sendChatMessageWithContextCacheAndCallback(conversationId, message, audio, voiceId, callback);
    }

    /**
     * 验证发送聊天消息的参数
     */
    private void validateSendChatMessageParams(String conversationId, List<ChatContextVO> message) {
        if (!StringUtils.hasText(conversationId)) {
            throw new IllegalArgumentException("会话ID不能为空");
        }
        if (message == null || message.isEmpty()) {
            throw new IllegalArgumentException("消息列表不能为空");
        }
    }

    /**
     * 使用上下文缓存进行对话（带回调）
     * 优化的主要聊天方法，拆分为更小的逻辑单元
     */
    private AIResponseVO sendChatMessageWithContextCacheAndCallback(String conversationId,
                                                                   List<ChatContextVO> message, boolean audio, String voiceId,
                                                                   VolcanoChatCallback callback) throws Exception {

        // 处理特殊的错误会话ID
        conversationId = handleSpecialErrorConversationId(conversationId, message, callback);

        log.debug("开始上下文缓存对话（带回调）: conversationId={}, messagesCount={}, audio={}",
                conversationId, message.size(), audio);

        // 构建聊天消息列表
        List<ChatMessage> messages = buildChatMessages(message);
        if (messages.isEmpty()) {
            throw new IllegalArgumentException("没有有效的消息内容");
        }

        try {
            // 执行上下文缓存对话
            String responseContent = executeContextChatWithRetry(conversationId, message, messages, callback);

            // 构建AI响应
            AIResponseVO aiResponse = buildAIResponse(conversationId, responseContent, audio, voiceId);

            log.info("上下文缓存对话完成（带回调）: contextCacheId={}, responseLength={}",
                    conversationId, responseContent.length());
            return aiResponse;

        } catch (VolcanoContentParsingException e) {
            throw e;
        } catch (Exception e) {
            log.error("上下文缓存对话失败，回退到传统模式: conversationId={}", conversationId, e);
            return fallbackToTraditionalMode(conversationId, message, audio, voiceId, callback);
        }
    }

    /**
     * 处理特殊的错误会话ID
     */
    private String handleSpecialErrorConversationId(String conversationId, List<ChatContextVO> message, VolcanoChatCallback callback) {
        if ("error".equals(conversationId) && callback != null) {
            SystemContext systemContext = createSystemContext(conversationId, message);
            CreateRemoteSessionParamsDTO sessionParams = executeCallback(
                callback, conversationId, systemContext, "上下文缓存资源未找到",
                cb -> cb.onResourceNotFound(conversationId, systemContext, "上下文缓存资源未找到")
            );

            if (sessionParams != null && sessionParams.getContexts() != null) {
                return createConversation(sessionParams.getContexts());
            } else {
                log.error("回调未返回有效的系统消息");
            }
        }
        return conversationId;
    }

    /**
     * 执行上下文聊天并处理重试逻辑
     */
    private String executeContextChatWithRetry(String conversationId, List<ChatContextVO> message,
                                             List<ChatMessage> messages, VolcanoChatCallback callback) throws Exception {
        String contextCacheId = conversationId;

        // 调用上下文缓存对话API
        String responseContent = chatWithContextCache(contextCacheId, messages, callback);

        // 处理ResourceNotFound错误
        if (RESOURCE_NOT_FOUND_RESPONSE.equals(responseContent)) {
            responseContent = handleResourceNotFound(conversationId, contextCacheId, message, messages, callback);
            // 更新contextCacheId（如果重新创建了上下文）
            if (!RESOURCE_NOT_FOUND_RESPONSE.equals(responseContent)) {
                // 成功处理，responseContent现在包含实际响应
                return responseContent;
            } else {
                throw new Exception("上下文缓存功能不可用");
            }
        }

        // 处理ContentTooLong错误
        if (CONTENT_TOO_LONG_RESPONSE.equals(responseContent)) {
            responseContent = handleContentTooLong(conversationId, contextCacheId, message, messages, callback);
        }

        return responseContent;
    }

    /**
     * 回退到传统模式
     */
    private AIResponseVO fallbackToTraditionalMode(String conversationId, List<ChatContextVO> message,
                                                  boolean audio, String voiceId, VolcanoChatCallback callback) throws Exception {
        SystemContext systemContext = createSystemContext(conversationId, message);
        CreateRemoteSessionParamsDTO sessionParams = executeCallback(
            callback, conversationId, systemContext, "上下文缓存资源未找到",
            cb -> cb.onResourceNotFound(conversationId, systemContext, "上下文缓存资源未找到")
        );

        if (sessionParams == null || sessionParams.getContexts() == null) {
            throw new Exception("上下文缓存功能不可用");
        }

        // 构建新的上下文消息列表
        List<ChatContextVO> newContext = new ArrayList<>();
        sessionParams.getContexts().forEach(item ->
                newContext.add(ChatContextVO.builder()
                        .role(item.getRole())
                        .content(item.getContent())
                        .contentType("text")
                        .build())
        );
        newContext.addAll(message);

        return sendChatMessageTraditional(newContext, audio, voiceId);
    }

    /**
     * 传统方式进行对话（不使用上下文缓存）
     * 优化的传统模式，简化了错误处理
     */
    private AIResponseVO sendChatMessageTraditional(List<ChatContextVO> message, boolean audio, String voiceId) throws Exception {
        log.info("使用传统模式进行对话: messagesCount={}, audio={}", message.size(), audio);

        List<ChatMessage> chatMessages = buildChatMessages(message);
        if (chatMessages.isEmpty()) {
            throw new IllegalArgumentException("没有有效的消息内容");
        }

        ArkService arkService = getArkService();
        VolcanoArkSettingVO setting = getVolcanoArkSetting();

        ChatCompletionRequest request = ChatCompletionRequest.builder()
                .model(setting.getModelId())
                .messages(chatMessages)
                .maxTokens(DEFAULT_MAX_TOKENS)
                .temperature(DEFAULT_TEMPERATURE)
                .build();

        log.debug("发送传统模式请求到火山方舟: modelId={}, messagesCount={}",
                setting.getModelId(), chatMessages.size());

        try {
            ChatCompletionResult response = arkService.createChatCompletion(request);
            String responseContent = getResponseContent(response);
            AIResponseVO aiResponse = buildAIResponse(null, responseContent, audio, voiceId);

            log.info("传统模式对话完成: responseLength={}, contextsCount={}",
                    responseContent.length(), aiResponse.getContexts().size());
            return aiResponse;

        } catch (VolcanoContentParsingException e) {
            log.error("火山方舟内容解析异常: modelId={}, errorCode={}, error={}",
                    setting.getModelId(), e.getErrorCode(), e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("调用火山方舟API时出错: modelId={}, error={}", setting.getModelId(), e.getMessage());
            throw new Exception("火山方舟API调用失败: " + e.getMessage(), e);
        }
    }

    @Override
    public String sendCompressChatMessage(List<ChatContextVO> message) throws Exception {
        log.info("压缩提取会话: messagesCount={}", message != null ? message.size() : 0);

        if (message == null || message.isEmpty()) {
            throw new IllegalArgumentException("消息列表不能为空");
        }

        List<ChatMessage> chatMessages = buildChatMessages(message);
        if (chatMessages.isEmpty()) {
            throw new IllegalArgumentException("没有有效的消息内容");
        }

        ArkService arkService = getArkService();
        VolcanoArkSettingVO setting = getVolcanoArkSetting();

        ChatCompletionRequest request = ChatCompletionRequest.builder()
                .model(setting.getCompressModelId())
                .messages(chatMessages)
                .maxTokens(DEFAULT_COMPRESS_MAX_TOKENS)
                .temperature(DEFAULT_TEMPERATURE)
                .build();

        log.debug("发送压缩模式请求到火山方舟: modelId={}, messagesCount={}",
                setting.getCompressModelId(), chatMessages.size());

        try {
            var response = arkService.createChatCompletion(request);
            String responseContent = getResponseContent(response);

            log.info("压缩模式对话完成: responseLength={}", responseContent.length());
            return responseContent;

        } catch (VolcanoContentParsingException e) {
            log.error("火山方舟内容解析异常: modelId={}, errorCode={}, error={}",
                    setting.getCompressModelId(), e.getErrorCode(), e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("调用火山方舟API进行压缩时出错: modelId={}, error={}", setting.getCompressModelId(), e.getMessage());
            throw new Exception("火山方舟API调用失败: " + e.getMessage(), e);
        }
    }

}