package com.gw.chat.service.impl;

import com.gw.chat.dto.LikeActionDTO;
import com.gw.chat.entity.ConversationLike;
import com.gw.chat.repository.ConversationLikeRepository;
import com.gw.chat.service.ConversationLikeService;
import com.gw.chat.vo.ConversationLikeVO;
import com.gw.chat.vo.LikeStatsVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Log4j2
@Service
@RequiredArgsConstructor
public class ConversationLikeServiceImpl implements ConversationLikeService {

    private final ConversationLikeRepository likeRepository;

    @Override
    public ConversationLikeVO likeConversation(String username, LikeActionDTO likeDTO, Long agentId, String agentUsername) {
        // 查找是否已经存在点赞记录
        Optional<ConversationLike> existingLike = likeRepository.findByLikerAndSessionId(
                username, likeDTO.getSessionId());

        ConversationLike like;
        if (existingLike.isPresent()) {
            // 更新已有记录
            like = existingLike.get();
            like.setLiked(likeDTO.getLiked());
        } else {
            // 创建新记录
            like = ConversationLike.builder()
                    .liker(username)
                    .agentId(agentId)
                    .sessionId(likeDTO.getSessionId())
                    .liked(likeDTO.getLiked())
                    .build();
        }

        ConversationLike savedLike = likeRepository.save(like);
        return convertToLikeVO(savedLike);
    }

    @Override
    public LikeStatsVO getLikeStats(String conversationId, String username) {
        long likeCount = likeRepository.countBySessionIdAndLiked(conversationId, true);
        boolean userLiked = hasUserLiked(username, conversationId);

        return LikeStatsVO.builder()
                .conversationId(conversationId)
                .likeCount(likeCount)
                .userLiked(userLiked)
                .build();
    }

    @Override
    public boolean hasUserLiked(String username, String conversationId) {
        Optional<ConversationLike> userLike = likeRepository.findByLikerAndSessionId(username, conversationId);
        return userLike.isPresent() && userLike.get().getLiked();
    }

    @Override
    public Page<ConversationLikeVO> getLikesByConversationId(String conversationId, Pageable pageable) {
        Page<ConversationLike> likes = likeRepository.findBySessionIdAndLikedOrderByCreatedAtDesc(
                conversationId, true, pageable);
        return likes.map(this::convertToLikeVO);
    }

    @Override
    public Page<ConversationLikeVO> getLikesByUsername(String username, Long agentId, Pageable pageable) {
        Page<ConversationLike> likes = likeRepository.findByLikerAndAgentIdAndLikedOrderByCreatedAtDesc(
                username, agentId, true, pageable);
        return likes.map(this::convertToLikeVO);
    }

    /**
     * 转换点赞实体为VO
     */
    private ConversationLikeVO convertToLikeVO(ConversationLike like) {
        return ConversationLikeVO.builder()
                .id(like.getId())
                .liker(like.getLiker())
                .likerNickname(like.getLiker()) // 这里可以调用用户服务获取昵称
                .agentId(like.getAgentId())
                .conversationId(like.getSessionId())
                .liked(like.getLiked())
                .createdAt(like.getCreatedAt())
                .updatedAt(like.getUpdatedAt())
                .build();
    }
} 