package com.gw.chat.service;

import com.gw.chat.dto.ChatMessageDto;
import com.gw.chat.event.WebSocketMessageEvent;
import lombok.extern.log4j.Log4j2;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

/**
 * WebSocket消息推送服务
 */
@Service
@Log4j2
public class WebSocketService {
    private static final Logger log = LogManager.getLogger(WebSocketService.class);
    private final ApplicationEventPublisher eventPublisher;

    // Standard constructor for Spring dependency injection
    public WebSocketService(ApplicationEventPublisher eventPublisher) {
        this.eventPublisher = eventPublisher;
    }

    /**
     * 向特定用户发送消息
     *
     * @param username 用户ID
     * @param message  消息内容
     */
    public void sendMessageToUser(String username, ChatMessageDto message) {
        log.info("发送用户消息事件 - 用户: {}, 会话: {}, 消息ID: {}, 消息类型: {}",
                username, message != null ? message.getSessionId() : "null",
                message != null ? message.getId() : "null",
                message != null ? message.getType() : "null");
        eventPublisher.publishEvent(
                new WebSocketMessageEvent(this, username, message, WebSocketMessageEvent.MessageType.USER));
        log.debug("用户消息事件发布完成 - 用户: {}", username);
    }

    /**
     * 向会话中的所有人广播消息
     *
     * @param sessionId 会话ID
     * @param message   消息内容
     */
    public void broadcastToSession(String sessionId, ChatMessageDto message) {
        log.debug("向会话 {} 广播WebSocket消息: {}", sessionId, message);
        // 在当前实现中，我们使用用户点对点方式发送消息
        // 如需广播功能，可以通过事件实现
    }

    /**
     * 发送系统消息
     *
     * @param userId    用户ID
     * @param sessionId 会话ID
     * @param content   消息内容
     */
    public void sendSystemMessage(String userId, String sessionId, String content) {
        log.info("发送系统消息事件 - 用户: {}, 会话: {}, 内容长度: {}",
                userId, sessionId, content != null ? content.length() : 0);
        eventPublisher.publishEvent(new WebSocketMessageEvent(this, userId, sessionId, content));
        log.debug("系统消息事件发布完成 - 用户: {}, 会话: {}", userId, sessionId);
    }
}