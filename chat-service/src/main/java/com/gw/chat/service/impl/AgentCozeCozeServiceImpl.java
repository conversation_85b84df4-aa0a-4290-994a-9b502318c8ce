package com.gw.chat.service.impl;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.coze.openapi.client.audio.common.AudioFormat;
import com.coze.openapi.client.audio.speech.CreateSpeechReq;
import com.coze.openapi.client.audio.speech.CreateSpeechResp;
import com.coze.openapi.client.chat.CreateChatReq;
import com.coze.openapi.client.chat.model.ChatPoll;
import com.coze.openapi.client.connversations.CreateConversationReq;
import com.coze.openapi.client.connversations.CreateConversationResp;
import com.coze.openapi.client.connversations.message.CreateMessageReq;
import com.coze.openapi.client.connversations.message.CreateMessageResp;
import com.coze.openapi.client.connversations.message.model.Message;
import com.coze.openapi.client.connversations.message.model.MessageContentType;
import com.coze.openapi.client.connversations.message.model.MessageRole;
import com.coze.openapi.client.connversations.message.model.MessageType;
import com.coze.openapi.service.auth.TokenAuth;
import com.coze.openapi.service.service.CozeAPI;
import com.gw.chat.config.SpeechConfig;
import com.gw.chat.constant.ChatConstant;
import com.gw.chat.service.AgentCozeRemoteService;
import com.gw.chat.utils.ChatUtils;
import com.gw.chat.vo.AIResponseVO;
import com.gw.chat.vo.ChatContextVO;
import com.gw.chat.vo.TextToSpeechVO;
import com.gw.common.agent.service.AgentProxyService;
import com.gw.common.agent.vo.CozeSettingVO;
import com.gw.common.util.DateTimeUtils;
import com.gw.common.util.StringUtils;
import com.gw.common.util.UploadFileUtil;
import com.mpatric.mp3agic.Mp3File;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.attribute.FileTime;
import java.util.*;
import java.util.stream.Stream;


@Service
@Log4j2
@RequiredArgsConstructor
public class AgentCozeCozeServiceImpl implements AgentCozeRemoteService {
    private final AgentProxyService agentProxyService;
    private final SpeechConfig speechConfig;

    private long calculateDirectorySize(Path directory) {
        try (Stream<Path> paths = Files.walk(directory)) {  // ✅ 使用try-with-resources
            return paths
                    .filter(Files::isRegularFile)
                    .mapToLong(path -> {
                        try {
                            return Files.size(path);
                        } catch (IOException e) {
                            log.error("Error getting file size: {}", path, e);
                            return 0L;
                        }
                    })
                    .sum();
        } catch (IOException e) {
            log.error("Error calculating directory size: {}", directory, e);
            return 0L;
        }
    }

    private void cleanupOldFiles(Path directory) {
        try (Stream<Path> pathStream = Files.walk(directory)) {  // ✅ 使用try-with-resources
            // 先收集文件信息，避免在排序时文件被删除
            List<FileInfo> fileInfos = pathStream
                    .filter(Files::isRegularFile)
                    .map(path -> {
                        try {
                            return new FileInfo(path, Files.getLastModifiedTime(path));
                        } catch (IOException e) {
                            // 文件可能在检查过程中被删除，跳过此文件
                            log.debug("File no longer exists during cleanup scan: {}", path);
                            return null;
                        }
                    })
                    .filter(Objects::nonNull)  // 过滤掉null值
                    .sorted(Comparator.comparing(FileInfo::lastModifiedTime))
                    .toList();

            // Delete oldest files until directory size is under limit
            long currentSize = calculateDirectorySize(directory);
            int deletedCount = 0;

            for (FileInfo fileInfo : fileInfos) {
                if (currentSize <= speechConfig.getMaxDirSize()) {
                    break;
                }

                Path file = fileInfo.path();
                try {
                    // 再次检查文件是否存在，因为可能在收集信息后被删除
                    if (!Files.exists(file)) {
                        log.debug("File no longer exists during cleanup: {}", file);
                        continue;
                    }

                    long fileSize = Files.size(file);
                    Files.delete(file);
                    currentSize -= fileSize;
                    deletedCount++;
                    log.info("Deleted old file: {}", file);
                } catch (IOException e) {
                    log.error("Error deleting file: {}", file, e);
                }
            }

            if (deletedCount > 0) {
                log.info("Cleanup completed, deleted {} old files, current directory size: {} bytes",
                        deletedCount, currentSize);
            }
        } catch (IOException e) {
            log.error("Error cleaning up old files: {}", directory, e);
        }
    }

    /**
     * 文件信息记录类，用于安全地处理文件排序
     */
    private record FileInfo(Path path, FileTime lastModifiedTime) {}

    private CozeAPI getCozeAPI() {
        CozeSettingVO vo = agentProxyService.getCozeSetting();
        TokenAuth authCli = new TokenAuth(vo.getToken());
        return new CozeAPI.Builder()
                .baseURL(vo.getApiBase())
                .auth(authCli)
                .readTimeout(10000)
                .connectTimeout(10000)
                .build();
    }

    @Override
    public String createConversation() {
        CozeAPI cozeAPI = getCozeAPI();

        CreateConversationResp resp = cozeAPI.conversations().create(new CreateConversationReq());
        System.out.println("create conversations" + resp);
        return resp.getConversation().getId();
    }

    /**
     * Convert text to speech and return file path and duration
     *
     * @param text    The text content to convert to speech
     * @param voiceId The voice ID to use for the speech
     * @return TextToSpeechVO containing the file path and duration
     */
    @Override
    public TextToSpeechVO textConvertSpeech(String text, String voiceId,boolean fixed) {
        CozeAPI cozeAPI = getCozeAPI();
        String content = StringUtils.removeParenthesisContent(text);
        log.info("textConvertSpeech text:{}, fixed:{}", content, fixed);

        CreateSpeechReq req = CreateSpeechReq.builder().input(content).voiceID(voiceId).build();
        req.setSampleRate(24000);
        req.setResponseFormat(AudioFormat.MP3);
        req.setSpeed(1.0f);
        CreateSpeechResp resp = cozeAPI.audio().speech().create(req);

        // Choose storage path based on fixed parameter
        String storagePath = fixed ? speechConfig.getFixedPath() : speechConfig.getTempPath();
        Path uploadPath = UploadFileUtil.buildUploadPath(null, storagePath);

        // Only check directory size and clean up for temporary files (not fixed files)
        if (!fixed) {
            long currentSize = calculateDirectorySize(uploadPath);
            if (currentSize >= speechConfig.getMaxDirSize()) {
                log.info("Directory size {} exceeds limit of {}, cleaning up old files",
                        currentSize, speechConfig.getMaxDirSize());
                cleanupOldFiles(uploadPath);
            }
        }

        try {
            String filename = System.currentTimeMillis() + ".mp3";
            String fullPath = uploadPath.resolve(filename).toString();
            resp.writeToFile(fullPath);

            // Parse MP3 duration
            Mp3File mp3file = new Mp3File(fullPath);
            long durationInSeconds = mp3file.getLengthInSeconds();
            String formattedDuration = formatDuration(durationInSeconds);

            log.info("MP3 saved at {} with duration {} (fixed: {})", fullPath, formattedDuration, fixed);
            TextToSpeechVO vo = new TextToSpeechVO();
            vo.setUrl(storagePath + filename);
            vo.setDuration(formattedDuration);
            return vo;
        } catch (Exception e) {
            log.error("Error processing MP3 file", e);
            return null;
        }
    }

    @Override
    public void sendMessage(String conversationId, String botId, String message) {
        CozeAPI cozeAPI = getCozeAPI();
        CreateMessageReq.CreateMessageReqBuilder builder = CreateMessageReq.builder();
        builder
                .conversationID(conversationId)
                .role(MessageRole.USER)
                .content(message)
                .contentType(MessageContentType.TEXT);
        CreateMessageResp messageResp = cozeAPI.conversations().messages().create(builder.build());

    }

    /**
     * Format duration in seconds to MM:SS format
     *
     * @param seconds Duration in seconds
     * @return Formatted duration
     */
    private String formatDuration(long seconds) {
        long minutes = seconds / 60;
        long remainingSeconds = seconds % 60;
        return String.format("%d:%02d", minutes, remainingSeconds);
    }

    @Override
    public AIResponseVO sendChatMessage(String conversationId, String botId, String uid, List<ChatContextVO> message,
                                        Map<String, String> customVariables,
                                        boolean audio, String voiceId) throws Exception {
        List<Message> additionalMessages = new ArrayList<>();
        CozeAPI cozeAPI = getCozeAPI();
        message.forEach(item -> {
            if (item.getRole() == null) {
                return;
            }
            MessageRole role = switch (item.getRole()) {
                case ChatConstant.CHAT_USER_ROLE -> MessageRole.USER;
                case ChatConstant.CHAT_ASSISTANT_ROLE -> MessageRole.ASSISTANT;
                default -> MessageRole.UNKNOWN;
            };
            if (item.getType() == null) {
                return;
            }

            MessageType type = switch (item.getType()) {
                case ChatConstant.CHAT_MSG_QUESTION_TYPE -> MessageType.QUESTION;
                case ChatConstant.CHAT_MSG_ANSWER_TYPE -> MessageType.ANSWER;
                case ChatConstant.CHAT_MSG_FUNCTION_CALL_TYPE -> MessageType.FUNCTION_CALL;
                case ChatConstant.CHAT_MSG_TOOL_RESPONSE_TYPE -> MessageType.TOOL_RESPONSE;
                case ChatConstant.CHAT_MSG_FOLLOW_UP_TYPE -> MessageType.FOLLOW_UP;
                case ChatConstant.CHAT_MSG_VERBOSE_TYPE -> MessageType.VERBOSE;
                default -> MessageType.UNKNOWN;
            };
            if (item.getContentType() == null) {
                return;
            }
            MessageContentType contentType = switch (item.getContentType()) {
                case ChatConstant.CHAT_CONTENT_TYPE_TEXT -> MessageContentType.TEXT;
                case ChatConstant.CHAT_CONTENT_TYPE_OBJECT -> MessageContentType.OBJECT_STRING;
                case ChatConstant.CHAT_CONTENT_TYPE_CARD -> MessageContentType.CARD;
                default -> MessageContentType.UNKNOWN;
            };
            additionalMessages.add(Message.builder()
                    .role(role)
                    .type(type)
                    .content(item.getContent())
                    .contentType(contentType)
                    .build());

        });
        CreateChatReq req =
                CreateChatReq.builder()
                        .conversationID(conversationId)
                        .customVariables(customVariables)
                        .botID(botId)
                        .userID(uid)
                        .autoSaveHistory(true)
                        .messages(additionalMessages)
                        .build();
        log.info("查询并且轮询");
        ChatPoll chat = cozeAPI.chat().createAndPoll(req, 20L);
        AIResponseVO rsp = new AIResponseVO();
        log.info("rsp:{}", JSONObject.toJSONString(chat));
        rsp.setStatus(chat.getChat().getStatus().getValue());
        if (chat.getChat().getCreatedAt() != null) {
            rsp.setCreatedAt(DateTimeUtils.convertUnixTimestampToLocalDateTime(chat.getChat().getCreatedAt()));
        }
        if (chat.getChat().getCompletedAt() != null) {
            rsp.setCompletedAt(DateTimeUtils.convertUnixTimestampToLocalDateTime(chat.getChat().getCompletedAt()));
        }
        if (chat.getChat().getFailedAt() != null) {
            rsp.setFailedAt(DateTimeUtils.convertUnixTimestampToLocalDateTime(chat.getChat().getFailedAt()));
        }
        List<ChatContextVO> contextRsp = new ArrayList<>();
        JSONArray followMsg = new JSONArray();
        for (Message rspMsg : chat.getMessages()) {
            if (rspMsg.getType().equals(MessageType.ANSWER)) {
                String audioUrl = null;
                String audioDuration = "";
                if (audio) {
                    var content = ChatUtils.removeBracketContent(rspMsg.getContent());
                    TextToSpeechVO audioInfo = textConvertSpeech(content, voiceId,false);
                    if (audioInfo != null) {
                        audioUrl = audioInfo.getUrl();
                        audioDuration = audioInfo.getDuration();
                    }
                }
                contextRsp.add(ChatContextVO.builder()
                        .role(rspMsg.getRole().getValue())
                        .type(rspMsg.getType().getValue())
                        .content(rspMsg.getContent())
                        .audioUrl(audioUrl)
                        .audioDuration(audioDuration)
                        .contentType(rspMsg.getContentType().getValue())
                        .build());

            } else if (rspMsg.getType().equals(MessageType.FOLLOW_UP)) {
                followMsg.add(rspMsg.getContent());
            }
        }
        rsp.setFollowMsg(followMsg);
        rsp.setContexts(contextRsp);
        return rsp;
    }
}
