package com.gw.chat.service.impl;

import com.gw.chat.dto.CommentCreateDTO;
import com.gw.chat.dto.CommentReplyDTO;
import com.gw.chat.entity.ConversationComment;
import com.gw.chat.repository.ConversationCommentRepository;
import com.gw.chat.service.ConversationCommentService;
import com.gw.chat.vo.CommentVO;
import com.gw.chat.vo.CommentWithRepliesVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Log4j2
@Service
@RequiredArgsConstructor
public class ConversationCommentServiceImpl implements ConversationCommentService {

    private final ConversationCommentRepository commentRepository;

    @Override
    public CommentVO createComment(String commenter, CommentCreateDTO commentDTO, Long agentId, String agentUser) {
        ConversationComment comment = ConversationComment.builder()
                .agentUser(agentUser)
                .agentId(agentId)
                .sessionId(commentDTO.getSessionId())
                .commenter(commenter)
                .content(commentDTO.getContent())
                .status(ConversationComment.CommentStatus.ACTIVE)
                .depth(0)
                .replyCount(0)
                .build();

        ConversationComment savedComment = commentRepository.save(comment);
        return convertToCommentVO(savedComment);
    }

    @Override
    public CommentVO replyToComment(String commenter, CommentReplyDTO replyDTO) {
        // 获取父评论
        ConversationComment parentComment = commentRepository.findById(replyDTO.getParentId())
                .orElseThrow(() -> new RuntimeException("父评论不存在"));

        // 获取回复的深度
        int depth = parentComment.getDepth() + 1;
        if (depth > 3) {
            throw new RuntimeException("评论嵌套深度超过限制");
        }

        // 创建回复评论
        ConversationComment replyComment = ConversationComment.builder()
                .agentUser(parentComment.getAgentUser())
                .agentId(parentComment.getAgentId())
                .commenter(commenter)
                .content(replyDTO.getContent())
                .parentId(replyDTO.getParentId())
                .replyToId(replyDTO.getReplyToId())
                .replyToUsername(replyDTO.getReplyToUsername())
                .status(ConversationComment.CommentStatus.ACTIVE)
                .depth(depth)
                .replyCount(0)
                .build();

        ConversationComment savedReply = commentRepository.save(replyComment);

        // 更新父评论的回复数
        updateReplyCount(parentComment.getId());

        return convertToCommentVO(savedReply);
    }

    @Override
    public void deleteComment(String commentId, String commenter) {
        ConversationComment comment = commentRepository.findById(commentId)
                .orElseThrow(() -> new RuntimeException("评论不存在"));

        // 检查权限
        if (!Objects.equals(comment.getCommenter(), commenter)) {
            throw new RuntimeException("无权删除此评论");
        }

        // 逻辑删除评论
        comment.setStatus(ConversationComment.CommentStatus.DELETED);
        commentRepository.save(comment);

        // 如果有父评论，更新父评论的回复数
        if (comment.getParentId() != null) {
            updateReplyCount(comment.getParentId());
        }
    }

    @Override
    public CommentVO getComment(String commentId) {
        ConversationComment comment = commentRepository.findById(commentId)
                .orElseThrow(() -> new RuntimeException("评论不存在"));

        if (comment.getStatus() == ConversationComment.CommentStatus.DELETED) {
            throw new RuntimeException("评论已删除");
        }

        return convertToCommentVO(comment);
    }

    @Override
    public CommentWithRepliesVO getCommentWithReplies(String commentId) {
        // 获取评论
        CommentVO comment = getComment(commentId);

        // 获取回复列表
        List<ConversationComment> replies = commentRepository.findByParentIdAndStatusOrderByCreatedAtAsc(
                commentId, ConversationComment.CommentStatus.ACTIVE);

        List<CommentVO> replyVOs = replies.stream()
                .map(this::convertToCommentVO)
                .collect(Collectors.toList());

        return CommentWithRepliesVO.builder()
                .comment(comment)
                .replies(replyVOs)
                .build();
    }

    @Override
    public Page<CommentWithRepliesVO> getCommentsBySessionId(String sessionId, Pageable pageable) {
        // 分页查询根评论
        Page<ConversationComment> rootComments = commentRepository.findBySessionIdAndParentIdIsNullAndStatusOrderByCreatedAtDesc(
                sessionId, ConversationComment.CommentStatus.ACTIVE, pageable);

        // 转换为带回复的评论VO
        return rootComments.map(rootComment -> {
            CommentVO commentVO = convertToCommentVO(rootComment);

            // 查询回复
            List<ConversationComment> replies = commentRepository.findByParentIdAndStatusOrderByCreatedAtAsc(
                    rootComment.getId(), ConversationComment.CommentStatus.ACTIVE);

            List<CommentVO> replyVOs = replies.stream()
                    .map(this::convertToCommentVO)
                    .collect(Collectors.toList());

            return CommentWithRepliesVO.builder()
                    .comment(commentVO)
                    .replies(replyVOs)
                    .build();
        });
    }

    @Override
    public Page<CommentVO> getCommentsByUsername(String username, Long agentId, Pageable pageable) {
        Page<ConversationComment> comments = commentRepository.findByAgentUserAndAgentIdAndStatusOrderByCreatedAtDesc(
                username, agentId, ConversationComment.CommentStatus.ACTIVE, pageable);

        return comments.map(this::convertToCommentVO);
    }

    /**
     * 更新评论的回复数
     */
    private void updateReplyCount(String commentId) {
        ConversationComment comment = commentRepository.findById(commentId).orElse(null);
        if (comment != null) {
            long replyCount = commentRepository.countByParentIdAndStatusNot(
                    commentId, ConversationComment.CommentStatus.DELETED);
            comment.setReplyCount((int) replyCount);
            commentRepository.save(comment);
        }
    }

    /**
     * 转换评论实体为VO
     */
    private CommentVO convertToCommentVO(ConversationComment comment) {
        return CommentVO.builder()
                .id(comment.getId())
                .commenter(comment.getCommenter())
                .commenterNickname(comment.getCommenter()) // 这里可以调用用户服务获取昵称
                .agentId(comment.getAgentId())
                .content(comment.getContent())
                .parentId(comment.getParentId())
                .replyToId(comment.getReplyToId())
                .replyToUsername(comment.getReplyToUsername())
                .depth(comment.getDepth())
                .replyCount(comment.getReplyCount())
                .createdAt(comment.getCreatedAt())
                .build();
    }
} 