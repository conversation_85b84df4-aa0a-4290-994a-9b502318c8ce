package com.gw.chat.service;

import com.gw.chat.entity.ChatCompressHistory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Optional;

/**
 * 聊天压缩历史记录服务接口
 */
public interface ChatCompressHistoryService {
    
    /**
     * 保存压缩历史记录
     *
     * @param history 压缩历史记录
     * @return 保存后的记录
     */
    ChatCompressHistory save(ChatCompressHistory history);
    
    /**
     * 根据ID查找压缩历史记录
     *
     * @param id 记录ID
     * @return 压缩历史记录
     */
    Optional<ChatCompressHistory> findById(String id);
    
    /**
     * 根据会话ID查找压缩历史记录列表
     *
     * @param sessionId 会话ID
     * @return 压缩历史记录列表
     */
    List<ChatCompressHistory> findBySessionId(String sessionId);
    
    /**
     * 根据会话ID分页查找压缩历史记录
     *
     * @param sessionId 会话ID
     * @param pageable 分页参数
     * @return 分页的压缩历史记录
     */
    Page<ChatCompressHistory> findBySessionId(String sessionId, Pageable pageable);
    
    /**
     * 根据用户名查找压缩历史记录
     *
     * @param username 用户名
     * @param pageable 分页参数
     * @return 分页的压缩历史记录
     */
    Page<ChatCompressHistory> findByUsername(String username, Pageable pageable);
    
    /**
     * 根据智能体ID查找压缩历史记录
     *
     * @param agentId 智能体ID
     * @param pageable 分页参数
     * @return 分页的压缩历史记录
     */
    Page<ChatCompressHistory> findByAgentId(Long agentId, Pageable pageable);
    
    /**
     * 获取会话的最新压缩序列号
     *
     * @param sessionId 会话ID
     * @return 最新的压缩序列号
     */
    Long getLatestCompressSequence(String sessionId);
    
    /**
     * 根据会话ID和压缩序列号查找记录
     *
     * @param sessionId 会话ID
     * @param compressSequence 压缩序列号
     * @return 压缩历史记录
     */
    Optional<ChatCompressHistory> findBySessionIdAndCompressSequence(String sessionId, Long compressSequence);
    
    /**
     * 删除指定时间之前的历史记录
     *
     * @param beforeTime 时间戳
     * @return 删除的记录数量
     */
    long deleteHistoryBefore(Long beforeTime);
    
    /**
     * 统计会话的压缩历史记录数量
     *
     * @param sessionId 会话ID
     * @return 记录数量
     */
    long countBySessionId(String sessionId);
}
