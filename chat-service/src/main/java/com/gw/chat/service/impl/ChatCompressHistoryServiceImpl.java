package com.gw.chat.service.impl;

import com.gw.chat.entity.ChatCompressHistory;
import com.gw.chat.service.ChatCompressHistoryService;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.support.PageableExecutionUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * 聊天压缩历史记录服务实现类
 */
@Service
@RequiredArgsConstructor
@Log4j2
public class ChatCompressHistoryServiceImpl implements ChatCompressHistoryService {
    
    private final MongoTemplate mongoTemplate;
    
    @Override
    public ChatCompressHistory save(ChatCompressHistory history) {
        try {
            return mongoTemplate.save(history);
        } catch (Exception e) {
            log.error("保存压缩历史记录失败", e);
            throw new RuntimeException("保存压缩历史记录失败", e);
        }
    }
    
    @Override
    public Optional<ChatCompressHistory> findById(String id) {
        try {
            ChatCompressHistory history = mongoTemplate.findById(id, ChatCompressHistory.class);
            return Optional.ofNullable(history);
        } catch (Exception e) {
            log.error("根据ID查找压缩历史记录失败: {}", id, e);
            return Optional.empty();
        }
    }
    
    @Override
    public List<ChatCompressHistory> findBySessionId(String sessionId) {
        try {
            Query query = new Query(Criteria.where("sessionId").is(sessionId))
                    .with(Sort.by(Sort.Direction.DESC, "compressSequence"));
            return mongoTemplate.find(query, ChatCompressHistory.class);
        } catch (Exception e) {
            log.error("根据会话ID查找压缩历史记录失败: {}", sessionId, e);
            return List.of();
        }
    }
    
    @Override
    public Page<ChatCompressHistory> findBySessionId(String sessionId, Pageable pageable) {
        try {
            Query query = new Query(Criteria.where("sessionId").is(sessionId))
                    .with(pageable);
            
            List<ChatCompressHistory> content = mongoTemplate.find(query, ChatCompressHistory.class);
            
            return PageableExecutionUtils.getPage(content, pageable, () -> {
                Query countQuery = new Query(Criteria.where("sessionId").is(sessionId));
                return mongoTemplate.count(countQuery, ChatCompressHistory.class);
            });
        } catch (Exception e) {
            log.error("分页查找会话压缩历史记录失败: {}", sessionId, e);
            return Page.empty(pageable);
        }
    }
    
    @Override
    public Page<ChatCompressHistory> findByUsername(String username, Pageable pageable) {
        try {
            Query query = new Query(Criteria.where("username").is(username))
                    .with(pageable);
            
            List<ChatCompressHistory> content = mongoTemplate.find(query, ChatCompressHistory.class);
            
            return PageableExecutionUtils.getPage(content, pageable, () -> {
                Query countQuery = new Query(Criteria.where("username").is(username));
                return mongoTemplate.count(countQuery, ChatCompressHistory.class);
            });
        } catch (Exception e) {
            log.error("分页查找用户压缩历史记录失败: {}", username, e);
            return Page.empty(pageable);
        }
    }
    
    @Override
    public Page<ChatCompressHistory> findByAgentId(Long agentId, Pageable pageable) {
        try {
            Query query = new Query(Criteria.where("agentId").is(agentId))
                    .with(pageable);
            
            List<ChatCompressHistory> content = mongoTemplate.find(query, ChatCompressHistory.class);
            
            return PageableExecutionUtils.getPage(content, pageable, () -> {
                Query countQuery = new Query(Criteria.where("agentId").is(agentId));
                return mongoTemplate.count(countQuery, ChatCompressHistory.class);
            });
        } catch (Exception e) {
            log.error("分页查找智能体压缩历史记录失败: {}", agentId, e);
            return Page.empty(pageable);
        }
    }
    
    @Override
    public Long getLatestCompressSequence(String sessionId) {
        try {
            Query query = new Query(Criteria.where("sessionId").is(sessionId))
                    .with(Sort.by(Sort.Direction.DESC, "compressSequence"))
                    .limit(1);
            
            ChatCompressHistory latest = mongoTemplate.findOne(query, ChatCompressHistory.class);
            return latest != null ? latest.getCompressSequence() : 0L;
        } catch (Exception e) {
            log.error("获取最新压缩序列号失败: {}", sessionId, e);
            return 0L;
        }
    }
    
    @Override
    public Optional<ChatCompressHistory> findBySessionIdAndCompressSequence(String sessionId, Long compressSequence) {
        try {
            Query query = new Query(Criteria.where("sessionId").is(sessionId)
                    .and("compressSequence").is(compressSequence));
            
            ChatCompressHistory history = mongoTemplate.findOne(query, ChatCompressHistory.class);
            return Optional.ofNullable(history);
        } catch (Exception e) {
            log.error("根据会话ID和压缩序列号查找记录失败: sessionId={}, sequence={}", sessionId, compressSequence, e);
            return Optional.empty();
        }
    }
    
    @Override
    public long deleteHistoryBefore(Long beforeTime) {
        try {
            Query query = new Query(Criteria.where("compressedTime").lt(beforeTime));
            return mongoTemplate.remove(query, ChatCompressHistory.class).getDeletedCount();
        } catch (Exception e) {
            log.error("删除历史记录失败: beforeTime={}", beforeTime, e);
            return 0;
        }
    }
    
    @Override
    public long countBySessionId(String sessionId) {
        try {
            Query query = new Query(Criteria.where("sessionId").is(sessionId));
            return mongoTemplate.count(query, ChatCompressHistory.class);
        } catch (Exception e) {
            log.error("统计会话压缩历史记录数量失败: {}", sessionId, e);
            return 0;
        }
    }
}
