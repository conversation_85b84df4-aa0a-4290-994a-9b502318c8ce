package com.gw.chat.service;

import com.gw.chat.entity.ChatMessage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 聊天消息服务类，支持动态表名
 */
@Service
public class ChatMessageService {

    @Autowired
    private MongoTemplate mongoTemplate;

    /**
     * 根据agentId生成集合名称
     */
    private String getCollectionName(Long agentId) {
        return "chat_messages_" + agentId;
    }

    /**
     * 保存消息
     */
    public ChatMessage save(ChatMessage chatMessage) {
        String collectionName = getCollectionName(chatMessage.getAgentId());
        return mongoTemplate.save(chatMessage, collectionName);
    }

    /**
     * 根据ID查找消息
     */
    public Optional<ChatMessage> findById(String id, Long agentId) {
        String collectionName = getCollectionName(agentId);
        ChatMessage message = mongoTemplate.findById(id, ChatMessage.class, collectionName);
        return Optional.ofNullable(message);
    }

    /**
     * 根据会话ID查询指定数量的最新消息
     */
    public List<ChatMessage> findBySessionIdOrderByCreatedAtDescLimit(String sessionId, Long agentId, Pageable pageable) {
        String collectionName = getCollectionName(agentId);
        Query query = new Query(Criteria.where("sessionId").is(sessionId))
                .with(pageable)
                .with(org.springframework.data.domain.Sort.by(org.springframework.data.domain.Sort.Direction.DESC, "createdAt"));
        return mongoTemplate.find(query, ChatMessage.class, collectionName);
    }
    public List<ChatMessage> findAllBySeqNum(String sessionId,Long agentId,Long seqNum){
        String collectionName = getCollectionName(agentId);
        Query query = new Query();
        query.addCriteria(Criteria.where("sessionId").is(sessionId).and("seqNum").gte(seqNum));
        return mongoTemplate.find(query, ChatMessage.class, collectionName);
    }
    /**
     * 根据会话ID查询消息
     */
    public List<ChatMessage> pageBySessionIdOrderByCreatedAtAsc(String sessionId, Long agentId) {
        String collectionName = getCollectionName(agentId);
        Query query = new Query(Criteria.where("sessionId").is(sessionId))
                .with(org.springframework.data.domain.Sort.by(org.springframework.data.domain.Sort.Direction.ASC, "createdAt"));
        return mongoTemplate.find(query, ChatMessage.class, collectionName);
    }

    /**
     * 根据会话ID分页查询消息
     */
    public Page<ChatMessage> findBySessionIdOrderByCreatedAtDesc(String sessionId, Long agentId, Pageable pageable) {
        String collectionName = getCollectionName(agentId);
        Query query = new Query(Criteria.where("sessionId").is(sessionId))
                .with(pageable)
                .with(org.springframework.data.domain.Sort.by(org.springframework.data.domain.Sort.Direction.DESC, "createdAt"));

        List<ChatMessage> messages = mongoTemplate.find(query, ChatMessage.class, collectionName);
        long total = mongoTemplate.count(Query.query(Criteria.where("sessionId").is(sessionId)), ChatMessage.class, collectionName);

        return new PageImpl<>(messages, pageable, total);
    }

    /**
     * 根据会话ID和状态查询消息
     */
    public Page<ChatMessage> pageBySessionIdOrderByCreatedAtAsc(String sessionId, Long agentId, Pageable pageable) {
        String collectionName = getCollectionName(agentId);

        // 构建查询条件
        Query query = new Query(Criteria.where("sessionId").is(sessionId))
                .with(org.springframework.data.domain.Sort.by(org.springframework.data.domain.Sort.Direction.ASC, "createdAt"));

        // 计算总数
        long total = mongoTemplate.count(query, ChatMessage.class, collectionName);

        // 添加分页参数
        query.with(pageable);

        // 执行查询
        List<ChatMessage> messages = mongoTemplate.find(query, ChatMessage.class, collectionName);

        // 返回分页结果
        return new PageImpl<>(messages, pageable, total);
    }

    /**
     * 统计会话消息数量
     */
    public long countBySessionId(String sessionId, Long agentId) {
        String collectionName = getCollectionName(agentId);
        Query query = new Query(Criteria.where("sessionId").is(sessionId));
        return mongoTemplate.count(query, ChatMessage.class, collectionName);
    }

    /**
     * 查找指定状态和时间之后的消息
     */
    public List<ChatMessage> findByStatusAndCreatedAtAfter(ChatMessage.MessageStatus status, LocalDateTime time, Long agentId) {
        String collectionName = getCollectionName(agentId);
        Query query = new Query(Criteria.where("status").is(status).and("createdAt").gt(time));
        return mongoTemplate.find(query, ChatMessage.class, collectionName);
    }

    /**
     * 根据会话ID、发送者类型和创建时间查询消息
     */
    public Page<ChatMessage> findBySessionIdAndTypeAndCreatedAtBefore(
            String sessionId, String type, LocalDateTime createdAt, Long agentId, Pageable pageable) {
        String collectionName = getCollectionName(agentId);
        Query query = new Query(Criteria.where("sessionId").is(sessionId)
                .and("type").is(type)
                .and("createdAt").lt(createdAt))
                .with(pageable);

        List<ChatMessage> messages = mongoTemplate.find(query, ChatMessage.class, collectionName);
        long total = mongoTemplate.count(Query.query(Criteria.where("sessionId").is(sessionId)
                .and("type").is(type)
                .and("createdAt").lt(createdAt)), ChatMessage.class, collectionName);

        return new PageImpl<>(messages, pageable, total);
    }

    /**
     * 根据智能体ID查询消息
     */
    public Page<ChatMessage> findByAgentIdOrderByCreatedAtDesc(Long agentId, Pageable pageable) {
        String collectionName = getCollectionName(agentId);
        Query query = new Query(Criteria.where("agentId").is(agentId))
                .with(pageable)
                .with(org.springframework.data.domain.Sort.by(org.springframework.data.domain.Sort.Direction.DESC, "createdAt"));

        List<ChatMessage> messages = mongoTemplate.find(query, ChatMessage.class, collectionName);
        long total = mongoTemplate.count(Query.query(Criteria.where("agentId").is(agentId)), ChatMessage.class, collectionName);

        return new PageImpl<>(messages, pageable, total);
    }

    /**
     * 根据用户名查询消息
     */
    public Page<ChatMessage> findByUsernameOrderByCreatedAtDesc(String username, Long agentId, Pageable pageable) {
        String collectionName = getCollectionName(agentId);
        Query query = new Query(Criteria.where("username").is(username))
                .with(pageable)
                .with(org.springframework.data.domain.Sort.by(org.springframework.data.domain.Sort.Direction.DESC, "createdAt"));

        List<ChatMessage> messages = mongoTemplate.find(query, ChatMessage.class, collectionName);
        long total = mongoTemplate.count(Query.query(Criteria.where("username").is(username)), ChatMessage.class, collectionName);

        return new PageImpl<>(messages, pageable, total);
    }

    /**
     * 根据用户名和智能体ID查询消息
     */
    public Page<ChatMessage> findByUsernameAndAgentIdOrderByCreatedAtDesc(String username, Long agentId, Pageable pageable) {
        String collectionName = getCollectionName(agentId);
        Query query = new Query(Criteria.where("username").is(username).and("agentId").is(agentId))
                .with(pageable)
                .with(org.springframework.data.domain.Sort.by(org.springframework.data.domain.Sort.Direction.DESC, "createdAt"));

        List<ChatMessage> messages = mongoTemplate.find(query, ChatMessage.class, collectionName);
        long total = mongoTemplate.count(Query.query(Criteria.where("username").is(username).and("agentId").is(agentId)), ChatMessage.class, collectionName);

        return new PageImpl<>(messages, pageable, total);
    }

    /**
     * 查询指定会话中创建时间大于等于指定时间的消息
     */
    public List<ChatMessage> findBySessionIdAndCreatedAtGreaterThanEqual(String sessionId, LocalDateTime createdAt, Long agentId) {
        String collectionName = getCollectionName(agentId);
        Query query = new Query(Criteria.where("sessionId").is(sessionId).and("createdAt").gte(createdAt));
        return mongoTemplate.find(query, ChatMessage.class, collectionName);
    }

    /**
     * 查询指定会话中创建时间大于指定时间的消息
     */
    public List<ChatMessage> findBySessionIdAndCreatedAtGreaterThan(String sessionId, LocalDateTime createdAt, Long agentId) {
        String collectionName = getCollectionName(agentId);
        Query query = new Query(Criteria.where("sessionId").is(sessionId).and("createdAt").gt(createdAt));
        return mongoTemplate.find(query, ChatMessage.class, collectionName);
    }

    /**
     * 直接删除指定会话中创建时间大于等于指定时间的消息
     */
    public void deleteBySessionIdAndCreatedAtGreaterThanEqual(String sessionId, LocalDateTime createdAt, Long agentId) {
        String collectionName = getCollectionName(agentId);
        Query query = new Query(Criteria.where("sessionId").is(sessionId).and("createdAt").gte(createdAt));
        mongoTemplate.remove(query, ChatMessage.class, collectionName);
    }

    /**
     * 直接删除指定会话中创建时间大于指定时间的消息
     */
    public void deleteBySessionIdAndCreatedAtGreaterThan(String sessionId, LocalDateTime createdAt, Long agentId) {
        String collectionName = getCollectionName(agentId);
        Query query = new Query(Criteria.where("sessionId").is(sessionId).and("createdAt").gt(createdAt));
        mongoTemplate.remove(query, ChatMessage.class, collectionName);
    }

    /**
     * 删除消息
     */
    public void delete(ChatMessage chatMessage) {
        String collectionName = getCollectionName(chatMessage.getAgentId());
        mongoTemplate.remove(chatMessage, collectionName);
    }

    /**
     * 根据ID删除消息
     */
    public void deleteById(String id, Long agentId) {
        String collectionName = getCollectionName(agentId);
        Query query = new Query(Criteria.where("id").is(id));
        mongoTemplate.remove(query, ChatMessage.class, collectionName);
    }
}