package com.gw.chat.utils;

import lombok.extern.log4j.Log4j2;

@Log4j2
public class ChatUtils {
    public static String removeBracketContent(String content) {
        int maxIterations = 10; // 限制最大迭代次数，防止死循环
        int count = 0;
        // 处理中英文括号混合的情况
        content = content.replace('（', '('); // 将中文左括号替换为英文左括号
        content = content.replace('）', ')'); // 将中文右括号替换为英文右括号
        content = content.replace('<', '('); // 将中文左括号替换为英文左括号
        content = content.replace('>', ')'); // 将中文右括号替换为英文右括号
        content = content.replace('《', '('); // 将中文左括号替换为英文左括号
        content = content.replace('》', ')'); // 将中文右括号替换为英文右括号
        // 处理所有括号及其内容
        while ((content.contains("(") || content.contains(")")) && count < maxIterations) {
            String before = content;
            // 先处理完整的括号对
            content = content.replaceAll("\\([^()]*\\)", "");
            // 处理可能存在的不成对括号
            content = content.replaceAll("\\([^()]*$", ""); // 处理左括号后面没有右括号的情况
            content = content.replaceAll("^[^()]*\\)", ""); // 处理右括号前面没有左括号的情况
            // 如果内容没有变化，说明可能存在嵌套括号或其他特殊情况，跳出循环
            if (before.equals(content)) {
                break;
            }
            count++;
        }
        log.debug("处理后的文本内容: {}，迭代次数: {}", content, count);
        return content;
    }
}
