package com.gw.chat.repository;

import com.gw.chat.entity.ChatCompressMessage;
import org.jetbrains.annotations.NotNull;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 聊天压缩消息数据访问接口
 */
@Repository
public interface ChatCompressMessageRepository extends MongoRepository<ChatCompressMessage, String> {

    /**
     * 根据ID查询压缩消息
     */
    @NotNull Optional<ChatCompressMessage> findById(@NotNull String id);

    /**
     * 根据会话ID查询压缩消息
     */
    List<ChatCompressMessage> findBySessionIdOrderByCreatedAtAsc(String sessionId);

    /**
     * 根据会话ID分页查询压缩消息
     */
    Page<ChatCompressMessage> findBySessionIdOrderByCreatedAtDesc(String sessionId, Pageable pageable);

    /**
     * 根据会话ID查询最新的压缩消息
     */
    @Query(value = "{ 'sessionId': ?0 }", sort = "{ 'createdAt': -1 }")
    List<ChatCompressMessage> findBySessionIdOrderByCreatedAtDescLimit(String sessionId, Pageable pageable);

    /**
     * 根据用户名查询压缩消息
     */
    Page<ChatCompressMessage> findByUsernameOrderByCreatedAtDesc(String username, Pageable pageable);

    /**
     * 根据智能体ID查询压缩消息
     */
    Page<ChatCompressMessage> findByAgentIdOrderByCreatedAtDesc(Long agentId, Pageable pageable);

    /**
     * 根据用户名和智能体ID查询压缩消息
     */
    Page<ChatCompressMessage> findByUsernameAndAgentIdOrderByCreatedAtDesc(String username, Long agentId, Pageable pageable);

    /**
     * 根据用户名和会话ID查询压缩消息
     */
    List<ChatCompressMessage> findByUsernameAndSessionIdOrderByCreatedAtAsc(String username, String sessionId);

    /**
     * 根据会话ID和结束序列号查询压缩消息
     */
    List<ChatCompressMessage> findBySessionIdAndEndSeqNumOrderByCreatedAtAsc(String sessionId, Long endSeqNum);

    /**
     * 根据会话ID查询压缩消息数量
     */
    long countBySessionId(String sessionId);

    /**
     * 根据用户名查询压缩消息数量
     */
    long countByUsername(String username);

    /**
     * 根据智能体ID查询压缩消息数量
     */
    long countByAgentId(Long agentId);

    /**
     * 根据压缩时间查询压缩消息
     */
    List<ChatCompressMessage> findByCompressedTimeGreaterThanOrderByCreatedAtAsc(Long compressedTime);

    /**
     * 根据会话ID和创建时间范围查询压缩消息
     */
    List<ChatCompressMessage> findBySessionIdAndCreatedAtBetweenOrderByCreatedAtAsc(
            String sessionId, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 删除指定会话的压缩消息
     */
    void deleteBySessionId(String sessionId);

    /**
     * 删除指定用户的压缩消息
     */
    void deleteByUsername(String username);

    /**
     * 分页查询未压缩或指定序列号的压缩消息
     * 查询条件：(compressedTime为null或0) 或 (endSeqNum等于指定序列号)
     */
    @Query(value = "{ 'sessionId': ?0, '$or': [ " +
            "{ '$or': [ { 'compressedTime': null }, { 'compressedTime': 0 } ] }, " +
            "{ 'endSeqNum': ?1 } " +
            "] }")
    Page<ChatCompressMessage> findBySessionIdAndCompressedTimeNullOrEndSeqNumOrderByCreatedAtDesc(
            String sessionId, Long seqNum, Pageable pageable);
}
