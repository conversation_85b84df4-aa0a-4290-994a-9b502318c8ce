package com.gw.chat.repository;

import com.gw.chat.entity.ChatMessage;
import org.jetbrains.annotations.NotNull;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 聊天消息数据访问接口
 *
 * @deprecated 推荐使用 ChatMessageService 来支持动态表名功能
 * 该接口仍然保留用于向后兼容，但新功能请使用 ChatMessageService
 */
@Repository
@Deprecated
public interface ChatMessageRepository extends MongoRepository<ChatMessage, String> {
    @NotNull Optional<ChatMessage> findById(@NotNull String id);

    /**
     * 根据会话ID查询指定数量的最新消息
     */
    @Query(value = "{ 'sessionId': ?0 }", sort = "{ 'createdAt': -1 }")
    List<ChatMessage> findBySessionIdOrderByCreatedAtDescLimit(String sessionId, Pageable pageable);

    /**
     * 根据会话ID查询消息
     */
    List<ChatMessage> findBySessionIdOrderByCreatedAtAsc(String sessionId);

    /**
     * 根据会话ID分页查询消息
     */
    Page<ChatMessage> findBySessionIdOrderByCreatedAtDesc(String sessionId, Pageable pageable);

    /**
     * 根据会话ID和状态查询消息
     */
    List<ChatMessage> findBySessionIdAndStatusOrderByCreatedAtAsc(String sessionId, ChatMessage.MessageStatus status);

    /**
     * 删除会话相关的所有消息（逻辑删除）
     */
    long countBySessionId(String sessionId);

    /**
     * 查找指定状态和时间之后的消息
     */
    List<ChatMessage> findByStatusAndCreatedAtAfter(ChatMessage.MessageStatus status, LocalDateTime time);

    /**
     * 根据会话ID、发送者类型和创建时间查询消息
     */
    Page<ChatMessage> findBySessionIdAndTypeAndCreatedAtBefore(
            String sessionId, String type,
            LocalDateTime createdAt, Pageable pageable);

    /**
     * 根据智能体ID查询消息
     */
    Page<ChatMessage> findByAgentIdOrderByCreatedAtDesc(Long agentId, Pageable pageable);

    /**
     * 根据用户名查询消息
     */
    Page<ChatMessage> findByUsernameOrderByCreatedAtDesc(String username, Pageable pageable);

    Page<ChatMessage> findByUsernameAndAgentIdOrderByCreatedAtDesc(String username, Long agentId, Pageable pageable);

    /**
     * 查询指定会话中创建时间大于等于指定时间的消息
     */
    List<ChatMessage> findBySessionIdAndCreatedAtGreaterThanEqual(String sessionId, LocalDateTime createdAt);

    /**
     * 查询指定会话中创建时间大于指定时间的消息
     */
    List<ChatMessage> findBySessionIdAndCreatedAtGreaterThan(String sessionId, LocalDateTime createdAt);

    /**
     * 直接删除指定会话中创建时间大于等于指定时间的消息
     */
    void deleteBySessionIdAndCreatedAtGreaterThanEqual(String sessionId, LocalDateTime createdAt);

    /**
     * 直接删除指定会话中创建时间大于指定时间的消息
     */
    void deleteBySessionIdAndCreatedAtGreaterThan(String sessionId, LocalDateTime createdAt);
}