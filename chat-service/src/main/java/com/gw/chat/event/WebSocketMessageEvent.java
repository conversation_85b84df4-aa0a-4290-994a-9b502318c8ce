package com.gw.chat.event;

import com.gw.chat.dto.ChatMessageDto;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * WebSocket消息事件，用于在服务间传递WebSocket消息
 */
@Getter
public class WebSocketMessageEvent extends ApplicationEvent {

    private final String username;
    private final String sessionId;
    private final String content;
    private final ChatMessageDto message;
    private final MessageType messageType;

    /**
     * 创建用户消息事件
     */
    public WebSocketMessageEvent(Object source, String username, ChatMessageDto message, MessageType messageType) {
        super(source);
        this.username = username;
        this.message = message;
        this.sessionId = message != null ? message.getSessionId() : null;
        this.content = null;
        this.messageType = messageType;
    }

    /**
     * 创建系统消息事件
     */
    public WebSocketMessageEvent(Object source, String username, String sessionId, String content) {
        super(source);
        this.username = username;
        this.message = null;
        this.sessionId = sessionId;
        this.content = content;
        this.messageType = MessageType.SYSTEM;
    }

    /**
     * 判断是否为系统消息
     */
    public boolean isSystemMessage() {
        return messageType == MessageType.SYSTEM;
    }

    /**
     * 判断是否为用户消息
     */
    public boolean isUserMessage() {
        return messageType == MessageType.USER;
    }

    public enum MessageType {
        USER,
        SYSTEM
    }
}