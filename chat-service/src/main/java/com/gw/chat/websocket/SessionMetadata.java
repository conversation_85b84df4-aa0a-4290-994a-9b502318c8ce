package com.gw.chat.websocket;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.web.reactive.socket.WebSocketSession;
import reactor.core.Disposable;
import reactor.core.publisher.Sinks;

import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.locks.StampedLock;

/**
 * WebSocket会话元数据
 * 包含会话相关的所有资源和状态信息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Log4j2
public class SessionMetadata {

    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * 用户名
     */
    private String username;
    private Long headAgentId;
    /**
     * WebSocket会话
     */
    private WebSocketSession session;

    /**
     * 消息Sink
     */
    private Sinks.Many<String> messageSink;

    /**
     * 消息订阅
     */
    private Disposable subscription;

    /**
     * 会话活跃状态
     */
    private AtomicBoolean active;

    /**
     * 最后活跃时间
     */
    private volatile long lastActiveTime;

    /**
     * 会话锁（使用StampedLock提供更好的性能）
     */
    private StampedLock sessionLock;

    /**
     * 创建时间
     */
    private long createTime;

    /**
     * 消息计数
     */
    private volatile long messageCount;

    /**
     * 连接来源IP
     */
    private String remoteAddress;

    /**
     * 用户代理信息
     */
    private String userAgent;

    /**
     * 连接时的请求头信息
     */
    private Map<String, String> headers;

    /**
     * 会话状态：CONNECTING, CONNECTED, DISCONNECTING, DISCONNECTED
     */
    private volatile String status;

    /**
     * 最后一次心跳时间
     */
    private volatile long lastHeartbeatTime;

    /**
     * 心跳失败次数
     * -- SETTER --
     *  设置心跳失败计数

     */
    private volatile int heartbeatFailureCount;

    /**
     * 错误计数
     */
    private volatile int errorCount;

    /**
     * 最后一次错误信息
     */
    private volatile String lastError;

    /**
     * 会话标签（用于分类管理）
     */
    private Set<String> tags;

    /**
     * 扩展属性
     */
    private Map<String, Object> attributes;

    /**
     * 当前发送状态：IDLE(空闲), SENDING(发送中), COMPLETED(已完成), TIMEOUT(超时)
     */
    private volatile String sendingStatus;

    /**
     * 发送开始时间
     */
    private volatile long sendingStartTime;

    /**
     * 发送超时时间（毫秒）
     */
    private volatile long sendingTimeoutMs;
    public boolean checkSessionValid(Long agentId) {
        if(headAgentId == null){
            return true;
        }
        return agentId.equals(this.headAgentId);
    }
    /**
     * 创建新的会话元数据
     */
    public static SessionMetadata create(String username, WebSocketSession session) {
        long currentTime = System.currentTimeMillis();

        // 提取连接信息
        String remoteAddress = extractRemoteAddress(session);
        String userAgent = extractUserAgent(session);
        Map<String, String> headers = extractHeaders(session);

        return SessionMetadata.builder()
                .sessionId(session.getId())
                .username(username)
                .session(session)
                .active(new AtomicBoolean(true))
                .lastActiveTime(currentTime)
                .createTime(currentTime)
                .sessionLock(new StampedLock())
                .messageCount(0L)
                .remoteAddress(remoteAddress)
                .userAgent(userAgent)
                .headers(headers)
                .status("CONNECTED")
                .lastHeartbeatTime(currentTime)
                .heartbeatFailureCount(0)
                .errorCount(0)
                .tags(ConcurrentHashMap.newKeySet())
                .attributes(new ConcurrentHashMap<>())
                .sendingStatus("IDLE")
                .sendingStartTime(0L)
                .sendingTimeoutMs(10000L) // 默认10秒超时
                .build();
    }

    private static String extractRemoteAddress(WebSocketSession session) {
        try {
            return session.getHandshakeInfo().getRemoteAddress() != null ?
                    session.getHandshakeInfo().getRemoteAddress().toString() : "unknown";
        } catch (Exception e) {
            return "unknown";
        }
    }

    private static String extractUserAgent(WebSocketSession session) {
        try {
            return session.getHandshakeInfo().getHeaders().getFirst("User-Agent");
        } catch (Exception e) {
            return null;
        }
    }

    private static Map<String, String> extractHeaders(WebSocketSession session) {
        try {
            Map<String, String> headerMap = new ConcurrentHashMap<>();
            session.getHandshakeInfo().getHeaders().forEach((key, values) -> {
                if (values != null && !values.isEmpty()) {
                    headerMap.put(key, values.get(0));
                }
            });
            return headerMap;
        } catch (Exception e) {
            return new ConcurrentHashMap<>();
        }
    }

    /**
     * 检查会话是否有效 - 增强版本，更宽松的验证逻辑
     */
    public boolean isValid() {
        // 基本检查
        if (session == null || active == null) {
            return false;
        }

        // 检查活跃状态
        boolean isActive = active.get();

        // 检查会话状态
        boolean sessionOpen = false;
        try {
            sessionOpen = session.isOpen();
        } catch (Exception e) {
            // 如果检查会话状态时出现异常，可能是网络问题，不立即判定为无效
            // 如果会话标记为活跃，给予一定的容错时间
            if (isActive && System.currentTimeMillis() - lastActiveTime < 30000) { // 30秒容错
                return true;
            }
            return false;
        }

        // 如果会话关闭但最近有活动，可能是临时网络问题
        if (!sessionOpen && isActive) {
            long timeSinceLastActivity = System.currentTimeMillis() - lastActiveTime;
            // 给予10秒的容错时间
            if (timeSinceLastActivity < 10000) {
                return true;
            }
        }

        return sessionOpen && isActive;
    }

    /**
     * 严格检查会话是否有效 - 用于需要确保会话绝对有效的场景
     */
    public boolean isStrictlyValid() {
        return session != null && session.isOpen() &&
                active != null && active.get();
    }

    /**
     * 检查会话是否超时
     */
    public boolean isTimeout(long timeoutMs) {
        return System.currentTimeMillis() - lastActiveTime > timeoutMs;
    }

    /**
     * 更新最后活跃时间
     */
    public void updateActiveTime() {
        this.lastActiveTime = System.currentTimeMillis();
    }

    /**
     * 增加消息计数
     */
    public void incrementMessageCount() {
        this.messageCount++;
    }

    /**
     * 更新心跳时间
     */
    public void updateHeartbeatTime() {
        this.lastHeartbeatTime = System.currentTimeMillis();
        this.heartbeatFailureCount = 0; // 重置失败计数
    }

    /**
     * 增加心跳失败计数
     */
    public void incrementHeartbeatFailure() {
        this.heartbeatFailureCount++;
    }

    /**
     * 增加错误计数
     */
    public void incrementErrorCount(String error) {
        this.errorCount++;
        this.lastError = error;
    }

    /**
     * 添加标签
     */
    public void addTag(String tag) {
        if (tags != null && tag != null) {
            tags.add(tag);
        }
    }

    /**
     * 移除标签
     */
    public void removeTag(String tag) {
        if (tags != null && tag != null) {
            tags.remove(tag);
        }
    }

    // ========== 静态辅助方法 ==========

    /**
     * 设置属性
     */
    public void setAttribute(String key, Object value) {
        if (attributes != null && key != null) {
            attributes.put(key, value);
        }
    }

    /**
     * 获取属性
     */
    public Object getAttribute(String key) {
        return attributes != null ? attributes.get(key) : null;
    }

    /**
     * 清理资源
     */
    public void cleanup() {
        log.debug("🔴 [元数据清理] 开始清理会话元数据: user={}, sessionId={}", username, sessionId);

        if (subscription != null && !subscription.isDisposed()) {
            log.debug("🔴 [元数据清理] 释放订阅: user={}, sessionId={}", username, sessionId);
            subscription.dispose();
        }
        if (messageSink != null) {
            log.debug("🔴 [元数据清理] 完成消息Sink: user={}, sessionId={}", username, sessionId);
            messageSink.tryEmitComplete();
        }
        if (active != null) {
            log.debug("🔴 [元数据清理] 设置为非活跃: user={}, sessionId={}", username, sessionId);
            active.set(false);
        }
        status = "DISCONNECTED";
        log.debug("🔴 [元数据清理] 会话元数据清理完成: user={}, sessionId={}", username, sessionId);
    }

    /**
     * 标记会话为非活跃
     */
    public void markInactive() {
        if (active != null) {
            active.set(false);
        }
    }

    /**
     * 开始发送消息
     */
    public void startSending() {
        this.sendingStatus = "SENDING";
        this.sendingStartTime = System.currentTimeMillis();
    }

    /**
     * 完成发送消息
     */
    public void completeSending() {
        this.sendingStatus = "COMPLETED";
        this.sendingStartTime = 0L;
    }

    /**
     * 标记发送超时
     */
    public void markSendingTimeout() {
        this.sendingStatus = "TIMEOUT";
        this.sendingStartTime = 0L;
    }

    /**
     * 检查是否正在发送中
     */
    public boolean isSending() {
        return "SENDING".equals(this.sendingStatus);
    }

    /**
     * 检查发送是否已完成（包括正常完成、超时、空闲状态）
     */
    public boolean isSendingCompleted() {
        return "COMPLETED".equals(this.sendingStatus) || 
               "TIMEOUT".equals(this.sendingStatus) || 
               "IDLE".equals(this.sendingStatus);
    }

    /**
     * 检查发送是否超时
     */
    public boolean isSendingTimeout() {
        if (!"SENDING".equals(this.sendingStatus)) {
            return false;
        }
        return System.currentTimeMillis() - this.sendingStartTime > this.sendingTimeoutMs;
    }

    /**
     * 强制完成发送（用于超时处理）
     */
    public void forceSendingComplete() {
        if (isSendingTimeout()) {
            markSendingTimeout();
        } else {
            completeSending();
        }
    }

    /**
     * 设置发送超时时间
     */
    public void setSendingTimeoutMs(long timeoutMs) {
        this.sendingTimeoutMs = timeoutMs;
    }
}