package com.gw.chat.websocket;

import com.github.benmanes.caffeine.cache.Cache;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.StampedLock;
import java.util.function.Supplier;

/**
 * 会话锁管理器
 * 使用StampedLock提供更好的读写性能
 */
@Component
@Log4j2
public class SessionLockManager {

    private final Cache<String, StampedLock> lockCache;

    public SessionLockManager(@Qualifier("sessionLockCache") Cache<String, Object> lockCache) {
        // 类型转换，因为Caffeine的泛型限制
        this.lockCache = (Cache<String, StampedLock>) (Cache<String, ?>) lockCache;
    }

    /**
     * 获取会话锁
     */
    public StampedLock getLock(String sessionId) {
        return lockCache.get(sessionId, key -> new StampedLock());
    }

    /**
     * 移除会话锁
     */
    public void removeLock(String sessionId) {
        lockCache.invalidate(sessionId);
    }

    /**
     * 执行读操作（乐观读锁）
     */
    public <T> T executeOptimisticRead(String sessionId, Supplier<T> operation) {
        StampedLock lock = getLock(sessionId);
        long stamp = lock.tryOptimisticRead();

        T result = operation.get();

        if (!lock.validate(stamp)) {
            // 乐观读失败，升级为悲观读锁
            stamp = lock.readLock();
            try {
                result = operation.get();
            } finally {
                lock.unlockRead(stamp);
            }
        }

        return result;
    }

    /**
     * 执行读操作（悲观读锁）
     */
    public <T> T executeRead(String sessionId, Supplier<T> operation) {
        StampedLock lock = getLock(sessionId);
        long stamp = lock.readLock();
        try {
            return operation.get();
        } finally {
            lock.unlockRead(stamp);
        }
    }

    /**
     * 执行写操作（写锁）
     */
    public <T> T executeWrite(String sessionId, Supplier<T> operation) {
        StampedLock lock = getLock(sessionId);
        long stamp = lock.writeLock();
        try {
            return operation.get();
        } finally {
            lock.unlockWrite(stamp);
        }
    }

    /**
     * 执行写操作（无返回值）
     */
    public void executeWrite(String sessionId, Runnable operation) {
        StampedLock lock = getLock(sessionId);
        long stamp = lock.writeLock();
        try {
            operation.run();
        } finally {
            lock.unlockWrite(stamp);
        }
    }

    /**
     * 尝试执行写操作（带超时）
     */
    public <T> T tryExecuteWrite(String sessionId, Supplier<T> operation, long timeout, TimeUnit unit) {
        StampedLock lock = getLock(sessionId);
        long stamp;
        try {
            stamp = lock.tryWriteLock(timeout, unit);
            if (stamp == 0) {
                log.warn("获取写锁超时: {}", sessionId);
                return null;
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.warn("获取写锁被中断: {}", sessionId);
            return null;
        }

        try {
            return operation.get();
        } finally {
            lock.unlockWrite(stamp);
        }
    }

    /**
     * 尝试执行写操作（无返回值，带超时）
     */
    public boolean tryExecuteWrite(String sessionId, Runnable operation, long timeout, TimeUnit unit) {
        StampedLock lock = getLock(sessionId);
        long stamp;
        try {
            stamp = lock.tryWriteLock(timeout, unit);
            if (stamp == 0) {
                log.warn("获取写锁超时: {}", sessionId);
                return false;
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.warn("获取写锁被中断: {}", sessionId);
            return false;
        }

        try {
            operation.run();
            return true;
        } finally {
            lock.unlockWrite(stamp);
        }
    }

    /**
     * 尝试将读锁升级为写锁
     */
    public <T> T tryConvertToWriteLock(String sessionId, long readStamp, Supplier<T> operation) {
        StampedLock lock = getLock(sessionId);
        long writeStamp = lock.tryConvertToWriteLock(readStamp);

        if (writeStamp != 0) {
            // 成功升级为写锁
            try {
                return operation.get();
            } finally {
                lock.unlockWrite(writeStamp);
            }
        } else {
            // 升级失败，释放读锁并获取写锁
            lock.unlockRead(readStamp);
            return executeWrite(sessionId, operation);
        }
    }

    /**
     * 获取锁统计信息
     */
    public long getLockCacheSize() {
        return lockCache.estimatedSize();
    }

    /**
     * 清理所有锁
     */
    public void clearAllLocks() {
        lockCache.invalidateAll();
        log.info("已清理所有会话锁");
    }

    /**
     * 检查锁是否存在
     */
    public boolean hasLock(String sessionId) {
        return lockCache.getIfPresent(sessionId) != null;
    }
}