package com.gw.chat.websocket;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.benmanes.caffeine.cache.Cache;
import com.gw.chat.config.CacheProperties;
import com.gw.chat.config.ChatConfig;
import com.gw.chat.config.WebSocketCacheConfig;
import com.gw.chat.constant.ChatConstant;
import com.gw.chat.constant.ChatWSConstant;
import com.gw.chat.dto.*;
import com.gw.chat.entity.ChatMessage;
import com.gw.chat.event.WebSocketMessageEvent;
import com.gw.chat.service.ChatMessageService;
import com.gw.chat.service.ChatService;
import com.gw.chat.service.SecurityCheckService;
import com.gw.chat.task.ChatTask;
import com.gw.chat.vo.ChatMessageBaseVO;
import com.gw.common.agent.constant.AgentCommonCacheConstant;
import com.gw.common.agent.constant.AgentConstant;
import com.gw.common.agent.constant.AgentStatus;
import com.gw.common.agent.service.AgentProxyService;
import com.gw.common.agent.vo.AgentBaseVO;
import com.gw.common.membership.constant.MemberBenefitConstant;
import com.gw.common.membership.service.MembershipProxyService;
import com.gw.common.ws.WebSocketErrorCode;
import com.gw.common.ws.WebSocketPayload;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import lombok.extern.log4j.Log4j2;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.handler.TextWebSocketHandler;
import reactor.core.Disposable;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.publisher.SignalType;
import reactor.core.publisher.Sinks;
import reactor.core.scheduler.Schedulers;

import java.io.IOException;
import java.security.Principal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 优化的WebSocket处理器实现
 * 使用Caffeine缓存和StampedLock提供更好的性能和并发控制
 */
@Component
@Log4j2
public class OptimizedWebSocketChatMessageHandler extends TextWebSocketHandler {

    private final ObjectMapper objectMapper;
    private final ChatService chatService;
    private final ChatTask chatTask;
    private final AgentProxyService agentProxyService;
    private final CacheProperties cacheProperties;
    private final MembershipProxyService membershipProxyService;
    private final SecurityCheckService securityCheckService;
    private final SessionLockManager lockManager;
    // 缓存
    private final Cache<String, SessionMetadata> sessionCache;
    private final Cache<String, Long> userActiveTimeCache;
    private final Cache<String, WebSocketCacheConfig.SessionStats> sessionStatsCache;
    // 传统WebSocket会话存储
    private final Map<String, WebSocketSession> traditionalSessions = new ConcurrentHashMap<>();
    // 统计信息
    private final AtomicInteger totalSessionsCreated = new AtomicInteger(0);
    private final AtomicInteger totalSessionsCleaned = new AtomicInteger(0);
    // 使用可选依赖，避免循环依赖
    private ChatWebSocketSessionManager chatSessionManager;
    private HeartbeatManager heartbeatManager;
    // 配置参数
    @Value("${websocket.max-sessions:1000}")
    private int maxSessions;
    @Value("${websocket.session-timeout-minutes:30}")
    private int sessionTimeoutMinutes;
    @Value("${websocket.cleanup-interval-minutes:5}")
    private int cleanupIntervalMinutes;
    @Value("${websocket.lock-timeout-ms:500}")
    private long lockTimeoutMs;
    @Value("${websocket.message-timeout-seconds:60}")
    private int messageTimeoutSeconds;
    // 定时清理任务执行器
    private ScheduledExecutorService cleanupExecutor;

    // 动态计算的超时时间
    private volatile long sessionTimeoutMs;
    private ChatMessageService chatMessageService;
    private ChatConfig chatConfig;

    public OptimizedWebSocketChatMessageHandler(
            ObjectMapper objectMapper,
            ChatService chatService,
            ChatTask chatTask,
            AgentProxyService agentProxyService,
            SecurityCheckService securityCheckService,
            CacheProperties cacheProperties,
            MembershipProxyService membershipProxyService,
            SessionLockManager lockManager,
            @Qualifier("sessionCache") Cache<String, SessionMetadata> sessionCache,
            @Qualifier("userActiveTimeCache") Cache<String, Long> userActiveTimeCache,
            @Qualifier("sessionStatsCache") Cache<String, WebSocketCacheConfig.SessionStats> sessionStatsCache) {

        this.objectMapper = objectMapper;
        this.chatService = chatService;
        this.chatTask = chatTask;
        this.agentProxyService = agentProxyService;
        this.cacheProperties = cacheProperties;
        this.membershipProxyService = membershipProxyService;
        this.securityCheckService = securityCheckService;
        this.lockManager = lockManager;
        this.sessionCache = sessionCache;
        this.userActiveTimeCache = userActiveTimeCache;
        this.sessionStatsCache = sessionStatsCache;

        log.info("优化的WebSocket处理器初始化完成");
    }

    // 通过setter注入，避免循环依赖
    @Autowired(required = false)
    public void setChatSessionManager(ChatWebSocketSessionManager chatSessionManager) {
        this.chatSessionManager = chatSessionManager;
    }

    @Autowired(required = false)
    public void setHeartbeatManager(HeartbeatManager heartbeatManager) {
        this.heartbeatManager = heartbeatManager;
        if (heartbeatManager != null) {
            log.info("✅ HeartbeatManager已注入到OptimizedWebSocketChatMessageHandler");
        } else {
            log.warn("⚠️ HeartbeatManager未注入到OptimizedWebSocketChatMessageHandler");
        }
    }

    @Autowired
    public void setChatConfig(ChatConfig chatConfig) {
        this.chatConfig = chatConfig;
        log.info("✅ ChatConfig已注入到OptimizedWebSocketChatMessageHandler");
    }

    /**
     * 初始化定时清理任务
     */
    @PostConstruct
    public void initCleanupTask() {
        // 计算超时时间
        this.sessionTimeoutMs = sessionTimeoutMinutes * 60 * 1000L;

        // 创建专用的清理线程池
        cleanupExecutor = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread t = new Thread(r, "websocket-cleanup-thread");
            t.setDaemon(true);
            t.setUncaughtExceptionHandler((thread, ex) ->
                    log.error("清理线程发生未捕获异常", ex));
            return t;
        });

        // 启动定时清理任务
        cleanupExecutor.scheduleWithFixedDelay(
                this::performPeriodicCleanup,
                cleanupIntervalMinutes,
                cleanupIntervalMinutes,
                TimeUnit.MINUTES
        );

        log.info("WebSocket会话清理任务已启动，清理间隔: {} 分钟，会话超时: {} 分钟，最大会话数: {}",
                cleanupIntervalMinutes, sessionTimeoutMinutes, maxSessions);
    }

    /**
     * 销毁清理任务
     */
    @PreDestroy
    public void destroyCleanupTask() {
        log.warn("🔴 [WebSocket关闭] 开始关闭WebSocket处理器 - 应用程序正在关闭");

        if (cleanupExecutor != null && !cleanupExecutor.isShutdown()) {
            log.warn("🔴 [WebSocket关闭] 正在关闭清理线程池...");
            cleanupExecutor.shutdown();
            try {
                if (!cleanupExecutor.awaitTermination(10, TimeUnit.SECONDS)) {
                    log.warn("🔴 [WebSocket关闭] 清理线程池10秒内未关闭，强制关闭...");
                    cleanupExecutor.shutdownNow();
                    if (!cleanupExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                        log.error("🔴 [WebSocket关闭] 清理线程池无法完全关闭 - 可能存在阻塞任务");
                    }
                }
            } catch (InterruptedException e) {
                log.error("🔴 [WebSocket关闭] 等待清理线程池关闭时被中断");
                cleanupExecutor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }

        // 清理所有会话
        log.warn("🔴 [WebSocket关闭] 开始执行最终清理，当前会话数: {}", sessionCache.estimatedSize());
        performFinalCleanup();

        log.warn("🔴 [WebSocket关闭] WebSocket处理器关闭完成，总共创建会话: {}, 清理会话: {}",
                totalSessionsCreated.get(), totalSessionsCleaned.get());
    }

    /**
     * 注册响应式会话 - 参考RefactoredMultiChatWebSocketHandler的方式
     */
    public void registerReactiveSession(String username,
                                        org.springframework.web.reactive.socket.WebSocketSession session,Long agentId) {
        if (username == null || !session.isOpen()) {
            log.warn("无法注册无效的会话: {}", session != null ? session.getId() : "null");
            return;
        }

        // 检查会话数量限制
        if (sessionCache.estimatedSize() >= maxSessions) {
            log.warn("会话数量已达上限 ({}), 拒绝新会话: {}", maxSessions, username);
            sendConnectionErrorMessage(session, "服务器繁忙，请稍后重试");
            return;
        }

        try {
            // 清理用户的旧会话
            cleanupUserOldSession(username);

            // 注册新会话
            registerNewReactiveSession(username, session,agentId);

            // 获取注册后的元数据
            SessionMetadata metadata = sessionCache.getIfPresent(username);
            if (metadata != null) {
                // 建立消息发送流（如果还没有建立）
                establishMessageFlow(username, session, metadata);
            }

            totalSessionsCreated.incrementAndGet();
            log.info("✅ 响应式WebSocket连接已注册，用户: {}, 会话ID: {}, 当前会话数: {}",
                    username, session.getId(), sessionCache.estimatedSize());

            // 发送欢迎消息
            sendConnectionAckMessage(session);

        } catch (Exception e) {
            log.error("注册响应式会话时发生异常: {}", username, e);
            // 确保清理失败的注册
            forceCleanupSession(username, session.getId());
        }
    }

    /**
     * 清理用户的旧会话
     */
    private void cleanupUserOldSession(String username) {
        SessionMetadata oldMetadata = sessionCache.getIfPresent(username);
        if (oldMetadata != null) {
            String oldSessionId = oldMetadata.getSessionId();
            log.warn("🟡 [会话替换] 用户 {} 已有会话，清理旧会话: {}, 会话状态: {}, 活跃状态: {}",
                    username, oldSessionId, oldMetadata.getStatus(),
                    oldMetadata.getActive() != null ? oldMetadata.getActive().get() : "null");

            if (oldMetadata.isValid()) {
                // 发送会话替换通知
                try {
                    log.warn("🟡 [会话替换] 向旧会话发送替换通知: user={}, sessionId={}", username, oldSessionId);
                    sendReactiveMessage(oldMetadata.getSession(), ChatWSConstant.CHAT_FORM_SERVER_CMD,
                            WebSocketErrorCode.SUCCESS_CODE, "会话已在其他地方登录", new JSONObject(),oldMetadata.getHeadAgentId());
                } catch (Exception e) {
                    log.error("🟡 [会话替换] 发送会话替换通知失败: user={}, sessionId={}", username, oldSessionId, e);
                }
            }

            // 强制清理旧会话
            log.warn("🟡 [会话替换] 强制清理旧会话: user={}, sessionId={}", username, oldSessionId);
            forceCleanupSession(username, oldSessionId);
        }
    }

    /**
     * 注册新的响应式会话 - 参考RefactoredMultiChatWebSocketHandler的方式
     */
    private void registerNewReactiveSession(String username,
                                            org.springframework.web.reactive.socket.WebSocketSession session,Long agentId) {
        String sessionId = session.getId();
        long currentTime = System.currentTimeMillis();

        // 使用统一的会话管理器注册会话
        if (chatSessionManager != null) {
            chatSessionManager.addSession(username, session);

            // 从会话管理器获取已创建的元数据
            SessionMetadata metadata = chatSessionManager.getSessionMetadata(username);
            if (metadata != null) {
                // 直接使用会话管理器创建的元数据，避免重复创建消息流
                sessionCache.put(username, metadata);
                userActiveTimeCache.put(username, currentTime);
                sessionStatsCache.put(sessionId, new WebSocketCacheConfig.SessionStats(username));

                log.debug("使用会话管理器注册会话: user={}, sessionId={}", username, sessionId);
                return;
            }
        }

        // 如果会话管理器不可用，则手动创建（但不创建消息流，避免重复调用send()）
        createSessionMetadataOnly(username, session, currentTime,agentId);
    }

    /**
     * 仅创建会话元数据，不创建消息流（避免重复调用send()）
     */
    private void createSessionMetadataOnly(String username,
                                           org.springframework.web.reactive.socket.WebSocketSession session,
                                           long currentTime,Long agentId) {
        String sessionId = session.getId();

        // 创建消息Sink - 使用replay确保消息不会丢失，并且流不会自动完成
        Sinks.Many<String> sink = Sinks.many().replay().latest();

        // 创建会话元数据
        SessionMetadata metadata = SessionMetadata.create(username, session);
        metadata.setMessageSink(sink);
        metadata.setHeadAgentId(agentId);
        // 保存会话元数据（不创建订阅，避免重复调用send()）
        sessionCache.put(username, metadata);
        userActiveTimeCache.put(username, currentTime);
        sessionStatsCache.put(sessionId, new WebSocketCacheConfig.SessionStats(username));

        log.debug("创建会话元数据（无消息流）: user={}, sessionId={}", username, sessionId);
    }

    /**
     * 建立消息发送流 - 参考RefactoredMultiChatWebSocketHandler的方式
     */
    private void establishMessageFlow(String username,
                                      org.springframework.web.reactive.socket.WebSocketSession session,
                                      SessionMetadata metadata) {
        if (metadata.getSubscription() != null && !metadata.getSubscription().isDisposed()) {
            log.debug("消息流已存在，跳过创建: user={}", username);
            return;
        }

        String sessionId = session.getId();
        Sinks.Many<String> sink = metadata.getMessageSink();

        if (sink == null) {
            log.warn("消息Sink不存在，无法建立消息流: user={}", username);
            return;
        }

        // 设置消息处理流 - 使用takeUntil确保流不会自动完成
        Flux<String> messageFlux = sink.asFlux()
                .doOnNext(msg -> updateSessionActiveTime(sessionId, username))
                .onBackpressureBuffer(1000)
                .onErrorContinue((error, obj) ->
                    log.warn("消息流处理异常: sessionId={}, error={}", sessionId, error.getMessage()))
                // 使用takeUntil确保流只在会话真正关闭时才结束
                .takeUntil(msg -> !metadata.getActive().get());

        // 创建订阅
        AtomicBoolean isActive = metadata.getActive();
        Disposable subscription = session.send(messageFlux.map(session::textMessage))
                .doOnSubscribe(s -> log.debug("📡 消息发送流已建立: user={}, sessionId={}", username, sessionId))
                .doFinally(signalType -> {
                    log.warn("🔴 [消息流关闭] WebSocket session {} 消息流完成: {}, user={}", sessionId, signalType, username);
                    // 只在真正的错误、取消或会话被标记为非活跃时才清理会话
                    if (isActive != null &&
                        (signalType == SignalType.ON_ERROR ||
                         signalType == SignalType.CANCEL ||
                         !isActive.get())) {
                        log.warn("🔴 [消息流关闭] 因异常/取消/非活跃调度会话清理: user={}, sessionId={}, 信号类型: {}, 活跃状态: {}, 延迟100ms",
                                username, sessionId, signalType, isActive.get());
                        scheduleSessionCleanup(username, 100);
                    } else if (signalType == SignalType.ON_COMPLETE) {
                        log.info("ℹ️ [消息流完成] WebSocket session {} 正常完成，但会话仍活跃，不清理: user={}", sessionId, username);
                    }
                })
                .subscribe(
                        null,
                        error -> {
                            log.error("🔴 [消息流错误] WebSocket session {} 发生错误, user={}", sessionId, username, error);
                            // 错误情况下才清理会话
                            if (isActive != null && isActive.get()) {
                                log.warn("🔴 [消息流错误] 因错误调度会话清理: user={}, sessionId={}, 延迟100ms", username, sessionId);
                                scheduleSessionCleanup(username, 100);
                            }
                        },
                        () -> {
                            // 正常完成时检查会话是否仍然活跃
                            if (isActive != null && isActive.get()) {
                                log.info("ℹ️ [消息流完成] WebSocket session {} 正常完成，会话仍活跃，不清理: user={}", sessionId, username);
                            } else {
                                log.warn("🔴 [消息流完成] WebSocket session {} 正常完成，会话已非活跃，调度清理: user={}", sessionId, username);
                                scheduleSessionCleanup(username, 100);
                            }
                        });

        // 保存订阅
        metadata.setSubscription(subscription);
    }

    /**
     * 发送连接确认消息 - 直接通过Sink发送，避免调用sendReactiveMessage
     */
    private void sendConnectionAckMessage(org.springframework.web.reactive.socket.WebSocketSession session) {
        try {
            String username = session.getHandshakeInfo().getHeaders().getFirst("X-User-Name");
            if (username != null) {
                SessionMetadata metadata = sessionCache.getIfPresent(username);
                if (metadata != null && metadata.getMessageSink() != null) {
                    ChatWsMsgRspDTO ackMsg = ChatWsMsgRspDTO.builder()
                            .cmd(ChatWSConstant.CONNECT_RSP_FORM_SERVER_CMD)
                            .code(WebSocketErrorCode.SUCCESS_CODE)
                            .error(WebSocketErrorCode.SUCCESS_MSG)
                            .msg(new JSONObject())
                            .build();

                    String messageJson = objectMapper.writeValueAsString(ackMsg);
                    metadata.getMessageSink().tryEmitNext(messageJson);

                    log.debug("发送连接确认消息: user={}", username);
                    return;
                }
            }

            // 如果无法通过Sink发送，记录警告
            log.warn("无法发送连接确认消息，会话元数据不可用: sessionId={}", session.getId());

        } catch (Exception e) {
            log.error("发送连接确认消息失败: sessionId={}", session.getId(), e);
        }
    }

    /**
     * 发送连接错误消息 - 直接通过WebSocket发送，因为此时还没有建立消息流
     */
    private void sendConnectionErrorMessage(org.springframework.web.reactive.socket.WebSocketSession session, String errorMsg) {
        try {
            ChatWsMsgRspDTO errorResponse = ChatWsMsgRspDTO.builder()
                    .cmd(ChatWSConstant.CONNECT_RSP_FORM_SERVER_CMD)
                    .code(WebSocketErrorCode.FAIL_CODE)
                    .error(errorMsg)
                    .msg(new JSONObject())
                    .build();

            String messageJson = objectMapper.writeValueAsString(errorResponse);

            // 直接发送单条消息，不建立持续的消息流
            session.send(Mono.just(session.textMessage(messageJson)))
                    .subscribe(
                            null,
                            error -> log.warn("发送连接错误消息失败: {}", error.getMessage()),
                            () -> log.debug("连接错误消息发送完成")
                    );

        } catch (Exception e) {
            log.error("构建连接错误消息失败: sessionId={}", session.getId(), e);
        }
    }

    /**
     * 调度会话清理
     */
    private void scheduleSessionCleanup(String username, long delayMs) {
        if (cleanupExecutor != null && !cleanupExecutor.isShutdown()) {
            log.warn("🔴 [调度清理] 调度会话清理任务: user={}, 延迟={}ms", username, delayMs);
            cleanupExecutor.schedule(() -> {
                log.warn("🔴 [执行清理] 执行调度的会话清理: user={}", username);
                unregisterReactiveSession(username);
            }, delayMs, TimeUnit.MILLISECONDS);
        } else {
            log.error("🔴 [调度清理] 无法调度清理任务，清理线程池不可用: user={}, executor状态: {}",
                    username, cleanupExecutor != null ? "shutdown" : "null");
        }
    }

    /**
     * 获取或恢复会话元数据 - 增强的会话验证和恢复机制
     */
    private SessionMetadata getOrRecoverSessionMetadata(String username,
                                                        org.springframework.web.reactive.socket.WebSocketSession session,
                                                        String sessionId,Long agentId) {
        // 首先尝试从缓存获取
        SessionMetadata metadata = sessionCache.getIfPresent(username);

        if (metadata != null) {
            // 检查会话是否真的有效
            if (isSessionReallyValid(metadata, session)) {
                return metadata;
            }

            // 尝试修复会话状态
            if (tryRepairSessionMetadata(metadata, session)) {
                log.info("成功修复会话元数据: user={}, sessionId={}", username, sessionId);
                return metadata;
            }

            log.warn("会话元数据修复失败，将重新创建: user={}, sessionId={}", username, sessionId);
        }

        // 如果会话仍然有效，尝试重新创建元数据
        if (session.isOpen()) {
            return tryRecreateSessionMetadata(username, session, agentId);
        }

        return null;
    }

    /**
     * 检查会话是否真的有效 - 更宽松的验证逻辑
     */
    private boolean isSessionReallyValid(SessionMetadata metadata,
                                         org.springframework.web.reactive.socket.WebSocketSession session) {
        if (metadata == null) {
            return false;
        }

        // 基本检查
        if (metadata.getSession() == null || metadata.getActive() == null) {
            return false;
        }

        // 检查WebSocket会话是否开启
        boolean sessionOpen = session != null && session.isOpen();
        boolean metadataSessionOpen = metadata.getSession().isOpen();

        // 如果当前会话开启，但元数据中的会话关闭，可能是会话对象不同步
        if (sessionOpen && !metadataSessionOpen) {
            log.debug("检测到会话对象不同步，尝试更新: user={}", metadata.getUsername());
            return false; // 需要修复
        }

        // 检查活跃状态
        boolean isActive = metadata.getActive().get();

        // 如果会话开启且标记为活跃，认为有效
        return sessionOpen && isActive;
    }

    /**
     * 尝试修复会话元数据
     */
    private boolean tryRepairSessionMetadata(SessionMetadata metadata,
                                             org.springframework.web.reactive.socket.WebSocketSession session) {
        try {
            // 更新会话对象引用
            if (session != null && session.isOpen()) {
                metadata.setSession(session);
                metadata.getActive().set(true);
                metadata.updateActiveTime();
                metadata.setStatus("CONNECTED");

                // 重置错误计数
                metadata.setErrorCount(0);
                metadata.setLastError(null);

                log.debug("会话元数据修复成功: user={}, sessionId={}",
                         metadata.getUsername(), session.getId());
                return true;
            }
        } catch (Exception e) {
            log.warn("修复会话元数据时发生异常: user={}", metadata.getUsername(), e);
        }

        return false;
    }

    /**
     * 尝试重新创建会话元数据 - 避免重复调用send()
     */
    private SessionMetadata tryRecreateSessionMetadata(String username,
                                                       org.springframework.web.reactive.socket.WebSocketSession session,Long agentId) {
        try {
            log.info("尝试重新创建会话元数据: user={}, sessionId={}", username, session.getId());

            // 清理旧地会话数据
            cleanupUserOldSession(username);

            // 检查会话管理器是否已经处理了这个会话
            if (chatSessionManager != null) {
                SessionMetadata existingMetadata = chatSessionManager.getSessionMetadata(username);
                if (existingMetadata != null && existingMetadata.getSession() == session) {
                    // 会话管理器已经创建了元数据，直接使用
                    sessionCache.put(username, existingMetadata);
                    userActiveTimeCache.put(username, System.currentTimeMillis());
                    sessionStatsCache.put(session.getId(), new WebSocketCacheConfig.SessionStats(username));

                    log.debug("使用现有会话管理器元数据: user={}, sessionId={}", username, session.getId());
                    return existingMetadata;
                }
            }

            // 仅创建元数据，不创建消息流（避免重复调用send()）
            createSessionMetadataOnly(username, session, System.currentTimeMillis(),agentId);

            // 返回新创建的元数据
            SessionMetadata metadata = sessionCache.getIfPresent(username);
            if (metadata != null) {
                log.debug("重新创建会话元数据成功: user={}, sessionId={}", username, session.getId());
            }

            return metadata;

        } catch (Exception e) {
            log.error("重新创建会话元数据失败: user={}, sessionId={}", username, session.getId(), e);
            return null;
        }
    }

    /**
     * 强制清理会话
     */
    private void forceCleanupSession(String username, String sessionId) {
        try {
            log.warn("🔴 [强制清理] 开始强制清理会话: user={}, sessionId={}", username, sessionId);

            // 移除用户会话映射
            SessionMetadata metadata = sessionCache.getIfPresent(username);
            if (metadata != null && metadata.getSessionId().equals(sessionId)) {
                log.warn("🔴 [强制清理] 从会话缓存中移除: user={}, sessionId={}", username, sessionId);
                sessionCache.invalidate(username);
            }

            // 清理会话资源
            if (sessionId != null) {
                // 清理锁
                log.debug("🔴 [强制清理] 清理会话锁: sessionId={}", sessionId);
                lockManager.removeLock(sessionId);

                // 清理统计信息
                log.debug("🔴 [强制清理] 清理统计信息: sessionId={}", sessionId);
                sessionStatsCache.invalidate(sessionId);

                // 清理活跃时间
                log.debug("🔴 [强制清理] 清理活跃时间: user={}", username);
                userActiveTimeCache.invalidate(username);
            }

            totalSessionsCleaned.incrementAndGet();
            log.warn("🔴 [强制清理] 强制清理会话完成: user={}, sessionId={}, 总清理数: {}",
                    username, sessionId, totalSessionsCleaned.get());
        } catch (Exception e) {
            log.error("🔴 [强制清理] 强制清理会话时发生异常: user={}, sessionId={}", username, sessionId, e);
        }
    }

    /**
     * 注销响应式会话
     */
    public void unregisterReactiveSession(String userId) {
        if (userId == null) {
            log.warn("🔴 [会话注销] 尝试注销空用户ID的会话");
            return;
        }

        try {
            log.warn("🔴 [会话注销] 开始注销响应式会话: user={}", userId);

            // 使用统一的会话管理器移除会话
            if (chatSessionManager != null) {
                log.debug("🔴 [会话注销] 从会话管理器中移除: user={}", userId);
                chatSessionManager.removeSession(userId);
            }

            // 移除心跳记录
            if (heartbeatManager != null) {
                log.debug("🔴 [会话注销] 移除心跳记录: user={}", userId);
                heartbeatManager.removeHeartbeatRecord(userId);
            }

            SessionMetadata metadata = sessionCache.getIfPresent(userId);
            if (metadata != null) {
                String sessionId = metadata.getSessionId();
                log.warn("🔴 [会话注销] 找到会话元数据: user={}, sessionId={}, 状态={}, 活跃={}",
                        userId, sessionId, metadata.getStatus(),
                        metadata.getActive() != null ? metadata.getActive().get() : "null");

                // 清理会话资源
                log.debug("🔴 [会话注销] 清理会话元数据资源: user={}, sessionId={}", userId, sessionId);
                metadata.cleanup();

                // 从缓存中移除
                log.debug("🔴 [会话注销] 从各种缓存中移除: user={}, sessionId={}", userId, sessionId);
                sessionCache.invalidate(userId);
                userActiveTimeCache.invalidate(userId);
                sessionStatsCache.invalidate(sessionId);
                lockManager.removeLock(sessionId);

                totalSessionsCleaned.incrementAndGet();
                log.warn("🔴 [会话注销] 响应式WebSocket连接已注销，用户: {}, 会话ID: {}, 总清理数: {}",
                        userId, sessionId, totalSessionsCleaned.get());
            } else {
                log.warn("🔴 [会话注销] 未找到会话元数据: user={}", userId);
            }
        } catch (Exception e) {
            log.error("🔴 [会话注销] 注销响应式会话时发生异常: user={}", userId, e);
        }
    }

    /**
     * 定期清理任务
     */
    private void performPeriodicCleanup() {
        try {
            log.info("🔄 [定期清理] 开始执行WebSocket会话定期清理任务，当前会话数: {}", sessionCache.estimatedSize());

            long currentTime = System.currentTimeMillis();
            int cleanedCount = 0;

            // 清理超时会话
            int timeoutCleaned = cleanupTimeoutSessions(currentTime);
            cleanedCount += timeoutCleaned;
            if (timeoutCleaned > 0) {
                log.warn("🔄 [定期清理] 清理超时会话: {} 个", timeoutCleaned);
            }

            // 清理无效的传统会话
            int traditionalCleaned = cleanupInvalidTraditionalSessions();
            cleanedCount += traditionalCleaned;
            if (traditionalCleaned > 0) {
                log.warn("🔄 [定期清理] 清理无效传统会话: {} 个", traditionalCleaned);
            }

            if (cleanedCount > 0) {
                log.warn("🔄 [定期清理] 定期清理任务完成，清理了 {} 个会话/资源", cleanedCount);
            }

            // 检查内存使用
            checkMemoryUsage();

            // 记录统计信息
            logSessionStatistics();

            // 触发缓存自动清理
            sessionCache.cleanUp();
            userActiveTimeCache.cleanUp();
            sessionStatsCache.cleanUp();

        } catch (Exception e) {
            log.error("🔄 [定期清理] 执行定期清理任务时发生错误", e);
        }
    }

    /**
     * 清理超时会话
     */
    private int cleanupTimeoutSessions(long currentTime) {
        int cleanedCount = 0;

        try {
            List<String> usersToCleanup = new ArrayList<>();

            // 检查所有用户的活跃时间
            for (Map.Entry<String, Long> entry : userActiveTimeCache.asMap().entrySet()) {
                String username = entry.getKey();
                Long lastActiveTime = entry.getValue();

                if (lastActiveTime != null && (currentTime - lastActiveTime) > sessionTimeoutMs) {
                    SessionMetadata metadata = sessionCache.getIfPresent(username);
                    if (metadata != null) {
                        long timeoutMinutes = (currentTime - lastActiveTime) / (60 * 1000);
                        log.warn("⚠️ 会话超时清理 - 用户: {}, 会话ID: {}, 超时时间: {}分钟, 心跳失败次数: {}, 会话状态: {}",
                                username, metadata.getSessionId(), timeoutMinutes,
                                metadata.getHeartbeatFailureCount(), metadata.getStatus());
                        usersToCleanup.add(username);
                    } else {
                        // 孤立的活跃时间记录
                        log.debug("清理孤立的活跃时间记录: user={}", username);
                        userActiveTimeCache.invalidate(username);
                        cleanedCount++;
                    }
                }
            }

            // 清理收集到的会话
            for (String username : usersToCleanup) {
                try {
                    log.warn("🔴 [超时清理] 清理超时用户会话: user={}", username);
                    unregisterReactiveSession(username);
                    cleanedCount++;
                } catch (Exception e) {
                    log.error("🔴 [超时清理] 清理用户 {} 的会话时发生错误", username, e);
                }
            }

        } catch (Exception e) {
            log.error("清理超时会话时发生异常", e);
        }

        return cleanedCount;
    }

    /**
     * 清理无效的传统会话
     */
    private int cleanupInvalidTraditionalSessions() {
        int cleanedCount = 0;

        try {
            // 创建快照
            Map<String, WebSocketSession> snapshot = new HashMap<>(traditionalSessions);
            List<String> usersToRemove = new ArrayList<>();

            for (Map.Entry<String, WebSocketSession> entry : snapshot.entrySet()) {
                String userId = entry.getKey();
                WebSocketSession session = entry.getValue();

                if (session == null || !session.isOpen()) {
                    usersToRemove.add(userId);
                }
            }

            for (String userId : usersToRemove) {
                traditionalSessions.remove(userId);
                cleanedCount++;
                log.warn("🔴 [传统会话清理] 清理了无效的传统WebSocket会话: user={}", userId);
            }

        } catch (Exception e) {
            log.error("清理无效传统会话时发生异常", e);
        }

        return cleanedCount;
    }

    /**
     * 检查内存使用情况
     */
    private void checkMemoryUsage() {
        try {
            Runtime runtime = Runtime.getRuntime();
            long totalMemory = runtime.totalMemory();
            long freeMemory = runtime.freeMemory();
            long usedMemory = totalMemory - freeMemory;
            long maxMemory = runtime.maxMemory();

            double usedPercentage = (double) usedMemory / maxMemory * 100;

            if (usedPercentage > 80) {
                log.warn("🔴 [内存压力] 内存使用率过高: {}%, 当前会话数: {}",
                        String.format("%.2f", usedPercentage), sessionCache.estimatedSize());

                // 如果内存使用率过高，进行激进清理
                if (usedPercentage > 90) {
                    log.error("🔴 [内存压力] 内存使用率超过90%，触发激进清理");
                    performAggressiveCleanup();
                }
            }
        } catch (Exception e) {
            log.error("检查内存使用时发生异常", e);
        }
    }

    /**
     * 激进清理 - 在内存压力大时使用
     */
    private void performAggressiveCleanup() {
        log.error("🔴 [激进清理] 执行激进清理，当前会话数: {}", sessionCache.estimatedSize());

        long currentTime = System.currentTimeMillis();
        // 将超时时间降低到10分钟
        long aggressiveTimeout = 10 * 60 * 1000L;

        List<String> usersToCleanup = new ArrayList<>();

        for (Map.Entry<String, Long> entry : userActiveTimeCache.asMap().entrySet()) {
            String username = entry.getKey();
            Long lastActiveTime = entry.getValue();

            if (lastActiveTime != null && (currentTime - lastActiveTime) > aggressiveTimeout) {
                long inactiveMinutes = (currentTime - lastActiveTime) / (60 * 1000);
                log.warn("🔴 [激进清理] 标记清理用户: user={}, 非活跃时间: {}分钟", username, inactiveMinutes);
                usersToCleanup.add(username);
            }
        }

        int cleanedCount = 0;
        for (String username : usersToCleanup) {
            try {
                log.warn("🔴 [激进清理] 清理用户会话: user={}", username);
                unregisterReactiveSession(username);
                cleanedCount++;
            } catch (Exception e) {
                log.error("🔴 [激进清理] 激进清理用户 {} 的会话时发生错误", username, e);
            }
        }

        log.error("🔴 [激进清理] 激进清理完成，清理了 {} 个会话", cleanedCount);

        // 强制垃圾回收
        log.warn("🔴 [激进清理] 触发垃圾回收");
        System.gc();
    }

    /**
     * 最终清理
     */
    private void performFinalCleanup() {
        log.error("🔴 [最终清理] 执行最终清理，当前会话数: {}", sessionCache.estimatedSize());

        try {
            // 清理所有响应式会话
            List<String> allUsers = new ArrayList<>(sessionCache.asMap().keySet());
            log.warn("🔴 [最终清理] 清理所有响应式会话，用户数: {}", allUsers.size());
            for (String username : allUsers) {
                log.debug("🔴 [最终清理] 清理用户: {}", username);
                unregisterReactiveSession(username);
            }

            // 清理所有传统会话
            int traditionalCount = traditionalSessions.size();
            if (traditionalCount > 0) {
                log.warn("🔴 [最终清理] 清理传统会话: {} 个", traditionalCount);
                traditionalSessions.clear();
            }

            // 清理缓存
            log.warn("🔴 [最终清理] 清理所有缓存");
            sessionCache.invalidateAll();
            userActiveTimeCache.invalidateAll();
            sessionStatsCache.invalidateAll();

            // 清理锁
            log.warn("🔴 [最终清理] 清理所有锁");
            lockManager.clearAllLocks();

            log.error("🔴 [最终清理] 最终清理完成");
        } catch (Exception e) {
            log.error("🔴 [最终清理] 最终清理时发生异常", e);
        }
    }

    /**
     * 更新会话最后活跃时间 - 统一的活跃时间更新方法
     * 任何WebSocket消息（聊天、心跳、ping等）都会调用此方法重置超时
     */
    private void updateSessionActiveTime(String sessionId, String username) {
        long currentTime = System.currentTimeMillis();

        if (username == null) {
            log.warn("⚠️ 用户名为空，无法更新活跃时间: sessionId={}", sessionId);
            return;
        }

        // 1. 更新用户活跃时间缓存
        userActiveTimeCache.put(username, currentTime);

        // 2. 更新会话元数据中的活跃时间
        SessionMetadata metadata = sessionCache.getIfPresent(username);
        if (metadata != null) {
            metadata.updateActiveTime();
            metadata.updateHeartbeatTime(); // 任何消息都重置心跳超时

            // 重置心跳失败次数 - 收到任何消息都表明连接活跃
            metadata.setHeartbeatFailureCount(0);

            // 确保会话处于活跃状态
            if (!metadata.getActive().get()) {
                metadata.getActive().set(true);
                metadata.setStatus("CONNECTED");
                log.debug("消息接收恢复会话活跃状态: user={}", username);
            }
        }

        // 3. 同步更新会话管理器中的活跃时间
        if (chatSessionManager != null) {
            chatSessionManager.updateUserActiveTime(username);
        }

        // 4. 更新会话统计信息
        WebSocketCacheConfig.SessionStats stats = sessionStatsCache.getIfPresent(sessionId);
        if (stats != null) {
            stats.incrementMessageCount();
            sessionStatsCache.put(sessionId, stats);
        }

        log.debug("🔄 活跃时间已更新: user={}, sessionId={}, time={}, 心跳失败计数已重置", username, sessionId, currentTime);
    }

    /**
     * 记录会话统计信息
     */
    private void logSessionStatistics() {
        long sessionCount = sessionCache.estimatedSize();
        long traditionalSessionCount = traditionalSessions.size();
        long lockCount = lockManager.getLockCacheSize();
        long activeTimeCount = userActiveTimeCache.estimatedSize();
        long statsCount = sessionStatsCache.estimatedSize();

        log.debug("WebSocket会话统计 - 响应式: {}, 传统: {}, 锁: {}, 活跃时间: {}, 统计: {}",
                sessionCount, traditionalSessionCount, lockCount, activeTimeCount, statsCount);

        // 检查资源一致性
        if (lockCount < sessionCount || activeTimeCount < sessionCount) {
            log.warn("检测到资源不一致，可能存在泄漏 - 会话: {}, 锁: {}, 活跃时间: {}",
                    sessionCount, lockCount, activeTimeCount);
        }
    }

    /**
     * 改进的消息发送方法 - 增强会话验证和恢复机制
     */
    private void sendReactiveMessage(org.springframework.web.reactive.socket.WebSocketSession session, String cmd,
                                     Integer code, String err, JSONObject msg, Long agentId) {
        if (session == null || !session.isOpen()) {
            log.error("🔴 [消息发送失败] 尝试向无效会话发送消息: session={}, isOpen={}",
                    session != null ? session.getId() : "null",
                    session != null ? session.isOpen() : "null");
            return;
        }

        String sessionId = session.getId();
        String username = session.getHandshakeInfo().getHeaders().getFirst("X-User-Name");
        if (username == null) {
            log.warn("会话没有关联用户名: {}", sessionId);
            return;
        }

        // 获取会话元数据，使用增强的验证逻辑
        SessionMetadata metadata = getOrRecoverSessionMetadata(username, session, sessionId, agentId);
        if (metadata == null) {
            log.error("🔴 [消息发送失败] 会话元数据无法恢复，跳过消息发送: sessionId={}, user={}", sessionId, username);
            return;
        }



        // 使用StampedLock进行乐观读
        boolean result = lockManager.tryExecuteWrite(sessionId, () -> {
            try {
                // 再次检查会话状态（使用宽松验证）
                if (!session.isOpen() || !metadata.isValid()) {
                    log.error("🔴 [消息发送失败] 会话在获取锁后已关闭或无效: sessionId={}, user={}, isOpen={}, isValid={}",
                            sessionId, username, session.isOpen(), metadata.isValid());
                    return;
                }

                ChatWsMsgRspDTO rsp = new ChatWsMsgRspDTO();
                rsp.setCmd(cmd);
                rsp.setCode(code);
                rsp.setError(err);
                rsp.setMsg(msg);

                String messageJson;
                try {
                    messageJson = objectMapper.writeValueAsString(rsp);
                } catch (Exception e) {
                    log.error("序列化WebSocket消息失败: {}", sessionId, e);
                    return;
                }

                // 获取当前会话的Sink
                Sinks.Many<String> sink = metadata.getMessageSink();
                if (sink == null) {
                    log.warn("🔴 [消息发送] 会话Sink不存在，尝试建立消息流: sessionId={}, user={}", sessionId, username);
                    // 尝试建立消息流
                    establishMessageFlow(username, session, metadata);
                    sink = metadata.getMessageSink();
                    if (sink == null) {
                        log.error("🔴 [消息发送失败] 无法建立消息流: sessionId={}, user={}", sessionId, username);
                        return;
                    }
                }

                // 发送消息到sink
                Sinks.EmitResult emitResult = sink.tryEmitNext(messageJson);

                if (emitResult.isFailure()) {
                    log.error("🔴 [消息发送失败] 向会话发送消息失败: sessionId={}, user={}, 结果: {}", sessionId, username, emitResult);
                    if (emitResult == Sinks.EmitResult.FAIL_TERMINATED || emitResult == Sinks.EmitResult.FAIL_CANCELLED) {
                        // 会话已终止，尝试重新建立消息流
                        log.warn("🔴 [消息流重建] 尝试重新建立消息流: user={}, sessionId={}", username, sessionId);
                        try {
                            // 重新创建Sink - 使用replay确保消息不会丢失
                            Sinks.Many<String> newSink = Sinks.many().replay().latest();
                            metadata.setMessageSink(newSink);
                            establishMessageFlow(username, session, metadata);

                            // 重新尝试发送消息
                            Sinks.EmitResult retryResult = newSink.tryEmitNext(messageJson);
                            if (retryResult.isSuccess()) {
                                log.info("🔴 [消息流重建] 重新建立消息流后发送成功: user={}, sessionId={}", username, sessionId);
                                updateSessionActiveTime(sessionId, username);
                            } else {
                                log.error("🔴 [消息流重建] 重新建立消息流后仍然发送失败: sessionId={}, user={}, 结果: {}", sessionId, username, retryResult);
                                scheduleSessionCleanup(username, 100);
                            }
                        } catch (Exception e) {
                            log.error("🔴 [消息流重建] 重新建立消息流失败: user={}, sessionId={}", username, sessionId, e);
                            scheduleSessionCleanup(username, 100);
                        }
                    }
                } else {
                    // 更新最后活动时间
                    updateSessionActiveTime(sessionId, username);
                    log.debug("WebSocket消息发送成功 - 用户: {}, 会话: {}", username, sessionId);
                }
            } catch (Exception e) {
                log.error("发送WebSocket消息时发生异常: {}", sessionId, e);
            }
        }, lockTimeoutMs, TimeUnit.MILLISECONDS);

        if (!result) {
            log.error("🔴 [消息发送失败] 获取会话锁超时，消息发送失败: sessionId={}, user={}, 超时时间: {}ms",
                    sessionId, username, lockTimeoutMs);
        }
    }

    // ========== 以下是原有方法的保留和优化 ==========

    public void handleChatMessage(org.springframework.web.reactive.socket.WebSocketSession session,
                                  ChatMessageDto messageDto) {
        String username = session.getHandshakeInfo().getHeaders().getFirst("X-User-Name");

        // 更新会话活跃时间
        updateSessionActiveTime(session.getId(), username);

        // 验证用户
        if (!validateUser(session, username)) {
            return;
        }

        // 处理会话ID为空的情况
        if (messageDto.getSessionId() == null || messageDto.getSessionId().trim().isEmpty()) {
            if (!handleSessionCreation(session, messageDto, username)) {
                return;
            }
        }else{
            ConversationSessionDto conversationSession = chatService.getSession(messageDto.getSessionId(),username);
            messageDto.setSession(conversationSession.getEntity());
        }
        String cacheKey = cacheProperties.getCacheName(AgentCommonCacheConstant.AGENT_BASE_MAP_CACHE_KEY);
        AgentBaseVO agent = agentProxyService.getAgentInfo(cacheKey, messageDto.getAgentId());
        messageDto.setUsername(username);
        setupMessageProperties(messageDto, username);
        messageDto.setAgent(agent);
        processAndSendMessage(session, messageDto, username);
    }
    public Long getAgentIdFromHeadAgentId(org.springframework.web.reactive.socket.WebSocketSession session) {
        final String agentId = session.getHandshakeInfo().getHeaders().getFirst("agent-id") != null
                ? session.getHandshakeInfo().getHeaders().getFirst("agent-id")
                : "0";
        return Long.parseLong(agentId);
    }
    private boolean validateUser(org.springframework.web.reactive.socket.WebSocketSession session, String username) {
        if (username == null || "anonymous".equals(username)) {
            sendReactiveMessage(session, ChatWSConstant.CHAT_FORM_SERVER_CMD,
                    WebSocketErrorCode.FAIL_CODE, "匿名用户必须提供发送者ID", new JSONObject(),getAgentIdFromHeadAgentId( session));
            return false;
        }
        return true;
    }

    private boolean handleSessionCreation(org.springframework.web.reactive.socket.WebSocketSession session,
                                          ChatMessageDto messageDto, String username) {
        if (messageDto.getAgentId() == null || messageDto.getAgentId() == 0) {
            sendReactiveMessage(session, ChatWSConstant.CHAT_FORM_SERVER_CMD,
                    WebSocketErrorCode.FAIL_CODE, "会话创建失败：agentId不能为空", new JSONObject(),messageDto.getAgentId());
            return false;
        }

        Long defaultAgentId = messageDto.getAgentId();
        String defaultTitle = messageDto.getContent();
        String cacheKey = cacheProperties.getCacheName(AgentCommonCacheConstant.AGENT_BASE_MAP_CACHE_KEY);

        AgentBaseVO agent = agentProxyService.getAgentInfo(cacheKey, defaultAgentId);
        if (!validateAgent(session, agent, username)) {
            return false;
        }

        return createNewSession(session, messageDto, username, defaultAgentId, defaultTitle, agent);
    }

    private boolean validateAgent(org.springframework.web.reactive.socket.WebSocketSession session,
                                  AgentBaseVO agent, String username) {
        if (agent == null || agent.getStatus() != AgentStatus.PUBLISHED.getCode()) {
            sendReactiveMessage(session, ChatWSConstant.CHAT_FORM_SERVER_CMD,
                    WebSocketErrorCode.FAIL_CODE, "无法创建会话：智能体不可用", new JSONObject(),agent.getId());
            return false;
        }
        if (agent.getShelfStatus() != AgentConstant.SHELF_ON_STATUS) {
            sendReactiveMessage(session, ChatWSConstant.CHAT_FORM_SERVER_CMD,
                    WebSocketErrorCode.AGENT_SHEFL_OFF_CODE, "无法创建会话：智能体未上架", new JSONObject(),agent.getId());
            return false;
        }

        boolean isOwner = username.equals(agent.getCreator());
        boolean isPublic = agent.getIsPublic() >= 2;

        if (!isOwner && !isPublic) {
            sendReactiveMessage(session, ChatWSConstant.CHAT_FORM_SERVER_CMD,
                    WebSocketErrorCode.AGENT_SHEFL_OFF_CODE, "无法创建会话：该智能体不是公开智能体", new JSONObject(),agent.getId());
            return false;
        }

        return true;
    }

    private boolean createNewSession(org.springframework.web.reactive.socket.WebSocketSession session,
                                     ChatMessageDto messageDto, String username, Long agentId, String title, AgentBaseVO agent) {
        try {
            ConversationSessionDto newSession = chatService.createSessionNotExist(username, agentId, title,
                    agent.getRemoteBotId());

            messageDto.setSessionId(newSession.getId());
            messageDto.setSession(newSession.getEntity());
            log.info("为用户 {} 自动创建新会话: {}", username, newSession.getId());

            sendReactiveSystemMessage(session, newSession.getId(), "已自动创建新会话");
            return true;
        } catch (Exception e) {
            log.error("为用户 {} 创建会话失败: {}", username, e.getMessage(), e);
            sendReactiveMessage(session, ChatWSConstant.CHAT_FORM_SERVER_CMD,
                    WebSocketErrorCode.FAIL_CODE, "创建会话失败: " + e.getMessage(), new JSONObject(),agentId);
            return false;
        }
    }

    private void setupMessageProperties(ChatMessageDto messageDto, String username) {
        messageDto.setSender(username);
        messageDto.setType(ChatConstant.CHAT_MSG_QUESTION_TYPE);
        messageDto.setRole(ChatConstant.CHAT_USER_ROLE);
        if (messageDto.getContentType() == null) {
            messageDto.setContentType(ChatConstant.CHAT_CONTENT_TYPE_TEXT);
        }
    }

    private void processAndSendMessage(org.springframework.web.reactive.socket.WebSocketSession session,
                                       ChatMessageDto messageDto, String username) {
        try {
            // 获取会话元数据并检查发送状态
            SessionMetadata metadata = getOrRecoverSessionMetadata(username, session, messageDto.getSessionId(), messageDto.getAgentId());
            if (metadata == null) {
                log.error("🔴 [会话元数据] 无法获取会话元数据: sessionId={}, user={}", messageDto.getSessionId(), username);
                sendReactiveSystemMessage(session, messageDto.getSessionId(), "会话状态异常，请重新连接");
                return;
            }

            // 检查并处理发送状态
            if (!checkAndHandleSendingStatus(metadata, session, messageDto.getSessionId(), username)) {
                return; // 如果检查失败，直接返回，checkAndHandleSendingStatus已经发送了相应的消息
            }

            ChatMessageDto savedUserMessage = messageDto;
            if (!messageDto.isReAnswer()) {
                savedUserMessage = chatService.saveUserMessageAndAcknowledge(messageDto);
                messageDto.setId(savedUserMessage.getId());
            }

            if (session.isOpen()) {
                String cmd = messageDto.isReAnswer() ?
                        ChatWSConstant.CHAT_RE_ANSWER_FORM_SERVER_CMD :
                        ChatWSConstant.CHAT_ACK_FORM_SERVER_CMD;

                sendReactiveMessage(session, cmd, WebSocketErrorCode.SUCCESS_CODE,
                        WebSocketErrorCode.SUCCESS_MSG,
                        JSONObject.parseObject(savedUserMessage.toString()),messageDto.getAgentId());
            } else {
                log.error("🔴 [会话关闭] 会话在处理过程中关闭: sessionId={}, user={}", session.getId(), username);
                // 确保在会话关闭时也标记发送完成
                metadata.completeSending();
                unregisterReactiveSession(username);
                return;
            }

            processAsyncResponse(session, messageDto, username);
        } catch (Exception e) {
            log.error("处理用户消息时发生错误", e);
            // 确保在异常情况下也标记发送完成
            try {
                SessionMetadata metadata = getOrRecoverSessionMetadata(username, session, messageDto.getSessionId(), messageDto.getAgentId());
                if (metadata != null) {
                    metadata.completeSending();
                }
            } catch (Exception ex) {
                log.error("标记发送完成时发生错误", ex);
            }
            
            if (session.isOpen()) {
                sendReactiveSystemMessage(session, messageDto.getSessionId(), "服务器处理消息出错: " + e.getMessage());
            }
        }
    }

    private void processAsyncResponse(org.springframework.web.reactive.socket.WebSocketSession session,
                                      ChatMessageDto messageDto, String username) {
        CompletableFuture<ChatMessageDto> future = chatTask.sendMessageAsync(messageDto, username);
        future = future.orTimeout(messageTimeoutSeconds, TimeUnit.SECONDS);

        future.whenComplete((aiMessageDto, ex) -> {
            try {
                // 获取会话元数据并标记发送完成
                SessionMetadata metadata = getOrRecoverSessionMetadata(username, session, messageDto.getSessionId(), messageDto.getAgentId());
                if (metadata != null) {
                    metadata.completeSending();
                    log.debug("✅ [发送完成] 会话消息发送完成: sessionId={}, user={}", messageDto.getSessionId(), username);
                }

                if (ex != null) {
                    handleAsyncResponseError(session, messageDto, ex);
                } else {
                    log.debug("异步AI回复已完成: {}", aiMessageDto != null ? aiMessageDto.getId() : "null");
                }
            } catch (Exception e) {
                log.error("处理异步响应完成时发生错误: sessionId={}, username={}", messageDto.getSessionId(), username, e);
            }
        });
    }

    private void handleAsyncResponseError(org.springframework.web.reactive.socket.WebSocketSession session,
                                          ChatMessageDto messageDto, Throwable ex) {
        log.error("WebSocket session {} 发生错误", session.getId(), ex);
        try {
            // 确保在错误情况下也标记发送完成
            String username = session.getHandshakeInfo().getHeaders().getFirst("X-User-Name");
            SessionMetadata metadata = getOrRecoverSessionMetadata(username, session, messageDto.getSessionId(), messageDto.getAgentId());
            if (metadata != null) {
                metadata.completeSending();
                log.debug("✅ [错误完成] 会话消息发送因错误完成: sessionId={}, user={}", messageDto.getSessionId(), username);
            }

            String errorMessage;
            if (ex instanceof TimeoutException) {
                errorMessage = String.format("处理消息超时（%d秒），请稍后重试", messageTimeoutSeconds);
                log.warn("WebSocket session {} 消息处理超时: {} 秒", session.getId(), messageTimeoutSeconds);
            } else {
                errorMessage = "处理消息时发生错误：" + ex.getMessage();
            }

            if (session.isOpen()) {
                sendReactiveSystemMessage(session, messageDto.getSessionId(), errorMessage);
            } else {
                log.error("🔴 [会话关闭] WebSocket session {} 已关闭，无法发送错误消息", session.getId());
            }
        } catch (Exception e) {
            log.error("WebSocket session {} 发送错误消息失败", session.getId(), e);
        }
    }

    private void sendReactiveSystemMessage(org.springframework.web.reactive.socket.WebSocketSession session,
                                           String sessionId, String content) {
        if (session == null || !session.isOpen()) {
            return;
        }

        ChatMessageDto systemMessage = ChatMessageDto.builder()
                .id("system-" + System.currentTimeMillis())
                .sessionId(sessionId)
                .sender("system")
                .username("system")
                .role(ChatConstant.CHAT_SYSTEM_ROLE)
                .type(ChatConstant.CHAT_SYSTEM_RESPONSE_TYPE)
                .content(content)
                .status(ChatMessage.MessageStatus.SENT)
                .build();

        sendReactiveMessage(session, ChatWSConstant.CHAT_FORM_SERVER_CMD, WebSocketErrorCode.SUCCESS_CODE,
                WebSocketErrorCode.SUCCESS_MSG, JSONObject.parseObject(systemMessage.toString()),getAgentIdFromHeadAgentId( session));
    }

    // ========== 其他原有方法的简化版本 ==========

    public void handleReactiveMessage(org.springframework.web.reactive.socket.WebSocketSession session, String payload) {
        if (session == null || !session.isOpen()) {
            return;
        }

        String sessionId = session.getId();
        String username = session.getHandshakeInfo().getHeaders().getFirst("X-User-Name");
        updateSessionActiveTime(sessionId, username);

        try {
            WebSocketPayload wsMsg = objectMapper.readValue(payload, WebSocketPayload.class);

            if (wsMsg.getCommand() == null) {
                sendReactiveMessage(session, ChatWSConstant.CHAT_FORM_SERVER_CMD,
                        WebSocketErrorCode.FAIL_CODE, "WebSocket命令不能为空", new JSONObject(),getAgentIdFromHeadAgentId( session));
                return;
            }

            switch (wsMsg.getCommand()) {
                case ChatWSConstant.CHAT_RE_ANSWER_CMD:
                    handleReAnswerCommand(session, wsMsg, username);
                    break;
                case ChatWSConstant.CHAT_FORM_CLIENT_CMD:
                    handleChatCommand(session, wsMsg, username);
                    break;
                case ChatWSConstant.PING_CMD:
                    handlePingCommand(session, username);
                    break;
                case "heartbeat_response":
                    handleHeartbeatResponse(session, username);
                    break;
                case ChatWSConstant.QUERY_LAST_MSG_CMD:
                    handleQueryLastMessageCommand(session, wsMsg, username);
                    break;
                default:
                    sendReactiveMessage(session, wsMsg.getCommand(), WebSocketErrorCode.FAIL_CODE,
                            "不支持的websocket命令", new JSONObject(),getAgentIdFromHeadAgentId( session));
                    break;
            }

        } catch (Exception e) {
            log.error("处理WebSocket消息失败", e);
            if (session.isOpen()) {
                sendReactiveSystemMessage(session, "error", "消息处理失败: " + e.getMessage());
            }
        }
    }

    private void handleReAnswerCommand(org.springframework.web.reactive.socket.WebSocketSession session,
                                       WebSocketPayload wsMsg, String username) {
        try {
            log.info("处理重新获取答案请求，用户: {}, 消息: {}", username, JSON.toJSONString(wsMsg));

            // 解析重新回答请求
            ChatReAnswerMessageDto reAnswerDto = wsMsg.getData().toJavaObject(ChatReAnswerMessageDto.class);
            if (reAnswerDto == null) {
                log.error("无法解析重新发送的消息");
                sendReactiveMessage(session, ChatWSConstant.CHAT_FORM_SERVER_CMD,
                        WebSocketErrorCode.FAIL_CODE, "无法解析重新发送的消息", new JSONObject(),getAgentIdFromHeadAgentId( session));
                return;
            }

            // 验证参数
            if (reAnswerDto.getChatId() == null || reAnswerDto.getChatId().trim().isEmpty()) {
                log.error("重新回答请求缺少chatId");
                sendReactiveMessage(session, ChatWSConstant.CHAT_FORM_SERVER_CMD,
                        WebSocketErrorCode.FAIL_CODE, "请求参数错误：chatId不能为空", new JSONObject(),reAnswerDto.getAgentId());
                return;
            }

            if (reAnswerDto.getAgentId() == null || reAnswerDto.getAgentId() <= 0) {
                log.error("重新回答请求缺少agentId");
                sendReactiveMessage(session, ChatWSConstant.CHAT_FORM_SERVER_CMD,
                        WebSocketErrorCode.FAIL_CODE, "请求参数错误：agentId不能为空", new JSONObject(),reAnswerDto.getAgentId());
                return;
            }

            // 获取AI回答消息
            ChatMessage lstMsg = chatService.getChatMessageEntityById(reAnswerDto.getChatId(), reAnswerDto.getSessionId());
            if (lstMsg == null) {
                log.error("获取聊天消息失败，请求的消息ID不存在: {}", reAnswerDto.getChatId());
                sendReactiveMessage(session, ChatWSConstant.CHAT_FORM_SERVER_CMD,
                        WebSocketErrorCode.FAIL_CODE, "获取聊天消息失败，请求的消息不存在", new JSONObject(),reAnswerDto.getAgentId());
                return;
            }

            // 验证消息归属
            if (!username.equals(lstMsg.getUsername())) {
                log.error("用户 {} 尝试重新回答不属于自己的消息: {}", username, reAnswerDto.getChatId());
                sendReactiveMessage(session, ChatWSConstant.CHAT_FORM_SERVER_CMD,
                        WebSocketErrorCode.FAIL_CODE, "无权限重新生成该消息", new JSONObject(),reAnswerDto.getAgentId());
                return;
            }

            // 检查消息是否有关联的上一条消息（用户提问）
            if (lstMsg.getLastChatId() == null || lstMsg.getLastChatId().trim().isEmpty()) {
                log.error("获取聊天消息失败，请求的消息没有关联上一条消息: {}", reAnswerDto.getChatId());
                sendReactiveMessage(session, ChatWSConstant.CHAT_FORM_SERVER_CMD,
                        WebSocketErrorCode.FAIL_CODE, "获取聊天消息失败，请求的消息没有关联上一条消息", new JSONObject(),reAnswerDto.getAgentId());
                return;
            }

            // 获取用户原始提问消息
            ChatMessage lstQueryMsg = chatService.getChatMessageEntityById(lstMsg.getLastChatId(), lstMsg.getSessionId());
            if (lstQueryMsg == null) {
                log.error("获取聊天消息失败，关联的上一条消息不存在，消息ID: {}", lstMsg.getLastChatId());
                sendReactiveMessage(session, ChatWSConstant.CHAT_FORM_SERVER_CMD,
                        WebSocketErrorCode.FAIL_CODE, "获取聊天消息失败，关联的上一条消息不存在", new JSONObject(),reAnswerDto.getAgentId());
                return;
            }

            // 验证用户提问消息也属于当前用户
            if (!username.equals(lstQueryMsg.getUsername())) {
                log.error("用户 {} 尝试重新回答不属于自己的提问消息: {}", username, lstMsg.getLastChatId());
                sendReactiveMessage(session, ChatWSConstant.CHAT_FORM_SERVER_CMD,
                        WebSocketErrorCode.FAIL_CODE, "无权限重新生成该消息", new JSONObject(),reAnswerDto.getAgentId());
                return;
            }

            // 记录智能体使用
            try {
                agentProxyService.recordAgentUsage(reAnswerDto.getAgentId(), username);
            } catch (Exception e) {
                log.warn("记录智能体使用失败: {}", e.getMessage());
            }

            // 检查用户权益
            boolean canUse = membershipProxyService.checkBenefitCanUse(username, MemberBenefitConstant.DAILY_LIMITED_CHAT_CODE);
            if (!canUse) {
                log.error("用户 {} 今日已使用完聊天次数", username);
                sendReactiveMessage(session, ChatWSConstant.CHAT_FORM_SERVER_CMD,
                        WebSocketErrorCode.BENEFIT_NOT_ALLOWED_CODE, "今日免费聊天额度已用完", new JSONObject(),reAnswerDto.getAgentId());
                return;
            }

            // 构建重新回答的消息DTO
            ChatMessageDto msgDto = ChatMessageDto.fromEntity(lstQueryMsg);
            msgDto.setReAnswer(true);
            msgDto.setLstQueryChatId(lstQueryMsg.getId());
            msgDto.setLstAnswerChatId(reAnswerDto.getChatId());
            msgDto.setAgentId(reAnswerDto.getAgentId()); // 确保使用正确的agentId

            // 验证必要字段
            if (msgDto.getSessionId() == null || msgDto.getSessionId().trim().isEmpty()) {
                log.error("消息缺少必要字段，无法查阅出session");
                sendReactiveMessage(session, ChatWSConstant.CHAT_FORM_SERVER_CMD,
                        WebSocketErrorCode.FAIL_CODE, "消息缺少必要字段", new JSONObject(),reAnswerDto.getAgentId());
                return;
            }

            // 验证会话是否属于当前用户
            try {
                ConversationSessionDto sessionDto = chatService.getSession(msgDto.getSessionId(), username);
                if (sessionDto == null) {
                    log.error("会话不存在或无权访问: {}", msgDto.getSessionId());
                    sendReactiveMessage(session, ChatWSConstant.CHAT_FORM_SERVER_CMD,
                            WebSocketErrorCode.FAIL_CODE, "会话不存在或无权访问", new JSONObject(),reAnswerDto.getAgentId());
                    return;
                }
            } catch (Exception e) {
                log.error("验证会话权限失败: {}", msgDto.getSessionId(), e);
                sendReactiveMessage(session, ChatWSConstant.CHAT_FORM_SERVER_CMD,
                        WebSocketErrorCode.FAIL_CODE, "会话验证失败", new JSONObject(),reAnswerDto.getAgentId());
                return;
            }

            // 更新会话活跃时间
            updateSessionActiveTime(session.getId(), username);

            log.info("开始处理重新回答请求，原消息ID: {}, 新消息ID: {}, 会话ID: {}",
                    reAnswerDto.getChatId(), lstQueryMsg.getId(), msgDto.getSessionId());

            // 处理重新回答的聊天消息
            handleChatMessage(session, msgDto);

        } catch (Exception e) {
            log.error("处理重新回答命令时发生异常，用户: {}", username, e);
            sendReactiveMessage(session, ChatWSConstant.CHAT_FORM_SERVER_CMD,
                    WebSocketErrorCode.FAIL_CODE, "处理重新回答请求时发生错误: " + e.getMessage(), new JSONObject(),getAgentIdFromHeadAgentId( session));
        }
    }
    private void handleQueryLastMessageCommand(org.springframework.web.reactive.socket.WebSocketSession session,
                                   WebSocketPayload wsMsg, String username){
        try{
            QueryLastMessageDTO messageDto = wsMsg.getData().toJavaObject(QueryLastMessageDTO.class);
            if (messageDto == null) {
                sendReactiveMessage(session, ChatWSConstant.RESPONSE_LAST_MSG_CMD,
                        WebSocketErrorCode.FAIL_CODE, "无法解析消息", new JSONObject(),getAgentIdFromHeadAgentId( session));
                return;
            }
            ConversationSessionDto conversationSession = chatService.getSession(messageDto.getSessionId(),username);
            if(conversationSession == null){
                sendReactiveMessage(session, ChatWSConstant.RESPONSE_LAST_MSG_CMD,
                        WebSocketErrorCode.FAIL_CODE, "会话不存在", new JSONObject(),getAgentIdFromHeadAgentId( session));
                return;
            }
            JSONObject json = new JSONObject();
            if(conversationSession.getLstSeqNum() == null || conversationSession.getLstSeqNum() == 0){
                json.put("messages", new ArrayList<>());
                sendReactiveMessage(session, ChatWSConstant.RESPONSE_LAST_MSG_CMD,
                        WebSocketErrorCode.SUCCESS_CODE, "成功", json, conversationSession.getAgentId());
                return;
            }
            List<ChatMessage> messages = chatMessageService.findAllBySeqNum(messageDto.getSessionId(),conversationSession.getAgentId(),conversationSession.getLstSeqNum() );
            List<ChatMessageBaseVO> messageBaseVOS = messages.stream().map(ChatMessageBaseVO::new).collect(Collectors.toList());

            json.put("messages",messageBaseVOS);
            sendReactiveMessage(session, ChatWSConstant.RESPONSE_LAST_MSG_CMD,
                    WebSocketErrorCode.SUCCESS_CODE, "成功", json,conversationSession.getAgentId());
        }catch (Exception e) {
            log.error("处理查同步消息命令时发生异常，用户: {}", username, e);
            sendReactiveMessage(session, ChatWSConstant.RESPONSE_LAST_MSG_CMD,
                    WebSocketErrorCode.FAIL_CODE, "处理同步 最后一条消息请求时发生错误: " + e.getMessage(), new JSONObject(),getAgentIdFromHeadAgentId( session));
        }
    }
    private void handleChatCommand(org.springframework.web.reactive.socket.WebSocketSession session,
                                   WebSocketPayload wsMsg, String username) {
        try {
            ChatMessageDto messageDto = wsMsg.getData().toJavaObject(ChatMessageDto.class);
            if (messageDto == null) {
                sendReactiveMessage(session, ChatWSConstant.CHAT_FORM_SERVER_CMD,
                        WebSocketErrorCode.FAIL_CODE, "无法解析消息", new JSONObject(),getAgentIdFromHeadAgentId( session));
                return;
            }

            // 安全检查
            if (!securityCheckService.performSecurityCheck(username, messageDto.getContent())) {
                sendReactiveMessage(session, ChatWSConstant.CHAT_FORM_SERVER_CMD,
                        WebSocketErrorCode.SECURITY_CHECK_FAIL_CODE, "消息内容含有违规词", new JSONObject(),getAgentIdFromHeadAgentId( session));
                return;
            }

            // 权益检查
            if (!membershipProxyService.checkBenefitCanUse(username, MemberBenefitConstant.DAILY_LIMITED_CHAT_CODE)) {
                sendReactiveMessage(session, ChatWSConstant.CHAT_FORM_SERVER_CMD,
                        WebSocketErrorCode.BENEFIT_NOT_ALLOWED_CODE, "今日免费聊天额度已用完", new JSONObject(),getAgentIdFromHeadAgentId( session));
                return;
            }

            agentProxyService.recordAgentUsage(messageDto.getAgentId(), username);
            handleChatMessage(session, messageDto);

        } catch (Exception e) {
            log.error("处理聊天消息时发生错误", e);
            sendReactiveMessage(session, ChatWSConstant.CHAT_FORM_SERVER_CMD, WebSocketErrorCode.FAIL_CODE,
                    "处理消息时发生错误: " + e.getMessage(), new JSONObject(),getAgentIdFromHeadAgentId( session));
        }
    }

    // ========== 传统WebSocket方法保持不变 ==========

    @Override
    public void afterConnectionEstablished(WebSocketSession session) {
        Principal principal = session.getPrincipal();
        if (principal != null) {
            String username = principal.getName();
            traditionalSessions.put(username, session);
            log.info("✅ [传统WebSocket] 连接已建立，用户: {}, sessionId: {}, 当前传统会话数: {}",
                    username, session.getId(), traditionalSessions.size());
        } else {
            log.warn("🔴 [传统WebSocket] 未经身份验证的连接尝试，sessionId: {}", session.getId());
            try {
                session.close(CloseStatus.NOT_ACCEPTABLE.withReason("未经身份验证"));
                log.warn("🔴 [传统WebSocket] 已关闭未经身份验证的会话: {}", session.getId());
            } catch (IOException e) {
                log.error("🔴 [传统WebSocket] 关闭未经身份验证的会话时出错: {}", session.getId(), e);
            }
        }
    }

    @Override
    public void afterConnectionClosed(WebSocketSession session, @NotNull CloseStatus status) {
        Principal principal = session.getPrincipal();
        if (principal != null) {
            String username = principal.getName();
            traditionalSessions.remove(username);
            log.warn("🔴 [传统WebSocket关闭] 连接已关闭，用户: {}, sessionId: {}, 关闭状态: {} ({}), 剩余传统会话数: {}",
                    username, session.getId(), status.getCode(), status.getReason(), traditionalSessions.size());
        } else {
            log.warn("🔴 [传统WebSocket关闭] 匿名会话关闭，sessionId: {}, 关闭状态: {} ({})",
                    session.getId(), status.getCode(), status.getReason());
        }
    }

    @Override
    public void handleTransportError(WebSocketSession session, Throwable exception) {
        Principal principal = session.getPrincipal();
        String username = principal != null ? principal.getName() : "anonymous";

        log.error("🔴 [传统WebSocket错误] 传输错误，用户: {}, sessionId: {}, 错误: {}",
                username, session.getId(), exception.getMessage(), exception);

        // 清理出错的会话
        if (principal != null) {
            traditionalSessions.remove(username);
            log.warn("🔴 [传统WebSocket错误] 因传输错误移除会话: user={}, sessionId={}", username, session.getId());
        }

        // 尝试关闭会话
        try {
            if (session.isOpen()) {
                session.close(CloseStatus.SERVER_ERROR.withReason("传输错误"));
                log.warn("🔴 [传统WebSocket错误] 因传输错误关闭会话: user={}, sessionId={}", username, session.getId());
            }
        } catch (IOException e) {
            log.error("🔴 [传统WebSocket错误] 关闭出错会话时发生异常: user={}, sessionId={}", username, session.getId(), e);
        }
    }

    @EventListener
    public void handleWebSocketEvent(WebSocketMessageEvent event) {
        if (event == null || event.getUsername() == null) {
            return;
        }

        // 获取会话元数据
        SessionMetadata metadata = sessionCache.getIfPresent(event.getUsername());
        org.springframework.web.reactive.socket.WebSocketSession reactiveSession =
                metadata != null ? metadata.getSession() : null;

        if (reactiveSession != null && !reactiveSession.isOpen()) {
            log.warn("🔴 [事件处理] 检测到响应式会话已关闭，注销会话: user={}, sessionId={}",
                    event.getUsername(), reactiveSession.getId());
            unregisterReactiveSession(event.getUsername());
            reactiveSession = null;
        }

        if (event.isUserMessage() && event.getMessage() != null) {
            handleUserMessageEvent(event, reactiveSession);
        } else if (event.isSystemMessage() && event.getContent() != null) {
            handleSystemMessageEvent(event, reactiveSession);
        }
    }

    private void handleUserMessageEvent(WebSocketMessageEvent event,
                                        org.springframework.web.reactive.socket.WebSocketSession reactiveSession) {
        if (reactiveSession != null) {
            final org.springframework.web.reactive.socket.WebSocketSession finalSession = reactiveSession;
            Mono.fromRunnable(() -> {
                        try {
                            String cmd = ChatWSConstant.CHAT_FORM_SERVER_CMD;
                            int code = WebSocketErrorCode.SUCCESS_CODE;
                            String error = WebSocketErrorCode.SUCCESS_MSG;

                            if (event.getMessage().getStatus() == ChatMessage.MessageStatus.BUSY) {
                                code = WebSocketErrorCode.SYSTEM_RESPONSE_TIMEOUT_CODE;
                                error = event.getMessage().getContent();
                            } else if (event.getMessage().getStatus() == ChatMessage.MessageStatus.FAILED) {
                                code = WebSocketErrorCode.FAIL_CODE;
                                error = event.getMessage().getContent();
                            }

                            sendReactiveMessage(finalSession, cmd, code, error,
                                    JSONObject.parseObject(event.getMessage().toString()),event.getMessage().getAgentId());
                        } catch (Exception e) {
                            log.error("响应式消息处理异常", e);
                            unregisterReactiveSession(event.getUsername());
                            sendMessageToUserFallback(event.getUsername(), event.getMessage());
                        }
                    })
                    .subscribeOn(Schedulers.boundedElastic())
                    .subscribe(null, throwable -> log.error("用户消息处理失败", throwable));
        } else {
            sendMessageToUserFallback(event.getUsername(), event.getMessage());
        }
    }

    private void handleSystemMessageEvent(WebSocketMessageEvent event,
                                          org.springframework.web.reactive.socket.WebSocketSession reactiveSession) {
        if (reactiveSession != null) {
            final org.springframework.web.reactive.socket.WebSocketSession finalSession = reactiveSession;
            Mono.fromRunnable(() -> {
                        try {
                            sendReactiveSystemMessage(finalSession, event.getSessionId(), event.getContent());
                        } catch (Exception e) {
                            log.error("响应式系统消息处理异常", e);
                            unregisterReactiveSession(event.getUsername());
                            sendSystemMessage(event.getUsername(), event.getSessionId(), event.getContent());
                        }
                    })
                    .subscribeOn(Schedulers.boundedElastic())
                    .subscribe(null, throwable -> log.error("系统消息处理失败", throwable));
        } else {
            sendSystemMessage(event.getUsername(), event.getSessionId(), event.getContent());
        }
    }

    private void sendMessageToUserFallback(String username, ChatMessageDto message) {
        WebSocketSession session = traditionalSessions.get(username);
        if (session != null && session.isOpen()) {
            try {
                String messageJson = objectMapper.writeValueAsString(message);
                session.sendMessage(new TextMessage(messageJson));
                log.debug("传统WebSocket消息发送成功: user={}, sessionId={}", username, session.getId());
            } catch (IOException e) {
                log.error("🔴 [传统WebSocket发送失败] 向用户发送回退消息失败: user={}, sessionId={}",
                        username, session.getId(), e);
                traditionalSessions.remove(username);
                log.warn("🔴 [传统WebSocket发送失败] 因发送失败移除传统会话: user={}, sessionId={}",
                        username, session.getId());
            }
        } else {
            log.warn("🔴 [传统WebSocket发送失败] 传统会话不可用: user={}, session存在={}, session开启={}",
                    username, session != null, session != null ? session.isOpen() : "null");
        }
    }

    public void sendMessageToUser(String username, ChatMessageDto message) {
        sendMessageToUserFallback(username, message);
    }

    public void sendSystemMessage(String userId, String sessionId, String content) {
        ChatMessageDto systemMessage = ChatMessageDto.builder()
                .id("system-" + System.currentTimeMillis())
                .sessionId(sessionId)
                .sender("system")
                .role(ChatConstant.CHAT_ASSISTANT_ROLE)
                .type(ChatConstant.CHAT_MSG_ANSWER_TYPE)
                .content(content)
                .status(ChatMessage.MessageStatus.SENT)
                .build();

        sendMessageToUser(userId, systemMessage);
    }

    /**
     * 处理Ping命令 - 增强版本
     */
    private void handlePingCommand(org.springframework.web.reactive.socket.WebSocketSession session, String username) {
        try {
            // 发送Pong响应
            sendReactiveMessage(session, ChatWSConstant.PONG_CMD, WebSocketErrorCode.SUCCESS_CODE,
                    WebSocketErrorCode.SUCCESS_MSG, new JSONObject(),getAgentIdFromHeadAgentId( session));

            // 注意：活跃时间已在handleReactiveMessage中更新，这里只需要处理心跳特定逻辑

            // 获取或恢复会话元数据
            SessionMetadata metadata = getOrRecoverSessionMetadata(username, session, session.getId(),getAgentIdFromHeadAgentId( session));
            if (metadata != null) {
                // 重置心跳失败计数
                metadata.setHeartbeatFailureCount(0);

                log.debug("Ping命令处理完成，心跳失败计数已重置: user={}", username);
            }

            log.info("💗 处理Ping命令: user={}, sessionId={}", username, session.getId());

        } catch (Exception e) {
            log.error("❌ 处理Ping命令失败: user={}", username, e);
        }
    }

    /**
     * 处理心跳响应 - 增强版本
     */
    private void handleHeartbeatResponse(org.springframework.web.reactive.socket.WebSocketSession session, String username) {
        try {
            // 记录心跳响应
            if (heartbeatManager != null) {
                heartbeatManager.recordHeartbeatResponse(username);
            }

            // 注意：活跃时间已在handleReactiveMessage中更新，这里只需要处理心跳特定逻辑

            // 获取或恢复会话元数据
            SessionMetadata metadata = getOrRecoverSessionMetadata(username, session, session.getId(),getAgentIdFromHeadAgentId( session));
            if (metadata != null) {
                // 重置心跳失败计数
                metadata.setHeartbeatFailureCount(0);

                log.debug("心跳响应处理完成，心跳失败计数已重置: user={}", username);
            }

            log.info("💗 收到心跳响应: user={}, sessionId={}", username, session.getId());

        } catch (Exception e) {
            log.error("❌ 处理心跳响应失败: user={}", username, e);
        }
    }

    /**
     * 检查并处理发送状态
     */
    private boolean checkAndHandleSendingStatus(SessionMetadata metadata, 
                                                org.springframework.web.reactive.socket.WebSocketSession session,
                                                String sessionId, String username) {
        try {
            // 设置超时时间（从配置读取，转换为毫秒）
            if (chatConfig != null && chatConfig.getSession() != null) {
                long timeoutMs = chatConfig.getSession().getSendingTimeoutSeconds() * 1000L;
                metadata.setSendingTimeoutMs(timeoutMs);
            }

            // 检查是否超时，如果超时则强制完成
            if (metadata.isSendingTimeout()) {
                log.warn("🟡 [发送超时] 会话发送超时，强制标记为完成: sessionId={}, user={}, 超时时间={}ms", 
                        sessionId, username, metadata.getSendingTimeoutMs());
                metadata.forceSendingComplete();
            }

            // 检查当前发送状态
            if (metadata.isSending()) {
                log.warn("🟡 [发送中] 上一次聊天正在发送中，拒绝新的聊天请求: sessionId={}, user={}", sessionId, username);
                sendReactiveSystemMessage(session, sessionId, "我还在思考刚才的问题呢，请稍等哦～");
                return false;
            }

            // 标记开始发送
            metadata.startSending();
            log.debug("✅ [开始发送] 会话开始发送消息: sessionId={}, user={}", sessionId, username);
            return true;

        } catch (Exception e) {
            log.error("检查发送状态时发生错误: sessionId={}, username={}", sessionId, username, e);
            // 发生错误时，允许继续发送
            return true;
        }
    }

    @Autowired
    public void setChatMessageService(ChatMessageService chatMessageService) {
        this.chatMessageService = chatMessageService;
    }
}