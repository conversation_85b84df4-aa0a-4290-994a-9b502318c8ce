package com.gw.chat.constant;

public class ChatWSConstant {
    public static final String CHAT_FORM_CLIENT_CMD = "chat_from_client"; //客户端发送消息
    public static final String CHAT_ACK_FORM_SERVER_CMD = "chat_ack_from_server"; //客户端发送消息
    public static final String CONNECT_RSP_FORM_SERVER_CMD = "connect_rsp_from_server"; //服务端发送消息
    public static final String CHAT_RE_ANSWER_CMD = "re_answer_lst_msg"; //客户端发送消息

    public static final String CHAT_FORM_SERVER_CMD = "chat_from_server"; //服务端发送消息
    public static final String CHAT_RE_ANSWER_FORM_SERVER_CMD = "chat_re_answer_from_server"; //服务端发送消息

    public static final String PING_CMD = "ping"; //客户端发送消息
    public static final String PONG_CMD = "pong"; //服务端发送消息

    public static final String QUERY_LAST_MSG_CMD = "query_last_msg_req";
    public static final String RESPONSE_LAST_MSG_CMD = "query_last_msg_rsp";

}
