package com.gw.chat.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.CompoundIndexes;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.LocalDateTime;

/**
 * 会话评论记录实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Document(collection = "conversation_comments")
@CompoundIndexes({
        @CompoundIndex(name = "commenter_agentId_idx", def = "{'commenter': 1, 'agentId': 1}"),
        @CompoundIndex(name = "parentId_idx", def = "{'parentId': 1}")
})
public class ConversationComment {

    /**
     * 评论记录ID
     */
    @Id
    private String id;

    /**
     * 智能体使用者的用户名
     */
    private String agentUser;
    /**
     * 会话ID
     */
    private String sessionId;
    /**
     * 智能体ID
     */
    private Long agentId;


    /**
     * 评论内容
     */
    private String content;

    /**
     * 评论状态 (ACTIVE: 有效, DELETED: 已删除)
     */
    private CommentStatus status;
    /**
     * 评论者用户名
     */
    private String commenter;
    /**
     * 创建时间
     */
    @CreatedDate
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @LastModifiedDate
    private LocalDateTime updatedAt;

    /**
     * 父评论ID，如果是回复其他评论则不为空
     */
    private String parentId;

    /**
     * 回复的目标评论ID
     */
    private String replyToId;

    /**
     * 回复的目标用户名
     */
    private String replyToUsername;

    /**
     * 评论深度，根评论为0，回复评论深度+1
     */
    private Integer depth;

    /**
     * 子评论数量
     */
    private Integer replyCount;

    /**
     * 评论状态枚举
     */
    public enum CommentStatus {
        ACTIVE, DELETED
    }
} 