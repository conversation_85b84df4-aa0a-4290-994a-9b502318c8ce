package com.gw.chat.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.CompoundIndexes;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Document
@CompoundIndexes({
        @CompoundIndex(name = "session_timestamp", def = "{'sessionId': 1, 'createdAt': 1}"),
        @CompoundIndex(name = "session_sequence", def = "{'sessionId': 1, 'endSeqNum': 1}"),
        @CompoundIndex(name = "user_session_timestamp", def = "{'username': 1, 'sessionId': 1, 'createdAt': 1}"),
        @CompoundIndex(name = "user_agent_timestamp", def = "{'username': 1, 'agentId': 1, 'createdAt': 1}"),
        @CompoundIndex(name = "agent_timestamp", def = "{'agentId': 1, 'createdAt': 1}"),
        @CompoundIndex(name = "compressed_time_idx", def = "{'compressedTime': 1, 'sessionId': 1}")
})
public class ChatCompressMessage {
    /**
     * 消息ID
     */
    @Id
    private String id;
    /**
     * 会话ID，关联ConversationSession
     */
    @Indexed
    private String sessionId;
    private String content;
    private Long endSeqNum = 0L;
    private Long compressedTime = 0L;
    @Indexed
    private String username;
    @Indexed
    private Long agentId;
    private String agentName;
    private LocalDateTime createdAt;
}
