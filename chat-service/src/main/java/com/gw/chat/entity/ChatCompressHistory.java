package com.gw.chat.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.CompoundIndexes;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.LocalDateTime;

/**
 * 聊天压缩历史记录实体类
 * 用于记录每次压缩操作的历史结果，便于追踪和回溯压缩过程
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Document(collection = "chat_compress_history")
@CompoundIndexes({
        @CompoundIndex(name = "session_timestamp", def = "{'sessionId': 1, 'createdAt': 1}"),
        @CompoundIndex(name = "session_sequence", def = "{'sessionId': 1, 'endSeqNum': 1}"),
        @CompoundIndex(name = "user_session_timestamp", def = "{'username': 1, 'sessionId': 1, 'createdAt': 1}"),
        @CompoundIndex(name = "user_agent_timestamp", def = "{'username': 1, 'agentId': 1, 'createdAt': 1}"),
        @CompoundIndex(name = "agent_timestamp", def = "{'agentId': 1, 'createdAt': 1}"),
        @CompoundIndex(name = "compressed_time_idx", def = "{'compressedTime': 1, 'sessionId': 1}"),
        @CompoundIndex(name = "session_compress_seq", def = "{'sessionId': 1, 'compressSequence': 1}")
})
public class ChatCompressHistory {
    
    /**
     * 历史记录ID
     */
    @Id
    private String id;
    
    /**
     * 会话ID，关联ConversationSession
     */
    @Indexed
    private String sessionId;
    
    /**
     * 压缩后的内容
     */
    private String content;
    
    /**
     * 结束序列号，表示压缩到哪个消息
     */
    private Long endSeqNum = 0L;
    
    /**
     * 压缩时间戳
     */
    private Long compressedTime = 0L;
    
    /**
     * 用户名
     */
    @Indexed
    private String username;
    
    /**
     * 智能体ID
     */
    @Indexed
    private Long agentId;
    
    /**
     * 智能体名称
     */
    private String agentName;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 压缩序列号，表示这是第几次压缩
     * 用于标识同一会话的压缩历史顺序
     */
    @Indexed
    private Long compressSequence = 1L;
    
    /**
     * 关联的当前压缩消息ID
     * 指向ChatCompressMessage表中的当前有效压缩记录
     */
    private String currentCompressMessageId;
    
    /**
     * 压缩前的消息数量
     */
    private Integer messageCount = 0;
    
    /**
     * 压缩前的内容长度
     */
    private Integer originalContentLength = 0;
    
    /**
     * 压缩后的内容长度
     */
    private Integer compressedContentLength = 0;
    
    /**
     * 压缩类型：FULL(全量压缩), INCREMENTAL(增量压缩)
     */
    private String compressType = "INCREMENTAL";
    
    /**
     * 压缩状态：SUCCESS(成功), FAILED(失败), PARTIAL(部分成功)
     */
    private String compressStatus = "SUCCESS";
    
    /**
     * 压缩耗时（毫秒）
     */
    private Long processingTimeMs = 0L;
    
    /**
     * 备注信息，记录压缩过程中的特殊情况
     */
    private String remarks;
}
