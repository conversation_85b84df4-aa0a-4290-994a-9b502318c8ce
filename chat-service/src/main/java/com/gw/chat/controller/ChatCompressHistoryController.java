package com.gw.chat.controller;

import com.gw.chat.entity.ChatCompressHistory;
import com.gw.chat.service.ChatCompressHistoryService;
import com.gw.common.dto.ResponseResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

/**
 * 聊天压缩历史记录控制器
 */
@RestController
@RequestMapping("/api/chat/compress/history")
@RequiredArgsConstructor
@Log4j2
@Tag(name = "聊天压缩历史记录", description = "聊天压缩历史记录管理接口")
public class ChatCompressHistoryController {
    
    private final ChatCompressHistoryService chatCompressHistoryService;
    
    @GetMapping("/{id}")
    @Operation(summary = "根据ID获取压缩历史记录")
    public ResponseResult<ChatCompressHistory> getById(
            @Parameter(description = "历史记录ID") @PathVariable String id) {
        try {
            Optional<ChatCompressHistory> history = chatCompressHistoryService.findById(id);
            return history.map(ResponseResult::success)
                    .orElse(ResponseResult.failure("压缩历史记录不存在"));
        } catch (Exception e) {
            log.error("获取压缩历史记录失败: {}", id, e);
            return ResponseResult.failure("获取压缩历史记录失败");
        }
    }
    
    @GetMapping("/session/{sessionId}")
    @Operation(summary = "根据会话ID获取压缩历史记录列表")
    public ResponseResult<List<ChatCompressHistory>> getBySessionId(
            @Parameter(description = "会话ID") @PathVariable String sessionId) {
        try {
            List<ChatCompressHistory> histories = chatCompressHistoryService.findBySessionId(sessionId);
            return ResponseResult.success(histories);
        } catch (Exception e) {
            log.error("获取会话压缩历史记录失败: {}", sessionId, e);
            return ResponseResult.failure("获取会话压缩历史记录失败");
        }
    }

    @GetMapping("/session/{sessionId}/page")
    @Operation(summary = "分页获取会话的压缩历史记录")
    public ResponseResult<Page<ChatCompressHistory>> getBySessionIdPage(
            @Parameter(description = "会话ID") @PathVariable String sessionId,
            @Parameter(description = "页码，从0开始") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") int size,
            @Parameter(description = "排序字段") @RequestParam(defaultValue = "compressSequence") String sortBy,
            @Parameter(description = "排序方向") @RequestParam(defaultValue = "desc") String sortDir) {
        try {
            Sort.Direction direction = "asc".equalsIgnoreCase(sortDir) ? Sort.Direction.ASC : Sort.Direction.DESC;
            Pageable pageable = PageRequest.of(page, size, Sort.by(direction, sortBy));
            
            Page<ChatCompressHistory> histories = chatCompressHistoryService.findBySessionId(sessionId, pageable);
            return ResponseResult.success(histories);
        } catch (Exception e) {
            log.error("分页获取会话压缩历史记录失败: {}", sessionId, e);
            return ResponseResult.failure("分页获取会话压缩历史记录失败");
        }
    }

    @GetMapping("/user/{username}/page")
    @Operation(summary = "分页获取用户的压缩历史记录")
    public ResponseResult<Page<ChatCompressHistory>> getByUsernamePage(
            @Parameter(description = "用户名") @PathVariable String username,
            @Parameter(description = "页码，从0开始") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") int size,
            @Parameter(description = "排序字段") @RequestParam(defaultValue = "createdAt") String sortBy,
            @Parameter(description = "排序方向") @RequestParam(defaultValue = "desc") String sortDir) {
        try {
            Sort.Direction direction = "asc".equalsIgnoreCase(sortDir) ? Sort.Direction.ASC : Sort.Direction.DESC;
            Pageable pageable = PageRequest.of(page, size, Sort.by(direction, sortBy));
            
            Page<ChatCompressHistory> histories = chatCompressHistoryService.findByUsername(username, pageable);
            return ResponseResult.success(histories);
        } catch (Exception e) {
            log.error("分页获取用户压缩历史记录失败: {}", username, e);
            return ResponseResult.failure("分页获取用户压缩历史记录失败");
        }
    }

    @GetMapping("/agent/{agentId}/page")
    @Operation(summary = "分页获取智能体的压缩历史记录")
    public ResponseResult<Page<ChatCompressHistory>> getByAgentIdPage(
            @Parameter(description = "智能体ID") @PathVariable Long agentId,
            @Parameter(description = "页码，从0开始") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") int size,
            @Parameter(description = "排序字段") @RequestParam(defaultValue = "createdAt") String sortBy,
            @Parameter(description = "排序方向") @RequestParam(defaultValue = "desc") String sortDir) {
        try {
            Sort.Direction direction = "asc".equalsIgnoreCase(sortDir) ? Sort.Direction.ASC : Sort.Direction.DESC;
            Pageable pageable = PageRequest.of(page, size, Sort.by(direction, sortBy));
            
            Page<ChatCompressHistory> histories = chatCompressHistoryService.findByAgentId(agentId, pageable);
            return ResponseResult.success(histories);
        } catch (Exception e) {
            log.error("分页获取智能体压缩历史记录失败: {}", agentId, e);
            return ResponseResult.failure("分页获取智能体压缩历史记录失败");
        }
    }

    @GetMapping("/session/{sessionId}/sequence/{sequence}")
    @Operation(summary = "根据会话ID和压缩序列号获取历史记录")
    public ResponseResult<ChatCompressHistory> getBySessionIdAndSequence(
            @Parameter(description = "会话ID") @PathVariable String sessionId,
            @Parameter(description = "压缩序列号") @PathVariable Long sequence) {
        try {
            Optional<ChatCompressHistory> history = chatCompressHistoryService
                    .findBySessionIdAndCompressSequence(sessionId, sequence);
            return history.map(ResponseResult::success)
                    .orElse(ResponseResult.failure("压缩历史记录不存在"));
        } catch (Exception e) {
            log.error("获取压缩历史记录失败: sessionId={}, sequence={}", sessionId, sequence, e);
            return ResponseResult.failure("获取压缩历史记录失败");
        }
    }

    @GetMapping("/session/{sessionId}/latest-sequence")
    @Operation(summary = "获取会话的最新压缩序列号")
    public ResponseResult<Long> getLatestSequence(
            @Parameter(description = "会话ID") @PathVariable String sessionId) {
        try {
            Long latestSequence = chatCompressHistoryService.getLatestCompressSequence(sessionId);
            return ResponseResult.success(latestSequence);
        } catch (Exception e) {
            log.error("获取最新压缩序列号失败: {}", sessionId, e);
            return ResponseResult.failure("获取最新压缩序列号失败");
        }
    }

    @GetMapping("/session/{sessionId}/count")
    @Operation(summary = "统计会话的压缩历史记录数量")
    public ResponseResult<Long> countBySessionId(
            @Parameter(description = "会话ID") @PathVariable String sessionId) {
        try {
            long count = chatCompressHistoryService.countBySessionId(sessionId);
            return ResponseResult.success(count);
        } catch (Exception e) {
            log.error("统计会话压缩历史记录数量失败: {}", sessionId, e);
            return ResponseResult.failure("统计会话压缩历史记录数量失败");
        }
    }

    @DeleteMapping("/cleanup")
    @Operation(summary = "清理指定时间之前的历史记录")
    public ResponseResult<Long> cleanupHistory(
            @Parameter(description = "清理时间戳（毫秒）") @RequestParam Long beforeTime) {
        try {
            long deletedCount = chatCompressHistoryService.deleteHistoryBefore(beforeTime);
            return ResponseResult.success(deletedCount);
        } catch (Exception e) {
            log.error("清理压缩历史记录失败: beforeTime={}", beforeTime, e);
            return ResponseResult.failure("清理压缩历史记录失败");
        }
    }
}
