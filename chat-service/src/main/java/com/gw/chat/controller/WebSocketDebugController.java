package com.gw.chat.controller;

import com.gw.chat.websocket.ChatWebSocketSessionManager;
import com.gw.chat.websocket.HeartbeatManager;
import com.gw.chat.websocket.OptimizedWebSocketChatMessageHandler;
import com.gw.chat.websocket.SessionMetadata;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * WebSocket调试控制器
 * 用于调试WebSocket连接状态、心跳状态等
 */
@RestController
@RequestMapping("/api/websocket/debug")
@Log4j2
public class WebSocketDebugController {

    @Autowired(required = false)
    private ChatWebSocketSessionManager sessionManager;

    @Autowired(required = false)
    private HeartbeatManager heartbeatManager;

    @Autowired(required = false)
    private OptimizedWebSocketChatMessageHandler messageHandler;

    /**
     * 获取WebSocket会话状态
     */
    @GetMapping("/sessions")
    public Map<String, Object> getSessionStatus() {
        Map<String, Object> result = new HashMap<>();
        
        if (sessionManager != null) {
            Map<String, SessionMetadata> activeSessions = sessionManager.getAllActiveSessions();
            result.put("activeSessionCount", activeSessions.size());
            result.put("activeSessions", activeSessions.keySet());
            
            // 获取每个会话的详细信息
            Map<String, Map<String, Object>> sessionDetails = new HashMap<>();
            for (Map.Entry<String, SessionMetadata> entry : activeSessions.entrySet()) {
                String username = entry.getKey();
                SessionMetadata metadata = entry.getValue();
                
                Map<String, Object> details = new HashMap<>();
                details.put("sessionId", metadata.getSessionId());
                details.put("isValid", metadata.isValid());
                details.put("isStrictlyValid", metadata.isStrictlyValid());
                details.put("isActive", metadata.getActive() != null ? metadata.getActive().get() : null);
                details.put("lastActiveTime", metadata.getLastActiveTime());
                details.put("timeSinceLastActivity", System.currentTimeMillis() - metadata.getLastActiveTime());
                details.put("heartbeatFailureCount", metadata.getHeartbeatFailureCount());
                details.put("status", metadata.getStatus());
                details.put("messageCount", metadata.getMessageCount());
                
                try {
                    details.put("sessionOpen", metadata.getSession() != null ? metadata.getSession().isOpen() : null);
                } catch (Exception e) {
                    details.put("sessionOpen", "ERROR: " + e.getMessage());
                }
                
                sessionDetails.put(username, details);
            }
            result.put("sessionDetails", sessionDetails);
        } else {
            result.put("error", "SessionManager not available");
        }
        
        return result;
    }

    /**
     * 获取心跳状态
     */
    @GetMapping("/heartbeat")
    public Map<String, Object> getHeartbeatStatus() {
        Map<String, Object> result = new HashMap<>();
        
        if (heartbeatManager != null) {
            result.put("heartbeatManagerAvailable", true);
            result.put("heartbeatStats", heartbeatManager.getHeartbeatStats());
        } else {
            result.put("heartbeatManagerAvailable", false);
            result.put("error", "HeartbeatManager not available");
        }
        
        return result;
    }

    /**
     * 获取特定用户的会话信息
     */
    @GetMapping("/session")
    public Map<String, Object> getUserSession(@RequestParam String username) {
        Map<String, Object> result = new HashMap<>();
        
        if (sessionManager != null) {
            SessionMetadata metadata = sessionManager.getSessionMetadata(username);
            if (metadata != null) {
                result.put("found", true);
                result.put("sessionId", metadata.getSessionId());
                result.put("isValid", metadata.isValid());
                result.put("isStrictlyValid", metadata.isStrictlyValid());
                result.put("isActive", metadata.getActive() != null ? metadata.getActive().get() : null);
                result.put("lastActiveTime", metadata.getLastActiveTime());
                result.put("timeSinceLastActivity", System.currentTimeMillis() - metadata.getLastActiveTime());
                result.put("heartbeatFailureCount", metadata.getHeartbeatFailureCount());
                result.put("status", metadata.getStatus());
                result.put("messageCount", metadata.getMessageCount());
                result.put("createTime", metadata.getCreateTime());
                result.put("lastHeartbeatTime", metadata.getLastHeartbeatTime());
                
                try {
                    result.put("sessionOpen", metadata.getSession() != null ? metadata.getSession().isOpen() : null);
                } catch (Exception e) {
                    result.put("sessionOpen", "ERROR: " + e.getMessage());
                }
                
                // 检查消息Sink状态
                if (metadata.getMessageSink() != null) {
                    result.put("messageSinkAvailable", true);
                    try {
                        result.put("messageSinkTerminated", metadata.getMessageSink().currentSubscriberCount());
                    } catch (Exception e) {
                        result.put("messageSinkError", e.getMessage());
                    }
                } else {
                    result.put("messageSinkAvailable", false);
                }
                
                // 检查订阅状态
                if (metadata.getSubscription() != null) {
                    result.put("subscriptionAvailable", true);
                    result.put("subscriptionDisposed", metadata.getSubscription().isDisposed());
                } else {
                    result.put("subscriptionAvailable", false);
                }
                
            } else {
                result.put("found", false);
                result.put("error", "Session not found for user: " + username);
            }
        } else {
            result.put("error", "SessionManager not available");
        }
        
        return result;
    }

    /**
     * 手动触发心跳发送（用于测试）
     */
    @GetMapping("/trigger-heartbeat")
    public Map<String, Object> triggerHeartbeat() {
        Map<String, Object> result = new HashMap<>();
        
        if (heartbeatManager != null) {
            try {
                // 通过反射调用私有方法（仅用于调试）
                java.lang.reflect.Method method = HeartbeatManager.class.getDeclaredMethod("sendHeartbeatToAllSessions");
                method.setAccessible(true);
                method.invoke(heartbeatManager);
                
                result.put("success", true);
                result.put("message", "心跳发送已触发");
            } catch (Exception e) {
                result.put("success", false);
                result.put("error", "触发心跳失败: " + e.getMessage());
                log.error("手动触发心跳失败", e);
            }
        } else {
            result.put("success", false);
            result.put("error", "HeartbeatManager not available");
        }
        
        return result;
    }

    /**
     * 获取组件状态
     */
    @GetMapping("/components")
    public Map<String, Object> getComponentStatus() {
        Map<String, Object> result = new HashMap<>();
        
        result.put("sessionManagerAvailable", sessionManager != null);
        result.put("heartbeatManagerAvailable", heartbeatManager != null);
        result.put("messageHandlerAvailable", messageHandler != null);
        
        if (sessionManager != null) {
            result.put("sessionManagerClass", sessionManager.getClass().getSimpleName());
        }
        
        if (heartbeatManager != null) {
            result.put("heartbeatManagerClass", heartbeatManager.getClass().getSimpleName());
        }
        
        if (messageHandler != null) {
            result.put("messageHandlerClass", messageHandler.getClass().getSimpleName());
        }
        
        return result;
    }
}
