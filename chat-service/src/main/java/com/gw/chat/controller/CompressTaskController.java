package com.gw.chat.controller;

import com.gw.chat.task.CompressTask;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * 压缩任务监控控制器
 * 提供压缩任务的性能监控和管理接口
 */
@RestController
@RequestMapping("/api/compress-task")
@RequiredArgsConstructor
@Log4j2
public class CompressTaskController {

    private final CompressTask compressTask;

    /**
     * 获取压缩任务性能统计
     */
    @GetMapping("/stats")
    public ResponseEntity<Map<String, Object>> getStats() {
        try {
            Map<String, Object> stats = new HashMap<>();
            stats.put("performance", compressTask.getPerformanceStats());
            stats.put("healthy", compressTask.isHealthy());
            stats.put("timestamp", System.currentTimeMillis());
            
            return ResponseEntity.ok(stats);
        } catch (Exception e) {
            log.error("获取压缩任务统计信息失败", e);
            return ResponseEntity.internalServerError()
                    .body(Map.of("error", "获取统计信息失败: " + e.getMessage()));
        }
    }

    /**
     * 重置性能统计
     */
    @PostMapping("/reset-stats")
    public ResponseEntity<Map<String, String>> resetStats() {
        try {
            compressTask.resetStats();
            return ResponseEntity.ok(Map.of("message", "性能统计已重置"));
        } catch (Exception e) {
            log.error("重置压缩任务统计信息失败", e);
            return ResponseEntity.internalServerError()
                    .body(Map.of("error", "重置统计信息失败: " + e.getMessage()));
        }
    }

    /**
     * 检查任务健康状态
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> checkHealth() {
        try {
            boolean healthy = compressTask.isHealthy();
            Map<String, Object> health = new HashMap<>();
            health.put("healthy", healthy);
            health.put("status", healthy ? "UP" : "DOWN");
            health.put("timestamp", System.currentTimeMillis());
            
            return ResponseEntity.ok(health);
        } catch (Exception e) {
            log.error("检查压缩任务健康状态失败", e);
            return ResponseEntity.internalServerError()
                    .body(Map.of("healthy", false, "error", e.getMessage()));
        }
    }

    /**
     * 手动触发压缩任务
     */
    @PostMapping("/trigger")
    public ResponseEntity<Map<String, String>> triggerCompression() {
        try {
            compressTask.triggerManualCompression();
            return ResponseEntity.ok(Map.of("message", "压缩任务已手动触发"));
        } catch (Exception e) {
            log.error("手动触发压缩任务失败", e);
            return ResponseEntity.internalServerError()
                    .body(Map.of("error", "触发压缩任务失败: " + e.getMessage()));
        }
    }
}
