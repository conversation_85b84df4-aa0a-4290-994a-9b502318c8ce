package com.gw.chat.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.gw.chat.entity.ChatCompressHistory;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 聊天压缩历史记录数据传输对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "聊天压缩历史记录DTO")
public class ChatCompressHistoryDto {
    
    @Schema(description = "历史记录ID")
    private String id;
    
    @Schema(description = "会话ID")
    private String sessionId;
    
    @Schema(description = "用户名")
    private String username;
    
    @Schema(description = "智能体ID")
    private Long agentId;
    
    @Schema(description = "智能体名称")
    private String agentName;
    
    @Schema(description = "压缩内容")
    private String content;
    
    @Schema(description = "结束序列号")
    private Long endSeqNum;
    
    @Schema(description = "压缩时间戳")
    private Long compressedTime;
    
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;
    
    @Schema(description = "压缩序列号")
    private Long compressSequence;
    
    @Schema(description = "关联的当前压缩消息ID")
    private String currentCompressMessageId;
    
    @Schema(description = "压缩前的消息数量")
    private Integer messageCount;
    
    @Schema(description = "压缩前的内容长度")
    private Integer originalContentLength;
    
    @Schema(description = "压缩后的内容长度")
    private Integer compressedContentLength;
    
    @Schema(description = "压缩类型")
    private String compressType;
    
    @Schema(description = "压缩状态")
    private String compressStatus;
    
    @Schema(description = "压缩耗时（毫秒）")
    private Long processingTimeMs;
    
    @Schema(description = "备注信息")
    private String remarks;
    
    @Schema(description = "压缩率（百分比）")
    private Double compressionRatio;
    
    @Schema(description = "压缩时间格式化")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime compressedTimeFormatted;
    
    /**
     * 从实体转换为DTO
     *
     * @param entity 压缩历史记录实体
     * @return DTO对象
     */
    public static ChatCompressHistoryDto fromEntity(ChatCompressHistory entity) {
        if (entity == null) {
            return null;
        }
        
        ChatCompressHistoryDto dto = ChatCompressHistoryDto.builder()
                .id(entity.getId())
                .sessionId(entity.getSessionId())
                .username(entity.getUsername())
                .agentId(entity.getAgentId())
                .agentName(entity.getAgentName())
                .content(entity.getContent())
                .endSeqNum(entity.getEndSeqNum())
                .compressedTime(entity.getCompressedTime())
                .createdAt(entity.getCreatedAt())
                .compressSequence(entity.getCompressSequence())
                .currentCompressMessageId(entity.getCurrentCompressMessageId())
                .messageCount(entity.getMessageCount())
                .originalContentLength(entity.getOriginalContentLength())
                .compressedContentLength(entity.getCompressedContentLength())
                .compressType(entity.getCompressType())
                .compressStatus(entity.getCompressStatus())
                .processingTimeMs(entity.getProcessingTimeMs())
                .remarks(entity.getRemarks())
                .build();
        
        // 计算压缩率
        if (entity.getOriginalContentLength() != null && entity.getOriginalContentLength() > 0 
            && entity.getCompressedContentLength() != null) {
            double ratio = (1.0 - (double) entity.getCompressedContentLength() / entity.getOriginalContentLength()) * 100;
            dto.setCompressionRatio(Math.round(ratio * 100.0) / 100.0); // 保留两位小数
        }
        
        // 格式化压缩时间
        if (entity.getCompressedTime() != null && entity.getCompressedTime() > 0) {
            dto.setCompressedTimeFormatted(
                LocalDateTime.ofEpochSecond(entity.getCompressedTime() / 1000, 0, 
                    java.time.ZoneOffset.ofHours(8)) // 假设使用东八区时间
            );
        }
        
        return dto;
    }
    
    /**
     * 转换为实体
     *
     * @return 实体对象
     */
    public ChatCompressHistory toEntity() {
        return ChatCompressHistory.builder()
                .id(this.id)
                .sessionId(this.sessionId)
                .username(this.username)
                .agentId(this.agentId)
                .agentName(this.agentName)
                .content(this.content)
                .endSeqNum(this.endSeqNum)
                .compressedTime(this.compressedTime)
                .createdAt(this.createdAt)
                .compressSequence(this.compressSequence)
                .currentCompressMessageId(this.currentCompressMessageId)
                .messageCount(this.messageCount)
                .originalContentLength(this.originalContentLength)
                .compressedContentLength(this.compressedContentLength)
                .compressType(this.compressType)
                .compressStatus(this.compressStatus)
                .processingTimeMs(this.processingTimeMs)
                .remarks(this.remarks)
                .build();
    }
    
    /**
     * 获取压缩效率描述
     *
     * @return 压缩效率描述
     */
    public String getCompressionEfficiencyDescription() {
        if (compressionRatio == null) {
            return "未知";
        }
        
        if (compressionRatio >= 80) {
            return "极高";
        } else if (compressionRatio >= 60) {
            return "高";
        } else if (compressionRatio >= 40) {
            return "中等";
        } else if (compressionRatio >= 20) {
            return "低";
        } else {
            return "极低";
        }
    }
    
    /**
     * 获取处理时间描述
     *
     * @return 处理时间描述
     */
    public String getProcessingTimeDescription() {
        if (processingTimeMs == null || processingTimeMs <= 0) {
            return "未知";
        }
        
        if (processingTimeMs < 1000) {
            return processingTimeMs + "毫秒";
        } else if (processingTimeMs < 60000) {
            return String.format("%.1f秒", processingTimeMs / 1000.0);
        } else {
            long minutes = processingTimeMs / 60000;
            long seconds = (processingTimeMs % 60000) / 1000;
            return String.format("%d分%d秒", minutes, seconds);
        }
    }
}
