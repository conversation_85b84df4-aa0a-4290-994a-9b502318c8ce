package com.gw.chat.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.gw.chat.entity.ConversationSession;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 会话数据传输对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ConversationSessionDto {

    /**
     * 会话ID
     */
    private String id;

    /**
     * 用户ID
     */
    private String username;

    /**
     * AI助手ID
     */
    private Long agentId;
    private String agentPicUrl;
    private String agentPicThumbnailUrl;
    /**
     * 会话标题
     */
    private String title;

    /**
     * 会话状态
     */
    private String status;

    /**
     * 最后一条消息
     */
    @Schema(description = "最后一条消息")
    private String lastMessage;
    @Schema(description = "最后一条消息时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastMessageTime;
    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
    @Schema(description = "置顶状态 1 置顶 0 取消指定")
    private int topping;
    /**
     * 会话上下文
     */
    private Map<String, Object> context;
    @JsonIgnore
    private ConversationSession entity;
    private Long lstSeqNum;
    /**
     * 从实体转换为DTO
     */
    public static ConversationSessionDto fromEntity(ConversationSession entity) {
        return ConversationSessionDto.builder()
                .id(entity.getId())
                .entity(entity)
                .username(entity.getUsername())
                .lstSeqNum(entity.getLstSeqNum())
                .agentId(entity.getAgentId())
                .title(entity.getTitle())
                .status(entity.getStatus().name())
                .lastMessage(entity.getLastMessage())
                .lastMessageTime(entity.getLastMessageTime())
                .createdAt(entity.getCreatedAt())
                .updatedAt(entity.getUpdatedAt())
                .context(entity.getContext())
                .topping(entity.getTopping())
                .build();
    }

    /**
     * 转换为实体
     */
    public ConversationSession toEntity() {
        return ConversationSession.builder()
                .id(this.id)
                .username(this.username)
                .agentId(this.agentId)
                .title(this.title)
                .status(ConversationSession.SessionStatus.valueOf(this.status))
                .lastMessage(this.lastMessage)
                .context(this.context)
                .build();
    }
} 