package com.gw.chat.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
@Schema(description = "点赞操作请求")
public class LikeActionDTO {
    @Schema(description = "会话ID", required = true)
    @NotBlank(message = "会话ID不能为空")
    private String sessionId;

    @Schema(description = "点赞状态", required = true)
    @NotNull(message = "点赞状态不能为空")
    private Boolean liked;
} 