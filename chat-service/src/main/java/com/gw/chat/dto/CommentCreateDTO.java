package com.gw.chat.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

@Data
@Schema(description = "创建评论请求")
public class CommentCreateDTO {
    @Schema(description = "会话ID")
    @NotBlank(message = "会话ID")
    private String sessionId;

    @Schema(description = "评论内容")
    @NotBlank(message = "评论内容不能为空")
    private String content;
} 