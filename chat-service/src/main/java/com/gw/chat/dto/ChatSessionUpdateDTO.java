package com.gw.chat.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Map;

@EqualsAndHashCode(callSuper = true)
@Data
public class ChatSessionUpdateDTO extends ChatSessionSubmitDTO {
    @Schema(description = "会话ID")
    @NotBlank(message = "会话ID不能为空")
    private String sessionId;
    private Map<String, Object> context;
}
