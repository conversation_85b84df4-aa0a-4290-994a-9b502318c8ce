package com.gw.chat.exception;

import com.gw.common.dto.ResponseResult;
import com.gw.common.exception.BusinessException;
import com.gw.common.exception.BusinessExceptionCode;
import com.gw.common.exception.ErrorRequestException;
import lombok.extern.log4j.Log4j2;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

/**
 * 响应式控制器异常处理
 * 专门用于处理WebFlux环境中的异常
 */
@RestControllerAdvice
@Log4j2
public class ReactiveControllerAdvice {

    /**
     * 处理业务异常
     */
    @ExceptionHandler(BusinessException.class)
    @ResponseStatus(HttpStatus.OK)
    public Mono<ResponseResult<?>> handleBusinessException(BusinessException e, ServerWebExchange exchange) {
        logError(exchange, e);
        return Mono.just(ResponseResult.failure(e.getCode(), e.getMsg()));
    }

    /**
     * 处理请求错误异常
     */
    @ExceptionHandler(ErrorRequestException.class)
    @ResponseStatus(HttpStatus.OK)
    public Mono<ResponseResult<?>> handleErrorRequestException(ErrorRequestException e, ServerWebExchange exchange) {
        logError(exchange, e);
        return Mono.just(ResponseResult.failure(e.getCode(), e.getMsg()));
    }

    /**
     * 处理火山方舟内容解析异常
     */
    @ExceptionHandler(VolcanoContentParsingException.class)
    @ResponseStatus(HttpStatus.OK)
    public Mono<ResponseResult<?>> handleVolcanoContentParsingException(VolcanoContentParsingException e, ServerWebExchange exchange) {
        logError(exchange, e);
        return Mono.just(ResponseResult.failure(
                BusinessExceptionCode.FAIL_CODE.getCode(),
                e.getMessage()));
    }

    /**
     * 处理运行时异常
     */
    @ExceptionHandler(RuntimeException.class)
    @ResponseStatus(HttpStatus.OK)
    public Mono<ResponseResult<?>> handleRuntimeException(RuntimeException e, ServerWebExchange exchange) {
        logError(exchange, e);
        return Mono.just(ResponseResult.failure(
                BusinessExceptionCode.INTERNAL_SERVER_ERROR_CODE.getCode(),
                "系统内部错误"));
    }

    /**
     * 处理所有其他异常
     */
    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.OK)
    public Mono<ResponseResult<?>> handleException(Exception e, ServerWebExchange exchange) {
        logError(exchange, e);
        return Mono.just(ResponseResult.failure(
                BusinessExceptionCode.INTERNAL_SERVER_ERROR_CODE.getCode(),
                "系统异常，请稍后重试"));
    }

    /**
     * 记录错误日志
     */
    private void logError(ServerWebExchange exchange, Exception e) {
        String requestURI = exchange.getRequest().getURI().getPath();
        String method = exchange.getRequest().getMethod().name();
        log.error("请求异常 - {} {} - 异常类型: {}", method, requestURI, e.getClass().getSimpleName(), e);
    }
}