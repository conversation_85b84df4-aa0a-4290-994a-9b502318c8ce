package com.gw.chat.exception;

import com.gw.common.dto.ResponseResult;
import com.gw.common.exception.BusinessException;
import com.gw.common.exception.BusinessExceptionCode;
import com.gw.common.exception.ErrorRequestException;
import lombok.extern.log4j.Log4j2;
import org.springframework.boot.autoconfigure.web.WebProperties;
import org.springframework.boot.autoconfigure.web.reactive.error.AbstractErrorWebExceptionHandler;
import org.springframework.boot.web.error.ErrorAttributeOptions;
import org.springframework.boot.web.reactive.error.ErrorAttributes;
import org.springframework.context.ApplicationContext;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.codec.ServerCodecConfigurer;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.server.*;
import org.springframework.web.server.ResponseStatusException;
import reactor.core.publisher.Mono;

import java.util.Map;

/**
 * 响应式全局异常处理器
 */
@Component
@Order(-2)
@Log4j2
public class ReactiveGlobalExceptionHandler extends AbstractErrorWebExceptionHandler {

    private final ReactiveExceptionHandler reactiveExceptionHandler;

    public ReactiveGlobalExceptionHandler(ErrorAttributes errorAttributes,
                                          WebProperties webProperties,
                                          ApplicationContext applicationContext,
                                          ServerCodecConfigurer configurer,
                                          ReactiveExceptionHandler reactiveExceptionHandler) {
        super(errorAttributes, webProperties.getResources(), applicationContext);
        this.setMessageWriters(configurer.getWriters());
        this.setMessageReaders(configurer.getReaders());
        this.reactiveExceptionHandler = reactiveExceptionHandler;
    }

    @Override
    protected RouterFunction<ServerResponse> getRoutingFunction(ErrorAttributes errorAttributes) {
        return RouterFunctions.route(RequestPredicates.all(), this::renderErrorResponse);
    }

    private Mono<ServerResponse> renderErrorResponse(ServerRequest request) {
        // 使用ReactiveExceptionHandler来获取错误属性
        Map<String, Object> errorPropertiesMap = getErrorAttributes(request, ErrorAttributeOptions.defaults());
        Throwable error = getError(request);

        log.error("请求异常 - {} {} - 异常类型: {}",
                request.method(), request.path(), error.getClass().getSimpleName(), error);

        ResponseResult<?> responseResult;

        if (error instanceof BusinessException) {
            BusinessException be = (BusinessException) error;
            responseResult = ResponseResult.failure(be.getCode(), be.getMsg());
        } else if (error instanceof ErrorRequestException) {
            ErrorRequestException ere = (ErrorRequestException) error;
            responseResult = ResponseResult.failure(ere.getCode(), ere.getMsg());
        } else if (error instanceof ResponseStatusException) {
            ResponseStatusException rse = (ResponseStatusException) error;
            responseResult = ResponseResult.failure(rse.getStatusCode().value(), rse.getReason());
        } else if (error instanceof IllegalStateException && error.getMessage() != null &&
                error.getMessage().contains("Could not resolve parameter")) {
            // 处理参数解析错误，通常是HttpServletRequest相关
            log.warn("参数解析错误: {}", error.getMessage());
            responseResult = ResponseResult.failure(
                    BusinessExceptionCode.INTERNAL_SERVER_ERROR_CODE.getCode(),
                    "请求参数解析错误");
        } else {
            responseResult = ResponseResult.failure(
                    BusinessExceptionCode.INTERNAL_SERVER_ERROR_CODE.getCode(),
                    "系统异常，请稍后重试");
        }

        return ServerResponse
                .status(HttpStatus.OK)
                .contentType(MediaType.APPLICATION_JSON)
                .body(BodyInserters.fromValue(responseResult));
    }
}