package com.gw.chat.exception;

import com.gw.common.dto.ResponseResult;
import com.gw.common.exception.BusinessException;
import com.gw.common.exception.BusinessExceptionCode;
import com.gw.common.exception.ErrorRequestException;
import lombok.extern.log4j.Log4j2;
import org.springframework.boot.web.reactive.error.DefaultErrorAttributes;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.server.ServerRequest;
import org.springframework.web.server.ResponseStatusException;
import org.springframework.web.server.ServerWebExchange;

import java.util.Map;

/**
 * WebFlux环境下的异常属性处理
 */
@Component
@Log4j2
public class ReactiveExceptionHandler extends DefaultErrorAttributes {

    @Override
    public Map<String, Object> getErrorAttributes(ServerRequest request,
                                                  org.springframework.boot.web.error.ErrorAttributeOptions options) {
        Throwable error = getError(request);
        logError(request, error);

        Map<String, Object> errorAttributes = super.getErrorAttributes(request, options);

        // 更新错误属性以匹配自定义格式
        int code;
        String message;

        if (error instanceof BusinessException) {
            BusinessException be = (BusinessException) error;
            code = be.getCode();
            message = be.getMsg();
        } else if (error instanceof ErrorRequestException) {
            ErrorRequestException ere = (ErrorRequestException) error;
            code = ere.getCode();
            message = ere.getMsg();
        } else if (error instanceof ResponseStatusException) {
            ResponseStatusException rse = (ResponseStatusException) error;
            code = rse.getStatusCode().value();
            message = rse.getReason();
        } else if (error instanceof IllegalStateException && error.getMessage() != null &&
                error.getMessage().contains("Could not resolve parameter")) {
            // 处理参数解析错误
            code = BusinessExceptionCode.INTERNAL_SERVER_ERROR_CODE.getCode();
            message = "WebFlux环境中的请求参数解析错误";
            log.warn("WebFlux环境中参数解析错误: {}", error.getMessage());
        } else {
            code = BusinessExceptionCode.INTERNAL_SERVER_ERROR_CODE.getCode();
            message = "系统异常，请稍后重试";
        }

        errorAttributes.put("code", code);
        errorAttributes.put("message", message);

        return errorAttributes;
    }

    /**
     * 将异常信息记录到日志
     */
    private void logError(ServerRequest request, Throwable error) {
        log.error("请求异常 - {} {} - 异常类型: {}",
                request.method(), request.path(), error.getClass().getSimpleName(), error);
    }

    /**
     * 将ResponseResult转换为错误属性
     */
    public ResponseResult<?> createErrorResponse(ServerWebExchange exchange, Throwable error) {
        int code;
        String message;

        if (error instanceof BusinessException) {
            BusinessException be = (BusinessException) error;
            code = be.getCode();
            message = be.getMsg();
        } else if (error instanceof ErrorRequestException) {
            ErrorRequestException ere = (ErrorRequestException) error;
            code = ere.getCode();
            message = ere.getMsg();
        } else if (error instanceof ResponseStatusException) {
            ResponseStatusException rse = (ResponseStatusException) error;
            code = rse.getStatusCode().value();
            message = rse.getReason();
        } else {
            code = HttpStatus.INTERNAL_SERVER_ERROR.value();
            message = "系统异常，请稍后重试";
        }

        log.error("请求异常 - {} {} - 异常类型: {}",
                exchange.getRequest().getMethod(),
                exchange.getRequest().getURI().getPath(),
                error.getClass().getSimpleName(),
                error);

        return ResponseResult.failure(code, message);
    }
}