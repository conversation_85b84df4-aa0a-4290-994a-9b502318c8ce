package com.gw.chat.exception;

import lombok.Getter;

/**
 * 🌋 火山方舟内容解析异常
 * 专门用于处理火山方舟API响应解析过程中的各种异常情况
 */
@Getter
public class VolcanoContentParsingException extends RuntimeException {

    /**
     * 错误代码
     */
    private final String errorCode;

    /**
     * 是否可重试
     */
    private final boolean retryable;

    /**
     * 异常类型
     */
    private final VolcanoErrorType errorType;

    public VolcanoContentParsingException(String message) {
        super(message);
        this.errorCode = "VOLCANO_PARSING_ERROR";
        this.retryable = false;
        this.errorType = VolcanoErrorType.GENERAL;
    }

    public VolcanoContentParsingException(String errorCode, String message, VolcanoErrorType errorType) {
        super(message);
        this.errorCode = errorCode;
        this.retryable = errorType.isRetryable();
        this.errorType = errorType;
    }

    public VolcanoContentParsingException(String errorCode, String message, VolcanoErrorType errorType, boolean retryable) {
        super(message);
        this.errorCode = errorCode;
        this.retryable = retryable;
        this.errorType = errorType;
    }

    public VolcanoContentParsingException(String message, Throwable cause) {
        super(message, cause);
        this.errorCode = "VOLCANO_PARSING_ERROR";
        this.retryable = false;
        this.errorType = VolcanoErrorType.GENERAL;
    }

    public VolcanoContentParsingException(String errorCode, String message, VolcanoErrorType errorType, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
        this.retryable = errorType.isRetryable();
        this.errorType = errorType;
    }

    /**
     * 🌋 火山方舟错误类型枚举
     */
    @Getter
    public enum VolcanoErrorType {
        /**
         * 内容长度超限
         */
        CONTENT_TOO_LONG("CONTENT_TOO_LONG", "内容太长，请重新提问", false),
        
        /**
         * 空响应
         */
        EMPTY_RESPONSE("EMPTY_RESPONSE", "火山方舟API返回空响应", true),
        
        /**
         * 空内容
         */
        EMPTY_CONTENT("EMPTY_CONTENT", "火山方舟API返回空内容", true),
        
        /**
         * 一般解析错误
         */
        GENERAL("GENERAL_PARSING_ERROR", "火山方舟内容解析异常", false);

        private final String code;
        private final String defaultMessage;
        private final boolean retryable;

        VolcanoErrorType(String code, String defaultMessage, boolean retryable) {
            this.code = code;
            this.defaultMessage = defaultMessage;
            this.retryable = retryable;
        }
    }

    /**
     * 🔧 便捷方法：创建内容长度超限异常
     */
    public static VolcanoContentParsingException contentTooLong() {
        return new VolcanoContentParsingException(
            VolcanoErrorType.CONTENT_TOO_LONG.getCode(),
            VolcanoErrorType.CONTENT_TOO_LONG.getDefaultMessage(),
            VolcanoErrorType.CONTENT_TOO_LONG
        );
    }

    /**
     * 🔧 便捷方法：创建空响应异常
     */
    public static VolcanoContentParsingException emptyResponse() {
        return new VolcanoContentParsingException(
            VolcanoErrorType.EMPTY_RESPONSE.getCode(),
            VolcanoErrorType.EMPTY_RESPONSE.getDefaultMessage(),
            VolcanoErrorType.EMPTY_RESPONSE
        );
    }

    /**
     * 🔧 便捷方法：创建空内容异常
     */
    public static VolcanoContentParsingException emptyContent() {
        return new VolcanoContentParsingException(
            VolcanoErrorType.EMPTY_CONTENT.getCode(),
            VolcanoErrorType.EMPTY_CONTENT.getDefaultMessage(),
            VolcanoErrorType.EMPTY_CONTENT
        );
    }
}
