package com.gw.chat.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "评论及回复数据")
public class CommentWithRepliesVO {

    @Schema(description = "评论信息")
    private CommentVO comment;

    @Schema(description = "回复列表")
    private List<CommentVO> replies;
} 