# 压缩任务配置
compress:
  task:
    # 分页大小（每页查询的会话数量）
    page-size: 500

    # 最大页数（防止无限循环的安全限制）
    max-pages: 1000

    # 批处理大小（批量处理时每批的会话数量）
    batch-size: 50

    # 是否启用压缩任务
    enabled: true

    # 压缩任务执行间隔（分钟）
    interval-minutes: 30

    # 压缩任务执行间隔（cron表达式）- 保留用于其他调度需求
    # 示例：每天凌晨2点执行
    cron: "0 0 2 * * ?"

    # 压缩任务超时时间（毫秒）
    timeout: 1800000  # 30分钟

    # 并发处理线程数
    thread-pool-size: 3

    # 重试配置
    retry:
      # 最大重试次数
      max-attempts: 1
      # 重试延迟（毫秒）
      delay-ms: 1000

    # 性能优化配置
    performance:
      # 流式处理页面大小
      stream-page-size: 200
      # 内容长度限制（字符数）
      max-content-length: 50000
      # 分段处理大小
      chunk-size: 25000

# 日志配置
logging:
  level:
    com.gw.chat.task.CompressTask: DEBUG
    com.gw.chat.service.impl.ChatServiceImpl: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%logger{36}] - %msg%n"
