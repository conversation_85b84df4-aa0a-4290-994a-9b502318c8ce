server:
  port: 9085

spring:
  application:
    name: chat-service
  profiles:
    # 激活内存优化配置（可选，根据需要启用）
    # active: compress-memory-optimized
    include:
      # 默认包含压缩配置，如需内存优化可改为 compress-memory-optimized
      - compress
  main:
    # 允许循环引用以解决WebSocketHandler的依赖问题
    allow-circular-references: true
    allow-bean-definition-overriding: true
    # 将web-application-type从servlet更改为reactive以支持Spring Cloud Gateway的WebSocket请求
    web-application-type: reactive
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
  data:
    mongodb:
      uri: ${MONGODB_URI:mongodb://localhost:27017/chat_service?retryWrites=false}
      auto-index-creation: true
      transactionEnabled: false
      timezone: Asia/Shanghai
    redis:
      host: ${REDIS_HOST:localhost}
      port: ${REDIS_PORT:6379}
      password: ${REDIS_PASSWORD:}
      database: 0
      timeout: 30000
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 0
          max-wait: -1
  cloud:
    nacos:
      discovery:
        server-addr: localhost:8848
    openfeign:
      client:
        config:
          default:
            connectTimeout: 5000
            readTimeout: 10000
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration
      - org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration
      - org.springframework.boot.autoconfigure.data.mongo.MongoDataAutoConfiguration
      - org.springframework.boot.autoconfigure.data.mongo.MongoReactiveDataAutoConfiguration
      - org.springframework.boot.autoconfigure.data.mongo.MongoRepositoriesAutoConfiguration
  task:
    execution:
      pool:
        core-size: 4
        max-size: 8
        queue-capacity: 100
      thread-name-prefix: chat-task-

logging:
  config: classpath:log4j2.xml
  level:
    com.gw.chat: ${LOG_LEVEL:debug}
    org.springframework.data.mongodb: ${MONGO_LOG_LEVEL:info}
    org.springframework.web.socket: ${WEBSOCKET_LOG_LEVEL:debug}
    org.springframework.web: ${WEB_LOG_LEVEL:debug}
    org.springframework.beans: ${BEAN_LOG_LEVEL:debug}
springdoc:
  api-docs:
    enabled: true
    path: /api/v1/chat/v3/api-docs
  swagger-ui:
    enabled: true
    path: /api/v1/chat/swagger-ui/index.html
    config-url: /api/v1/chat/v3/api-docs/swagger-config
    urls:
      - url: /api/v1/chat/v3/api-docs
        name: 聊天服务API
  paths-to-match: /api/v1/chat/**
  cache:
    disabled: true

# Feign客户端配置
feign:
  client:
    config:
      default:
        connectTimeout: 5000
        readTimeout: 10000
  compression:
    request:
      enabled: true
    response:
      enabled: true

# WebSocket配置
websocket:
  allowed-origins: "*"
wx:
  miniapp:
    appid: ${WX_MINIAPP_APPID:wxe96c29e240e54ff0}
    secret: ${WX_MINIAPP_SECRET:7b9a54bc3bf8070c34be5cb184602561}
    access-token-url: https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=APPID&secret=APPSECRET
    msg-sec-check-url: https://api.weixin.qq.com/wxa/msg_sec_check
    default-openid: test-openid # 测试用的OpenID，实际使用时应该传入真实用户的OpenID
# 聊天配置
chat:
  session:
    expiration-days: 179 # 会话过期天数
    # 发送超时配置
    sending-timeout-seconds: ${CHAT_SENDING_TIMEOUT:10} # 聊天发送超时时间（秒），默认10秒
  speech:
    temp-path: upload/file/chat/speech/tmp/
    fixed-path: upload/file/chat/speech/fixed/
    max-dir-size: 15368709120 # 5GB in bytes
  history:
    # 历史消息限制配置
    vip-limit: 150 # VIP用户历史消息限制
    normal-limit: 50 # 普通用户历史消息限制
    # TTL配置（秒）
    vip-ttl: 604800 # VIP用户TTL（7天）
    normal-ttl: 7200 # 普通用户TTL（2小时）

# 火山方舟配置
volcanoark:
  api-key: ${VOLCANOARK_API_KEY:b75e2ee2-f24b-40ef-8c04-9df7f797c385} # 请设置环境变量或在此处填入您的API Key
  base-url: ${VOLCANOARK_BASE_URL:https://ark.cn-beijing.volces.com}
  default-model-id: ${VOLCANOARK_MODEL_ID:ep-m-20250608214046-pv8dg}
  compress-model-id: ${VOLCANOARK_COMPRESS_MODEL_ID:ep-20250706151033-n28bn}
  connect-timeout: 30000
  read-timeout: 120000
  retry-times: 1
  max-connections: 10
  keep-alive-duration: 60

cache:
  prefix: lingxi
  configs:
    - name: agentBase
      expireAfterWrite: 1h
    - name: agentTag
      expireAfterWrite: 720h
    - name: agentType
      expireAfterWrite: 720h
    - name: agent
      expireAfterWrite: 720h
    - name: coze
      expireAfterWrite: 720h
