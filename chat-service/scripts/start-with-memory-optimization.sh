#!/bin/bash

# CompressTask 内存优化启动脚本
# 使用此脚本启动应用时会自动启用内存优化配置

echo "=== 启动 Chat Service (内存优化模式) ==="

# JVM 内存优化参数
JVM_OPTS="-Xms2g \
-Xmx4g \
-XX:NewRatio=3 \
-XX:+UseG1GC \
-XX:MaxGCPauseMillis=200 \
-XX:+PrintGCDetails \
-XX:+PrintGCTimeStamps \
-XX:+HeapDumpOnOutOfMemoryError \
-XX:HeapDumpPath=/tmp/chat-service-heapdump.hprof \
-XX:+UseStringDeduplication \
-XX:G1HeapRegionSize=16m"

# Spring Profile 配置 - 启用内存优化配置
SPRING_PROFILES="compress-memory-optimized"

# 应用参数
APP_OPTS="--spring.profiles.active=${SPRING_PROFILES} \
--logging.level.com.gw.chat.task.CompressTask=DEBUG \
--logging.level.com.gw.chat.util.MemoryMonitor=DEBUG"

echo "JVM 参数: ${JVM_OPTS}"
echo "Spring Profiles: ${SPRING_PROFILES}"
echo "应用参数: ${APP_OPTS}"

# 检查 JAR 文件是否存在
JAR_FILE="target/chat-service-*.jar"
if [ ! -f ${JAR_FILE} ]; then
    echo "错误: 找不到 JAR 文件 ${JAR_FILE}"
    echo "请先执行: mvn clean package"
    exit 1
fi

echo "启动应用..."
java ${JVM_OPTS} -jar ${JAR_FILE} ${APP_OPTS}
