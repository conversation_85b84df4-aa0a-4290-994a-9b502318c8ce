# CompressTask 优化文档

## 概述

本文档描述了对 `CompressTask` 类进行的性能优化，确保在处理大量聊天会话压缩时的高效性和稳定性。

## 主要优化内容

### 1. 并行处理架构

#### 优化前
- 单线程顺序处理所有会话
- 处理速度慢，资源利用率低

#### 优化后
- 引入多线程并行处理
- 使用 `ExecutorService` 和 `CompletionService` 管理并发任务
- 可配置线程池大小（默认3个线程）

```yaml
compress:
  task:
    thread-pool-size: 3  # 并发处理线程数
```

### 2. 批处理机制

#### 优化前
- 逐个处理会话，效率低下

#### 优化后
- 批量收集会话进行并行处理
- 可配置批处理大小（默认50个会话/批）
- 减少线程创建开销

```yaml
compress:
  task:
    batch-size: 50  # 批处理大小
```

### 3. 内存优化

#### 流式处理
- 将大会话的消息分页处理，避免内存溢出
- 减小页面大小（200条消息/页）
- 及时清理不需要的对象引用

#### 内容分段
- 对超长内容进行智能分段处理
- 避免单次AI调用内容过长导致超时
- 最大内容长度限制：50,000字符

### 4. 重试机制

#### 指数退避重试
- 最大重试次数：3次
- 重试延迟：1秒起，指数增长
- 提高任务成功率

```yaml
compress:
  task:
    retry:
      max-attempts: 3
      delay-ms: 1000
```

### 5. 性能监控

#### 实时统计
- 总处理数量
- 跳过数量
- 错误数量
- 活跃线程数

#### 健康检查
- 线程池状态监控
- 错误率检查
- 提供REST API接口

### 6. 资源管理

#### 优雅关闭
- `@PreDestroy` 注解确保线程池正确关闭
- 30秒等待调度线程池关闭
- 60秒等待工作线程池关闭

#### 超时控制
- 单个任务超时时间：30分钟
- 防止任务无限期运行

## 配置参数说明

### 核心配置

```yaml
compress:
  task:
    # 基础配置
    enabled: true                    # 是否启用压缩任务
    interval-minutes: 30             # 执行间隔（分钟）
    page-size: 500                   # 分页大小
    max-pages: 1000                  # 最大页数
    batch-size: 50                   # 批处理大小
    thread-pool-size: 3              # 线程池大小
    timeout: 1800000                 # 超时时间（毫秒）
    
    # 重试配置
    retry:
      max-attempts: 3                # 最大重试次数
      delay-ms: 1000                 # 重试延迟
    
    # 性能优化配置
    performance:
      stream-page-size: 200          # 流式处理页面大小
      max-content-length: 50000      # 最大内容长度
      chunk-size: 25000              # 分段大小
```

## API 接口

### 监控接口

#### 获取性能统计
```http
GET /api/compress-task/stats
```

#### 健康检查
```http
GET /api/compress-task/health
```

#### 重置统计
```http
POST /api/compress-task/reset-stats
```

#### 手动触发
```http
POST /api/compress-task/trigger
```

## 性能提升

### 处理速度
- **优化前**: 单线程顺序处理，约 10-20 会话/分钟
- **优化后**: 多线程并行处理，约 50-100 会话/分钟（3线程）

### 内存使用
- **优化前**: 加载所有消息到内存，可能导致OOM
- **优化后**: 流式处理，内存使用稳定

### 错误恢复
- **优化前**: 单个会话失败可能影响整个任务
- **优化后**: 隔离错误，重试机制，提高成功率

## 监控和运维

### 日志级别
```yaml
logging:
  level:
    com.gw.chat.task.CompressTask: DEBUG
```

### 关键指标
- 处理成功率
- 平均处理时间
- 内存使用情况
- 线程池状态

### 告警建议
- 错误率超过50%
- 处理时间超过预期
- 线程池异常关闭

## 注意事项

1. **线程池大小**: 根据服务器资源调整，避免过多线程导致资源竞争
2. **批处理大小**: 平衡内存使用和处理效率
3. **超时时间**: 根据AI服务响应时间调整
4. **重试策略**: 避免过度重试导致系统负载过高

## 故障排查

### 常见问题

1. **任务不执行**
   - 检查 `enabled` 配置
   - 查看线程池状态
   - 检查日志错误信息

2. **处理速度慢**
   - 增加线程池大小
   - 减小批处理大小
   - 检查AI服务响应时间

3. **内存溢出**
   - 减小页面大小
   - 降低并发线程数
   - 检查内容长度限制

4. **频繁失败**
   - 检查AI服务可用性
   - 调整重试参数
   - 查看具体错误日志
