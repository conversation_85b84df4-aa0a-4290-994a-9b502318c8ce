-- 智能体标签表
-- 删除已存在的表和序列
DROP TABLE IF EXISTS "public"."t_agent_tag";
DROP SEQUENCE IF EXISTS t_agent_tag_id_seq;

-- 创建序列
CREATE SEQUENCE t_agent_tag_id_seq START 1 CACHE 1;

-- 创建表
CREATE TABLE t_agent_tag
(
    -- BaseEntity字段
    id          BIGINT PRIMARY KEY DEFAULT NEXTVAL('t_agent_tag_id_seq'::regclass),
    create_time TIMESTAMP          DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP          DEFAULT CURRENT_TIMESTAMP,
    deleted     INTEGER            DEFAULT 0,
    creator     VARCHAR(255),
    updater     VARCHAR(255),

    -- AgentTagEntity特有字段
    name        VARCHAR(255) NOT NULL,
    type        INTEGER      NOT NULL,
    category    VARCHAR(255),
    description TEXT,
    use_count   INTEGER            DEFAULT 0
);

-- 添加表注释
COMMENT ON TABLE t_agent_tag IS '智能体标签表';
COMMENT ON COLUMN t_agent_tag.id IS '主键ID';
COMMENT ON COLUMN t_agent_tag.create_time IS '创建时间';
COMMENT ON COLUMN t_agent_tag.update_time IS '更新时间';
COMMENT ON COLUMN t_agent_tag.deleted IS '删除标记(0:未删除,1:已删除)';
COMMENT ON COLUMN t_agent_tag.creator IS '创建者';
COMMENT ON COLUMN t_agent_tag.updater IS '更新者';
COMMENT ON COLUMN t_agent_tag.name IS '标签名称';
COMMENT ON COLUMN t_agent_tag.type IS '标签类型';
COMMENT ON COLUMN t_agent_tag.category IS '标签分类';
COMMENT ON COLUMN t_agent_tag.description IS '标签描述';
COMMENT ON COLUMN t_agent_tag.use_count IS '使用次数';
INSERT INTO "public"."t_agent_tag" ("id", "create_time", "update_time", "deleted", "creator", "updater", "name", "type",
                                    "category", "description", "use_count")
VALUES (1, '2025-03-14 10:11:09.544435', '2025-03-14 10:11:09.544435', 0, 'root', 'root', '世家', 1, '1', '', 0);
INSERT INTO "public"."t_agent_tag" ("id", "create_time", "update_time", "deleted", "creator", "updater", "name", "type",
                                    "category", "description", "use_count")
VALUES (2, '2025-03-14 10:12:29.398973', '2025-03-14 10:12:29.398973', 0, 'root', 'root', '权谋', 1, '1', '', 0);
INSERT INTO "public"."t_agent_tag" ("id", "create_time", "update_time", "deleted", "creator", "updater", "name", "type",
                                    "category", "description", "use_count")
VALUES (3, '2025-03-14 10:16:07.316886', '2025-03-14 10:16:07.316886', 0, 'root', 'root', '仙侠', 1, '1', '', 0);
INSERT INTO "public"."t_agent_tag" ("id", "create_time", "update_time", "deleted", "creator", "updater", "name", "type",
                                    "category", "description", "use_count")
VALUES (4, '2025-03-14 10:16:07.346305', '2025-03-14 10:16:07.346305', 0, 'root', 'root', '武侠', 1, '1', '', 0);
INSERT INTO "public"."t_agent_tag" ("id", "create_time", "update_time", "deleted", "creator", "updater", "name", "type",
                                    "category", "description", "use_count")
VALUES (5, '2025-03-14 10:16:07.347172', '2025-03-14 10:16:07.347172', 0, 'root', 'root', '校园', 1, '1', '', 0);
INSERT INTO "public"."t_agent_tag" ("id", "create_time", "update_time", "deleted", "creator", "updater", "name", "type",
                                    "category", "description", "use_count")
VALUES (6, '2025-03-14 10:16:07.347731', '2025-03-14 10:16:07.347731', 0, 'root', 'root', '悬疑', 1, '1', '', 0);
INSERT INTO "public"."t_agent_tag" ("id", "create_time", "update_time", "deleted", "creator", "updater", "name", "type",
                                    "category", "description", "use_count")
VALUES (7, '2025-03-14 10:16:07.348323', '2025-03-14 10:16:07.348323', 0, 'root', 'root', '娱乐圈', 1, '1', '', 0);
INSERT INTO "public"."t_agent_tag" ("id", "create_time", "update_time", "deleted", "creator", "updater", "name", "type",
                                    "category", "description", "use_count")
VALUES (8, '2025-03-14 10:16:07.348857', '2025-03-14 10:16:07.348857', 0, 'root', 'root', '科幻', 1, '1', '', 0);
INSERT INTO "public"."t_agent_tag" ("id", "create_time", "update_time", "deleted", "creator", "updater", "name", "type",
                                    "category", "description", "use_count")
VALUES (9, '2025-03-14 10:16:07.34937', '2025-03-14 10:16:07.34937', 0, 'root', 'root', '玄幻', 1, '1', '', 0);
INSERT INTO "public"."t_agent_tag" ("id", "create_time", "update_time", "deleted", "creator", "updater", "name", "type",
                                    "category", "description", "use_count")
VALUES (10, '2025-03-14 10:16:07.35002', '2025-03-14 10:16:07.35002', 0, 'root', 'root', '星际', 1, '1', '', 0);
INSERT INTO "public"."t_agent_tag" ("id", "create_time", "update_time", "deleted", "creator", "updater", "name", "type",
                                    "category", "description", "use_count")
VALUES (11, '2025-03-14 10:16:07.350561', '2025-03-14 10:16:07.350561', 0, 'root', 'root', '末世', 1, '1', '', 0);
INSERT INTO "public"."t_agent_tag" ("id", "create_time", "update_time", "deleted", "creator", "updater", "name", "type",
                                    "category", "description", "use_count")
VALUES (12, '2025-03-14 10:16:07.351092', '2025-03-14 10:16:07.351092', 0, 'root', 'root', '无限流', 1, '1', '', 0);
INSERT INTO "public"."t_agent_tag" ("id", "create_time", "update_time", "deleted", "creator", "updater", "name", "type",
                                    "category", "description", "use_count")
VALUES (13, '2025-03-14 10:16:07.351613', '2025-03-14 10:16:07.351613', 0, 'root', 'root', '架空', 1, '1', '', 0);
INSERT INTO "public"."t_agent_tag" ("id", "create_time", "update_time", "deleted", "creator", "updater", "name", "type",
                                    "category", "description", "use_count")
VALUES (14, '2025-03-14 10:16:07.352172', '2025-03-14 10:16:07.352172', 0, 'root', 'root', '异能', 1, '1', '', 0);
INSERT INTO "public"."t_agent_tag" ("id", "create_time", "update_time", "deleted", "creator", "updater", "name", "type",
                                    "category", "description", "use_count")
VALUES (15, '2025-03-14 10:16:07.352724', '2025-03-14 10:16:07.352724', 0, 'root', 'root', '穿越重生', 1, '1', '', 0);
INSERT INTO "public"."t_agent_tag" ("id", "create_time", "update_time", "deleted", "creator", "updater", "name", "type",
                                    "category", "description", "use_count")
VALUES (16, '2025-03-14 10:16:07.353263', '2025-03-14 10:16:07.353263', 0, 'root', 'root', '卡通', 1, '1', '', 0);
INSERT INTO "public"."t_agent_tag" ("id", "create_time", "update_time", "deleted", "creator", "updater", "name", "type",
                                    "category", "description", "use_count")
VALUES (17, '2025-03-14 10:16:07.353776', '2025-03-14 10:16:07.353776', 0, 'root', 'root', '萌宠', 1, '1', '', 0);
INSERT INTO "public"."t_agent_tag" ("id", "create_time", "update_time", "deleted", "creator", "updater", "name", "type",
                                    "category", "description", "use_count")
VALUES (18, '2025-03-14 10:16:07.354245', '2025-03-14 10:16:07.354245', 0, 'root', 'root', '宫廷', 1, '1', '', 0);
INSERT INTO "public"."t_agent_tag" ("id", "create_time", "update_time", "deleted", "creator", "updater", "name", "type",
                                    "category", "description", "use_count")
VALUES (19, '2025-03-14 10:21:07.690723', '2025-03-14 10:21:07.690723', 0, 'root', 'root', '占有欲', 1, '2', '', 0);
INSERT INTO "public"."t_agent_tag" ("id", "create_time", "update_time", "deleted", "creator", "updater", "name", "type",
                                    "category", "description", "use_count")
VALUES (20, '2025-03-14 10:21:07.719257', '2025-03-14 10:21:07.719257', 0, 'root', 'root', '腹黑', 1, '2', '', 0);
INSERT INTO "public"."t_agent_tag" ("id", "create_time", "update_time", "deleted", "creator", "updater", "name", "type",
                                    "category", "description", "use_count")
VALUES (21, '2025-03-14 10:21:07.720279', '2025-03-14 10:21:07.720279', 0, 'root', 'root', '偏执', 1, '2', '', 0);
INSERT INTO "public"."t_agent_tag" ("id", "create_time", "update_time", "deleted", "creator", "updater", "name", "type",
                                    "category", "description", "use_count")
VALUES (22, '2025-03-14 10:21:07.720884', '2025-03-14 10:21:07.720884', 0, 'root', 'root', '清冷', 1, '2', '', 0);
INSERT INTO "public"."t_agent_tag" ("id", "create_time", "update_time", "deleted", "creator", "updater", "name", "type",
                                    "category", "description", "use_count")
VALUES (23, '2025-03-14 10:21:07.721459', '2025-03-14 10:21:07.721459', 0, 'root', 'root', '善良', 1, '2', '', 0);
INSERT INTO "public"."t_agent_tag" ("id", "create_time", "update_time", "deleted", "creator", "updater", "name", "type",
                                    "category", "description", "use_count")
VALUES (24, '2025-03-14 10:21:07.722123', '2025-03-14 10:21:07.722123', 0, 'root', 'root', '温柔', 1, '2', '', 0);
INSERT INTO "public"."t_agent_tag" ("id", "create_time", "update_time", "deleted", "creator", "updater", "name", "type",
                                    "category", "description", "use_count")
VALUES (25, '2025-03-14 10:21:07.722679', '2025-03-14 10:21:07.722679', 0, 'root', 'root', '毒蛇', 1, '2', '', 0);
INSERT INTO "public"."t_agent_tag" ("id", "create_time", "update_time", "deleted", "creator", "updater", "name", "type",
                                    "category", "description", "use_count")
VALUES (26, '2025-03-14 10:21:07.723239', '2025-03-14 10:21:07.723239', 0, 'root', 'root', '病娇', 1, '2', '', 0);
INSERT INTO "public"."t_agent_tag" ("id", "create_time", "update_time", "deleted", "creator", "updater", "name", "type",
                                    "category", "description", "use_count")
VALUES (27, '2025-03-14 10:21:07.72374', '2025-03-14 10:21:07.72374', 0, 'root', 'root', '傲娇', 1, '2', '', 0);
INSERT INTO "public"."t_agent_tag" ("id", "create_time", "update_time", "deleted", "creator", "updater", "name", "type",
                                    "category", "description", "use_count")
VALUES (28, '2025-03-14 10:21:07.72426', '2025-03-14 10:21:07.72426', 0, 'root', 'root', '高冷', 1, '2', '', 0);
INSERT INTO "public"."t_agent_tag" ("id", "create_time", "update_time", "deleted", "creator", "updater", "name", "type",
                                    "category", "description", "use_count")
VALUES (29, '2025-03-14 10:21:07.72484', '2025-03-14 10:21:07.72484', 0, 'root', 'root', '沉稳', 1, '2', '', 0);
INSERT INTO "public"."t_agent_tag" ("id", "create_time", "update_time", "deleted", "creator", "updater", "name", "type",
                                    "category", "description", "use_count")
VALUES (30, '2025-03-14 10:21:07.725381', '2025-03-14 10:21:07.725381', 0, 'root', 'root', '冷酷', 1, '2', '', 0);
INSERT INTO "public"."t_agent_tag" ("id", "create_time", "update_time", "deleted", "creator", "updater", "name", "type",
                                    "category", "description", "use_count")
VALUES (31, '2025-03-14 10:21:07.725943', '2025-03-14 10:21:07.725943', 0, 'root', 'root', '禁欲', 1, '2', '', 0);
INSERT INTO "public"."t_agent_tag" ("id", "create_time", "update_time", "deleted", "creator", "updater", "name", "type",
                                    "category", "description", "use_count")
VALUES (32, '2025-03-14 10:21:07.72652', '2025-03-14 10:21:07.72652', 0, 'root', 'root', '疯批', 1, '2', '', 0);
INSERT INTO "public"."t_agent_tag" ("id", "create_time", "update_time", "deleted", "creator", "updater", "name", "type",
                                    "category", "description", "use_count")
VALUES (33, '2025-03-14 10:21:07.72711', '2025-03-14 10:21:07.72711', 0, 'root', 'root', '忠诚', 1, '2', '', 0);
INSERT INTO "public"."t_agent_tag" ("id", "create_time", "update_time", "deleted", "creator", "updater", "name", "type",
                                    "category", "description", "use_count")
VALUES (34, '2025-03-14 10:21:07.727597', '2025-03-14 10:21:07.727597', 0, 'root', 'root', '张扬', 1, '2', '', 0);
INSERT INTO "public"."t_agent_tag" ("id", "create_time", "update_time", "deleted", "creator", "updater", "name", "type",
                                    "category", "description", "use_count")
VALUES (35, '2025-03-14 10:21:07.728163', '2025-03-14 10:21:07.728163', 0, 'root', 'root', '矜贵', 1, '2', '', 0);
INSERT INTO "public"."t_agent_tag" ("id", "create_time", "update_time", "deleted", "creator", "updater", "name", "type",
                                    "category", "description", "use_count")
VALUES (36, '2025-03-14 10:21:07.728752', '2025-03-14 10:21:07.728752', 0, 'root', 'root', '桀骜不驯', 1, '2', '', 0);
INSERT INTO "public"."t_agent_tag" ("id", "create_time", "update_time", "deleted", "creator", "updater", "name", "type",
                                    "category", "description", "use_count")
VALUES (37, '2025-03-14 10:21:07.729311', '2025-03-14 10:21:07.729311', 0, 'root', 'root', '高岭之花', 1, '2', '', 0);
INSERT INTO "public"."t_agent_tag" ("id", "create_time", "update_time", "deleted", "creator", "updater", "name", "type",
                                    "category", "description", "use_count")
VALUES (38, '2025-03-14 10:21:07.729834', '2025-03-14 10:21:07.729834', 0, 'root', 'root', '凉薄', 1, '2', '', 0);
INSERT INTO "public"."t_agent_tag" ("id", "create_time", "update_time", "deleted", "creator", "updater", "name", "type",
                                    "category", "description", "use_count")
VALUES (39, '2025-03-14 10:21:07.730872', '2025-03-14 10:21:07.730872', 0, 'root', 'root', '傲慢', 1, '2', '', 0);

--智能体表的创建
DROP TABLE IF EXISTS "public"."t_agent";
DROP SEQUENCE IF EXISTS t_agent_id_seq;

-- 创建序列
CREATE SEQUENCE t_agent_id_seq START 1 CACHE 1;
-- Create agent table
CREATE TABLE t_agent
(
    -- Base entity fields
    id                    BIGINT PRIMARY KEY    DEFAULT NEXTVAL('t_agent_id_seq'::regclass),
    create_time           TIMESTAMP             DEFAULT CURRENT_TIMESTAMP,
    update_time           TIMESTAMP             DEFAULT CURRENT_TIMESTAMP,
    deleted               INTEGER               DEFAULT 0,
    creator               VARCHAR(255),
    updater               VARCHAR(255),

    -- Agent specific fields
    name                  VARCHAR(255) NOT NULL,
    type                  INTEGER      NOT NULL,
    identity              VARCHAR(255),
    gender                INTEGER,
    avatar_url            VARCHAR(1024),
    bg_url                VARCHAR(1024),
    bg_thumbnail_url      VARCHAR(1024),
    introduction          TEXT,
    security_check_result text                  DEFAULT '',
    is_public             INTEGER      NOT NULL DEFAULT 1,
    status                INTEGER      NOT NULL DEFAULT 0,
    shelf_status          INTEGER      NOT NULL DEFAULT 1,
    popularity            INTEGER               DEFAULT 0,
    remote_bot_id         VARCHAR(255),
    profile               JSONB,
    platform              INTEGER               DEFAULT 1,
    recommend_idx         INTEGER               DEFAULT 1,
    model                 VARCHAR(255)          DEFAULT ''
);
-- Add table comments
COMMENT ON TABLE t_agent IS '智能体表';
COMMENT ON COLUMN t_agent.id IS '主键ID';
COMMENT ON COLUMN t_agent.create_time IS '创建时间';
COMMENT ON COLUMN t_agent.update_time IS '更新时间';
COMMENT ON COLUMN t_agent.deleted IS '删除标记(0:未删除,1:已删除)';
COMMENT ON COLUMN t_agent.creator IS '创建者';
COMMENT ON COLUMN t_agent.updater IS '更新者';
COMMENT ON COLUMN t_agent.name IS '智能体名称';
COMMENT ON COLUMN t_agent.type IS '智能体类型：1-聊天智能体，2-大模型智能体';
COMMENT ON COLUMN t_agent.identity IS '智能体身份';
COMMENT ON COLUMN t_agent.gender IS '性别：1-男，2-女，3-其他';
COMMENT ON COLUMN t_agent.avatar_url IS '头像URL';
COMMENT ON COLUMN t_agent.bg_url IS '背景图片URL';
COMMENT ON COLUMN t_agent.bg_thumbnail_url IS '背景缩略图URL';
COMMENT ON COLUMN t_agent.introduction IS '简介';
COMMENT ON COLUMN t_agent.is_public IS '是否公开：1-私密，2-公开';
COMMENT ON COLUMN t_agent.status IS '智能体状态：0-创建中，1-正常，2-审核中，3-禁用';
COMMENT ON COLUMN t_agent.shelf_status IS '上架状态：1-上架，2-下架';
COMMENT ON COLUMN t_agent.popularity IS '热度值';
COMMENT ON COLUMN t_agent.remote_bot_id IS 'Coze平台智能体ID';
COMMENT ON COLUMN t_agent.profile IS '智能体配置信息';
COMMENT ON COLUMN t_agent.security_check_result IS '字段安全检查';
COMMENT ON COLUMN t_agent.platform IS '平台：1-coze，2-其他';
COMMENT ON COLUMN t_agent.model IS '模型名称';
--智能体和智能体标签的关联语句
DROP TABLE IF EXISTS "public"."t_agent_tag_relation";
DROP SEQUENCE IF EXISTS t_agent_tag_relation_id_seq;
-- Create sequence for agent tag relation
CREATE SEQUENCE t_agent_tag_relation_id_seq START 1 CACHE 1;
-- Create Agent Tag Relation table
CREATE TABLE t_agent_tag_relation
(
    id       BIGINT PRIMARY KEY DEFAULT NEXTVAL('t_agent_tag_relation_id_seq'::regclass),
    agent_id BIGINT NOT NULL,
    tag_id   BIGINT NOT NULL
);
-- Add table comments
COMMENT ON TABLE t_agent_tag_relation IS '智能体标签关联表';
COMMENT ON COLUMN t_agent_tag_relation.id IS '主键ID';
COMMENT ON COLUMN t_agent_tag_relation.agent_id IS '智能体ID';
COMMENT ON COLUMN t_agent_tag_relation.tag_id IS '标签ID';
--智能体的收藏表
DROP TABLE IF EXISTS "public"."t_agent_favorite";
DROP SEQUENCE IF EXISTS t_agent_favorite_id_seq;
-- 创建序列
CREATE SEQUENCE t_agent_favorite_id_seq START 1 CACHE 1;
-- 创建智能体收藏表
CREATE TABLE t_agent_favorite
(
    -- BaseEntity字段
    id          BIGINT PRIMARY KEY DEFAULT NEXTVAL('t_agent_favorite_id_seq'::regclass),
    create_time TIMESTAMP          DEFAULT CURRENT_TIMESTAMP,
    deleted     INTEGER            DEFAULT 0,
    agent_id    BIGINT       NOT NULL,
    username    VARCHAR(255) NOT NULL,
    status      INTEGER            DEFAULT 1
);
-- 添加表注释
COMMENT ON TABLE t_agent_favorite IS '智能体收藏表';
COMMENT ON COLUMN t_agent_favorite.id IS '主键ID';
COMMENT ON COLUMN t_agent_favorite.create_time IS '收藏时间';
COMMENT ON COLUMN t_agent_favorite.deleted IS '删除标记(0:未删除,1:已删除)';
COMMENT ON COLUMN t_agent_favorite.agent_id IS '智能体ID';
COMMENT ON COLUMN t_agent_favorite.username IS '用户名';
COMMENT ON COLUMN t_agent_favorite.status IS '收藏状态：1-已收藏，0-取消收藏';
-- 智能体点赞表
DROP TABLE IF EXISTS "public"."t_agent_like";
DROP SEQUENCE IF EXISTS t_agent_like_id_seq;
-- 创建序列
CREATE SEQUENCE t_agent_like_id_seq START 1 CACHE 1;
-- 创建表
CREATE TABLE t_agent_like
(
    -- BaseEntity字段
    id          BIGINT PRIMARY KEY DEFAULT NEXTVAL('t_agent_like_id_seq'::regclass),
    create_time TIMESTAMP          DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP          DEFAULT CURRENT_TIMESTAMP,
    deleted     INTEGER            DEFAULT 0,
    -- AgentLikeEntity特有字段
    agent_id    BIGINT      NOT NULL,
    username    VARCHAR(64) NOT NULL,
    status      INTEGER            DEFAULT 1
);
-- 添加表注释
COMMENT ON TABLE t_agent_like IS '智能体点赞表';
COMMENT ON COLUMN t_agent_like.id IS '主键ID';
COMMENT ON COLUMN t_agent_like.create_time IS '创建时间';
COMMENT ON COLUMN t_agent_like.update_time IS '更新时间';
COMMENT ON COLUMN t_agent_like.deleted IS '删除标记(0:未删除,1:已删除)';
COMMENT ON COLUMN t_agent_like.agent_id IS '智能体ID';
COMMENT ON COLUMN t_agent_like.username IS '用户名';
COMMENT ON COLUMN t_agent_like.status IS '状态(1:已点赞,0:取消点赞)';
-- 创建索引
CREATE INDEX idx_agent_like_agent_id ON t_agent_like (agent_id);
CREATE INDEX idx_agent_like_username ON t_agent_like (username);
CREATE UNIQUE INDEX uk_agent_like_agent_username ON t_agent_like (agent_id, username) WHERE deleted = 0;

-- 智能体草稿表
DROP TABLE IF EXISTS "public"."t_agent_draft";
DROP SEQUENCE IF EXISTS t_agent_draft_id_seq;

-- 创建序列
CREATE SEQUENCE t_agent_draft_id_seq START 1 CACHE 1;

-- 创建表
CREATE TABLE t_agent_draft
(
    id          BIGINT PRIMARY KEY DEFAULT NEXTVAL('t_agent_draft_id_seq'::regclass),
    name        VARCHAR(255),
    username    VARCHAR(255) NOT NULL, -- 用户名
    update_time TIMESTAMP          DEFAULT CURRENT_TIMESTAMP,
    content     TEXT
);

-- 添加表注释
COMMENT ON TABLE t_agent_draft IS '智能体草稿表';
COMMENT ON COLUMN t_agent_draft.id IS '主键ID';
COMMENT ON COLUMN t_agent_draft.name IS '草稿名称';
COMMENT ON COLUMN t_agent_draft.content IS '草稿内容';

-- 智能体评论表
DROP TABLE IF EXISTS "public"."t_agent_comment";
DROP SEQUENCE IF EXISTS t_agent_comment_id_seq;
-- 创建序列
CREATE SEQUENCE t_agent_comment_id_seq START 1 CACHE 1;
-- 创建表
CREATE TABLE t_agent_comment
(
    -- 主键字段
    id                BIGINT PRIMARY KEY DEFAULT NEXTVAL('t_agent_comment_id_seq'::regclass),

    -- 业务字段
    agent_id          BIGINT       NOT NULL,
    username          VARCHAR(255) NOT NULL,
    content           TEXT         NOT NULL,
    parent_id         BIGINT,
    reply_to_username VARCHAR(255),
    status            INTEGER            DEFAULT 1,
    images            TEXT,
    messages          TEXT,
    child_cnt         INTEGER            DEFAULT 0,
    like_cnt          INTEGER            DEFAULT 0,
    last_child_id     BIGINT,

    -- 基础字段
    deleted           INTEGER            DEFAULT 0,
    create_time       TIMESTAMP          DEFAULT CURRENT_TIMESTAMP,
    update_time       TIMESTAMP          DEFAULT CURRENT_TIMESTAMP
);
-- 添加表注释
COMMENT ON TABLE t_agent_comment IS '智能体评论表';
COMMENT ON COLUMN t_agent_comment.id IS '主键ID';
COMMENT ON COLUMN t_agent_comment.agent_id IS '智能体ID';
COMMENT ON COLUMN t_agent_comment.username IS '用户名';
COMMENT ON COLUMN t_agent_comment.content IS '评论内容';
COMMENT ON COLUMN t_agent_comment.parent_id IS '父评论ID，顶级评论为null';
COMMENT ON COLUMN t_agent_comment.reply_to_username IS '被回复的用户名（仅在回复评论时有值）';
COMMENT ON COLUMN t_agent_comment.status IS '评论状态：1-正常，0-隐藏';
COMMENT ON COLUMN t_agent_comment.images IS '评论图片列表（JSON格式存储，最多9张）';
COMMENT ON COLUMN t_agent_comment.messages IS '评论消息列表（JSON格式存储）';
COMMENT ON COLUMN t_agent_comment.child_cnt IS '子评论数量（数据库字段，用于快速查询）';
COMMENT ON COLUMN t_agent_comment.like_cnt IS '点赞数量（数据库字段，用于快速查询）';
COMMENT ON COLUMN t_agent_comment.last_child_id IS '最新子评论ID（数据库字段，用于快速查询）';
COMMENT ON COLUMN t_agent_comment.deleted IS '删除标记(0:未删除,1:已删除)';
COMMENT ON COLUMN t_agent_comment.create_time IS '创建时间';
COMMENT ON COLUMN t_agent_comment.update_time IS '更新时间';
-- 创建索引
CREATE INDEX idx_agent_comment_agent_id ON t_agent_comment (agent_id);
CREATE INDEX idx_agent_comment_username ON t_agent_comment (username);
CREATE INDEX idx_agent_comment_parent_id ON t_agent_comment (parent_id);
CREATE INDEX idx_agent_comment_create_time ON t_agent_comment (create_time);

-- 智能体评论点赞表
DROP TABLE IF EXISTS "public"."t_agent_comment_like";
DROP SEQUENCE IF EXISTS t_agent_comment_like_id_seq;

-- 创建序列
CREATE SEQUENCE t_agent_comment_like_id_seq START 1 CACHE 1;

-- 创建表
CREATE TABLE t_agent_comment_like
(
    -- 主键字段
    id          BIGINT PRIMARY KEY DEFAULT NEXTVAL('t_agent_comment_like_id_seq'::regclass),

    -- 业务字段
    comment_id  BIGINT       NOT NULL,
    username    VARCHAR(255) NOT NULL,
    status      INTEGER            DEFAULT 1,

    -- 基础字段
    deleted     INTEGER            DEFAULT 0,
    create_time TIMESTAMP          DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP          DEFAULT CURRENT_TIMESTAMP
);

-- 添加表注释
COMMENT ON TABLE t_agent_comment_like IS '智能体评论点赞表';
COMMENT ON COLUMN t_agent_comment_like.id IS '主键ID';
COMMENT ON COLUMN t_agent_comment_like.comment_id IS '评论ID';
COMMENT ON COLUMN t_agent_comment_like.username IS '点赞用户名';
COMMENT ON COLUMN t_agent_comment_like.status IS '点赞状态：1-已点赞，0-取消点赞';
COMMENT ON COLUMN t_agent_comment_like.deleted IS '删除标记(0:未删除,1:已删除)';
COMMENT ON COLUMN t_agent_comment_like.create_time IS '创建时间';
COMMENT ON COLUMN t_agent_comment_like.update_time IS '更新时间';

-- 创建索引
CREATE INDEX idx_agent_comment_like_comment_id ON t_agent_comment_like (comment_id);
CREATE INDEX idx_agent_comment_like_username ON t_agent_comment_like (username);
CREATE UNIQUE INDEX uk_agent_comment_like_comment_username ON t_agent_comment_like (comment_id, username) WHERE deleted = 0;

-- 智能体类型表
-- 删除已存在的表和序列
DROP TABLE IF EXISTS "public"."t_agent_type";
DROP SEQUENCE IF EXISTS t_agent_type_id_seq;
-- 创建序列
CREATE SEQUENCE t_agent_type_id_seq START 1 CACHE 1;
-- 创建表
CREATE TABLE t_agent_type
(
    -- BaseEntity字段
    id          BIGINT PRIMARY KEY DEFAULT NEXTVAL('t_agent_type_id_seq'::regclass),
    create_time TIMESTAMP          DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP          DEFAULT CURRENT_TIMESTAMP,
    deleted     INTEGER            DEFAULT 0,
    creator     VARCHAR(255),
    updater     VARCHAR(255),
    -- AgentTypeEntity特有字段
    name        VARCHAR(255) NOT NULL,
    description TEXT,
    use_count   INTEGER            DEFAULT 0
);
-- 添加表注释
COMMENT ON TABLE t_agent_type IS '智能体类型表';
COMMENT ON COLUMN t_agent_type.id IS '主键ID';
COMMENT ON COLUMN t_agent_type.create_time IS '创建时间';
COMMENT ON COLUMN t_agent_type.update_time IS '更新时间';
COMMENT ON COLUMN t_agent_type.deleted IS '删除标记(0:未删除,1:已删除)';
COMMENT ON COLUMN t_agent_type.creator IS '创建者';
COMMENT ON COLUMN t_agent_type.updater IS '更新者';
COMMENT ON COLUMN t_agent_type.name IS '类型名称';
COMMENT ON COLUMN t_agent_type.description IS '类型描述';
COMMENT ON COLUMN t_agent_type.use_count IS '使用次数';
INSERT INTO "public"."t_agent_type" ("id", "create_time", "update_time", "deleted", "creator", "updater", "name",
                                     "description", "use_count")
VALUES (2, '2025-03-17 13:45:13.846255', '2025-03-17 13:45:13.846255', 0, 'root', 'root', '小说人物', '', 0);
INSERT INTO "public"."t_agent_type" ("id", "create_time", "update_time", "deleted", "creator", "updater", "name",
                                     "description", "use_count")
VALUES (3, '2025-03-17 13:45:13.847849', '2025-03-17 13:45:13.847849', 0, 'root', 'root', '动漫人物', '', 0);
INSERT INTO "public"."t_agent_type" ("id", "create_time", "update_time", "deleted", "creator", "updater", "name",
                                     "description", "use_count")
VALUES (4, '2025-03-17 13:45:13.848206', '2025-03-17 13:45:13.848206', 0, 'root', 'root', '原创', '', 0);
INSERT INTO "public"."t_agent_type" ("id", "create_time", "update_time", "deleted", "creator", "updater", "name",
                                     "description", "use_count")
VALUES (5, '2025-03-17 13:45:13.848531', '2025-03-17 13:45:13.848531', 0, 'root', 'root', '游戏人物', '', 0);
INSERT INTO "public"."t_agent_type" ("id", "create_time", "update_time", "deleted", "creator", "updater", "name",
                                     "description", "use_count")
VALUES (6, '2025-03-17 13:45:13.848963', '2025-03-17 13:45:13.848963', 0, 'root', 'root', '历史人物', '', 0);
INSERT INTO "public"."t_agent_type" ("id", "create_time", "update_time", "deleted", "creator", "updater", "name",
                                     "description", "use_count")
VALUES (7, '2025-03-17 13:45:13.849317', '2025-03-17 13:45:13.849317', 0, 'root', 'root', '情感陪伴', '', 0);
INSERT INTO "public"."t_agent_type" ("id", "create_time", "update_time", "deleted", "creator", "updater", "name",
                                     "description", "use_count")
VALUES (8, '2025-03-17 13:45:13.849706', '2025-03-17 13:45:13.849706', 0, 'root', 'root', 'AI工具', '', 0);
INSERT INTO "public"."t_agent_type" ("id", "create_time", "update_time", "deleted", "creator", "updater", "name",
                                     "description", "use_count")
VALUES (1, '2025-03-17 13:42:14.168394', '2025-03-17 13:42:14.168394', 0, 'root', 'root', '影视人物', '', 0);
-- 删除已存在的表和序列
DROP TABLE IF EXISTS "public"."t_agent_usage_record";
DROP SEQUENCE IF EXISTS t_agent_usage_record_id_seq;
-- 创建序列
CREATE SEQUENCE t_agent_usage_record_id_seq START 1 CACHE 1;
-- 创建智能体使用记录表
CREATE TABLE t_agent_usage_record
(
    -- 主键ID
    id          BIGINT PRIMARY KEY DEFAULT NEXTVAL('t_agent_usage_record_id_seq'::regclass),

    -- 智能体ID
    agent_id    BIGINT       NOT NULL,

    -- 用户名
    username    VARCHAR(255) NOT NULL,

    -- 使用次数
    use_count   INTEGER            DEFAULT 0,

    -- 更新时间
    update_time TIMESTAMP          DEFAULT CURRENT_TIMESTAMP,

    -- 删除标记
    deleted     INTEGER            DEFAULT 0,

    -- 状态：1-正常，0-禁用
    status      INTEGER            DEFAULT 1
);
-- 添加表注释
COMMENT ON TABLE t_agent_usage_record IS '智能体使用记录表';
COMMENT ON COLUMN t_agent_usage_record.id IS '主键ID';
COMMENT ON COLUMN t_agent_usage_record.agent_id IS '智能体ID';
COMMENT ON COLUMN t_agent_usage_record.username IS '用户名';
COMMENT ON COLUMN t_agent_usage_record.use_count IS '使用次数';
COMMENT ON COLUMN t_agent_usage_record.update_time IS '更新时间';
COMMENT ON COLUMN t_agent_usage_record.deleted IS '删除标记(0:未删除,1:已删除)';
COMMENT ON COLUMN t_agent_usage_record.status IS '状态(1:正常,0:禁用)';

-- 智能体设置表
DROP TABLE IF EXISTS "public"."t_my_common_agent_setting";
DROP SEQUENCE IF EXISTS t_my_common_agent_setting_id_seq;

-- 创建序列
CREATE SEQUENCE t_my_common_agent_setting_id_seq START 1 CACHE 1;

-- 创建智能体设置表
CREATE TABLE t_my_common_agent_setting
(
    -- BaseEntity字段
    id              BIGINT PRIMARY KEY DEFAULT NEXTVAL('t_my_common_agent_setting_id_seq'::regclass),

    -- AgentSettingEntity特有字段
    username        VARCHAR(255),
    my_nick_name    VARCHAR(255),
    my_gender       VARCHAR(255),
    my_identity     VARCHAR(255),
    auto_play_voice BOOLEAN            DEFAULT false
);

-- 添加表注释
COMMENT ON TABLE t_my_common_agent_setting IS '智能体设置表';
COMMENT ON COLUMN t_my_common_agent_setting.id IS '主键ID';
COMMENT ON COLUMN t_my_common_agent_setting.username IS '用户名';
COMMENT ON COLUMN t_my_common_agent_setting.my_nick_name IS '智能体对我称呼';
COMMENT ON COLUMN t_my_common_agent_setting.my_gender IS '智能体对我的性别认知';
COMMENT ON COLUMN t_my_common_agent_setting.my_identity IS '智能体对我的身份认知';
COMMENT ON COLUMN t_my_common_agent_setting.auto_play_voice IS '是否自动播放语音消息';

-- 用户喜好设置表
DROP TABLE IF EXISTS "public"."t_user_agent_preference";
DROP SEQUENCE IF EXISTS t_user_agent_preference_id_seq;

-- 创建序列
CREATE SEQUENCE t_user_agent_preference_id_seq START 1 CACHE 1;

-- 创建用户喜好设置表
CREATE TABLE t_user_agent_preference
(
    -- BaseEntity字段
    id                    BIGINT PRIMARY KEY DEFAULT NEXTVAL('t_user_agent_preference_id_seq'::regclass),
    create_time           TIMESTAMP          DEFAULT CURRENT_TIMESTAMP,
    update_time           TIMESTAMP          DEFAULT CURRENT_TIMESTAMP,
    deleted               INTEGER            DEFAULT 0,
    creator               VARCHAR(255),
    updater               VARCHAR(255),

    -- UserPreferenceEntity特有字段
    username              VARCHAR(255) NOT NULL,

    -- 喜好的智能体性别：1-男，2-女，3-男女都可以
    preferred_gender      INTEGER            DEFAULT 3,

    -- 喜好的智能体类型，多选，使用JSON数组存储
    preferred_agent_types TEXT               DEFAULT '[]'
);

-- 添加表注释
COMMENT ON TABLE t_user_agent_preference IS '用户喜好设置表';
COMMENT ON COLUMN t_user_agent_preference.id IS '主键ID';
COMMENT ON COLUMN t_user_agent_preference.create_time IS '创建时间';
COMMENT ON COLUMN t_user_agent_preference.update_time IS '更新时间';
COMMENT ON COLUMN t_user_agent_preference.deleted IS '删除标记(0:未删除,1:已删除)';
COMMENT ON COLUMN t_user_agent_preference.creator IS '创建者';
COMMENT ON COLUMN t_user_agent_preference.updater IS '更新者';
COMMENT ON COLUMN t_user_agent_preference.username IS '用户名';
COMMENT ON COLUMN t_user_agent_preference.preferred_gender IS '喜好的智能体性别：1-男，2-女，3-男女都可以';
COMMENT ON COLUMN t_user_agent_preference.preferred_agent_types IS '喜好的智能体类型，多选，使用JSON数组存储';

-- 创建用户名索引
CREATE INDEX idx_user_preference_username ON t_user_agent_preference (username);

-- 用户对智能体兴趣关系表
DROP TABLE IF EXISTS "public"."t_agent_interest";
DROP SEQUENCE IF EXISTS t_agent_interest_id_seq;

-- 创建序列
CREATE SEQUENCE t_agent_interest_id_seq START 1 CACHE 1;

-- 创建用户对智能体兴趣关系表
CREATE TABLE t_agent_interest
(
    -- 主键字段
    id             BIGINT PRIMARY KEY DEFAULT NEXTVAL('t_agent_interest_id_seq'::regclass),
    update_time    TIMESTAMP          DEFAULT CURRENT_TIMESTAMP,

    -- 业务字段
    username       VARCHAR(255) NOT NULL,
    agent_id       BIGINT       NOT NULL,
    interest_level INTEGER,
    source         INTEGER,
    marked         INTEGER            DEFAULT 0
);

-- 添加表注释
COMMENT ON TABLE t_agent_interest IS '用户对智能体兴趣关系表';
COMMENT ON COLUMN t_agent_interest.id IS '主键ID';
COMMENT ON COLUMN t_agent_interest.update_time IS '更新时间';
COMMENT ON COLUMN t_agent_interest.username IS '用户名';
COMMENT ON COLUMN t_agent_interest.agent_id IS '智能体ID';
COMMENT ON COLUMN t_agent_interest.interest_level IS '兴趣程度：1-轻度兴趣，2-中度兴趣，3-高度兴趣';
COMMENT ON COLUMN t_agent_interest.source IS '来源：1-浏览, 2-搜索, 3-推荐, 4-历史行为';
COMMENT ON COLUMN t_agent_interest.marked IS '标记：0-未标记，1-已标记';

-- 创建索引
CREATE INDEX idx_agent_interest_username ON t_agent_interest (username);
CREATE INDEX idx_agent_interest_agent_id ON t_agent_interest (agent_id);

-- 智能体热搜榜表
DROP TABLE IF EXISTS "public"."t_agent_search_rank";
DROP SEQUENCE IF EXISTS t_agent_search_rank_id_seq;

-- 创建序列
CREATE SEQUENCE t_agent_search_rank_id_seq START 1 CACHE 1;

-- 创建智能体热搜榜表
CREATE TABLE t_agent_search_rank
(
    -- 主键ID
    id           BIGINT PRIMARY KEY DEFAULT NEXTVAL('t_agent_search_rank_id_seq'::regclass),

    -- 更新时间
    update_time  TIMESTAMP          DEFAULT CURRENT_TIMESTAMP,

    -- 搜索关键词
    keyword      VARCHAR(255) NOT NULL,

    -- 搜索次数
    search_count INTEGER            DEFAULT 0,

    -- 搜索用户名
    username     VARCHAR(255)
);

-- 添加表注释
COMMENT ON TABLE t_agent_search_rank IS '智能体热搜榜表';
COMMENT ON COLUMN t_agent_search_rank.id IS '主键ID';
COMMENT ON COLUMN t_agent_search_rank.update_time IS '更新时间';
COMMENT ON COLUMN t_agent_search_rank.keyword IS '搜索关键词';
COMMENT ON COLUMN t_agent_search_rank.search_count IS '搜索次数';
COMMENT ON COLUMN t_agent_search_rank.username IS '搜索用户名';

-- 创建索引
CREATE INDEX idx_agent_search_rank_keyword ON t_agent_search_rank (keyword);
CREATE INDEX idx_agent_search_rank_username ON t_agent_search_rank (username);
-- 删除已存在的表和序列
DROP TABLE IF EXISTS "public"."t_agent_daily_usage_record";
DROP SEQUENCE IF EXISTS t_agent_daily_usage_record_id_seq;

-- 创建序列
CREATE SEQUENCE t_agent_daily_usage_record_id_seq START 1 CACHE 1;

-- 创建用户每日使用智能体记录表
CREATE TABLE t_agent_daily_usage_record
(
    -- 主键ID
    id          BIGINT PRIMARY KEY DEFAULT NEXTVAL('t_agent_daily_usage_record_id_seq'::regclass),

    -- 智能体ID
    agent_id    BIGINT       NOT NULL,

    -- 用户名
    username    VARCHAR(255) NOT NULL,

    -- 使用日期
    use_date    DATE         NOT NULL,

    -- 当日使用次数
    use_count   INTEGER            DEFAULT 1,

    -- 创建时间
    create_time TIMESTAMP          DEFAULT CURRENT_TIMESTAMP,

    -- 更新时间
    update_time TIMESTAMP          DEFAULT CURRENT_TIMESTAMP,

    -- 删除标记
    deleted     INTEGER            DEFAULT 0,

    -- 状态：1-正常，0-禁用
    status      INTEGER            DEFAULT 1
);

-- 添加表注释
COMMENT ON TABLE t_agent_daily_usage_record IS '用户每日使用智能体记录表';
COMMENT ON COLUMN t_agent_daily_usage_record.id IS '主键ID';
COMMENT ON COLUMN t_agent_daily_usage_record.agent_id IS '智能体ID';
COMMENT ON COLUMN t_agent_daily_usage_record.username IS '用户名';
COMMENT ON COLUMN t_agent_daily_usage_record.use_date IS '使用日期';
COMMENT ON COLUMN t_agent_daily_usage_record.use_count IS '当日使用次数';
COMMENT ON COLUMN t_agent_daily_usage_record.create_time IS '创建时间';
COMMENT ON COLUMN t_agent_daily_usage_record.update_time IS '更新时间';
COMMENT ON COLUMN t_agent_daily_usage_record.deleted IS '删除标记(0:未删除,1:已删除)';
COMMENT ON COLUMN t_agent_daily_usage_record.status IS '状态(1:正常,0:禁用)';

-- 创建索引
CREATE INDEX idx_agent_daily_usage_record_agent_id ON t_agent_daily_usage_record (agent_id);
CREATE INDEX idx_agent_daily_usage_record_username ON t_agent_daily_usage_record (username);
CREATE INDEX idx_agent_daily_usage_record_use_date ON t_agent_daily_usage_record (use_date);
CREATE UNIQUE INDEX uk_agent_daily_usage_agent_username_date ON t_agent_daily_usage_record (agent_id, username, use_date) WHERE deleted = 0;

-- 图像检查处理表
-- 删除已存在的表和序列
DROP TABLE IF EXISTS "public"."image_check_process";
DROP SEQUENCE IF EXISTS image_check_process_id_seq;

-- 创建序列
CREATE SEQUENCE image_check_process_id_seq START 1 CACHE 1;

-- 创建表
CREATE TABLE image_check_process
(
    -- 主键字段
    id           BIGINT PRIMARY KEY DEFAULT NEXTVAL('image_check_process_id_seq'::regclass),

    -- 实体类特有字段
    image_path   VARCHAR(512),
    check_id     VARCHAR(255),
    check_status VARCHAR(255),
    check_time   TIMESTAMP,
    trace_id     VARCHAR(255)
);

-- 添加表注释
COMMENT ON TABLE image_check_process IS '图像检查处理表';
COMMENT ON COLUMN image_check_process.id IS '主键ID';
COMMENT ON COLUMN image_check_process.image_path IS '图像路径';
COMMENT ON COLUMN image_check_process.check_id IS '检查ID';
COMMENT ON COLUMN image_check_process.check_status IS '检查状态';
COMMENT ON COLUMN image_check_process.check_time IS '检查时间';
COMMENT ON COLUMN image_check_process.trace_id IS '追踪ID';