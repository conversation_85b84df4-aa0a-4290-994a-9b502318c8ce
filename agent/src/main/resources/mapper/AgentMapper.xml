<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gw.agent.mapper.sql.AgentMapper">
    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.gw.agent.entity.AgentEntity">
        <id column="id" property="id"/>
        <result column="creator" property="creator"/>
        <result column="updater" property="updater"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="deleted" property="deleted"/>
        <result column="name" property="name"/>
        <result column="popularity" property="popularity"/>
        <result column="introduction" property="introduction"/>
        <result column="is_public" property="isPublic"/>
        <result column="status" property="status"/>
        <result column="type_id" property="typeId"/>
        <result column="gender" property="gender"/>
        <result column="avatar_url" property="avatarUrl"/>
        <result column="bg_url" property="bgUrl"/>
        <result column="bg_thumbnail_url" property="bgThumbnailUrl"/>
        <result column="identity" property="identity"/>
        <result column="shelf_status" property="shelfStatus"/>
        <result column="shelf_reason" property="shelfReason"/>
        <result column="remote_bot_id" property="remoteBotId"/>
        <result column="platform" property="platform"/>
        <result column="recommend_idx" property="recommendIdx"/>
        <result column="security_check_result" property="securityCheckResult"/>
        <result column="profile" property="profile" typeHandler="com.gw.agent.handler.AgentProfileTypeHandler"/>
    </resultMap>
    <!-- 分页查询 -->
    <select id="page" resultMap="BaseResultMap">
        SELECT * FROM t_agent
        <where>
            deleted = 0
            <if test="query.keyword != null and query.keyword != ''">
                AND (name LIKE CONCAT('%', #{query.keyword}, '%')
                OR identity LIKE CONCAT('%', #{query.keyword}, '%')
                <if test="query.tagSearchIds != null and query.tagSearchIds.size() > 0">
                    OR id IN (
                    SELECT agent_id FROM t_agent_tag_relation
                    WHERE tag_id IN
                    <foreach collection="query.tagSearchIds" item="tagId" open="(" separator="," close=")">
                        #{tagId}
                    </foreach>
                    )
                </if>
                <if test="query.typeSearchId != null and query.typeSearchId > 0">
                    OR type_id = #{query.typeSearchId}
                </if>
                )
            </if>
            <choose>
                <when test="query.myPublicUserName != null and query.myPublicUserName != ''">
                    AND (creator = #{query.myPublicUserName} OR is_public = 2)
                </when>
                <otherwise>
                    <if test="query.username != null and query.username != ''">
                        AND creator = #{query.username}
                    </if>
                    <if test="query.isPublic != null and query.isPublic > 0">
                        AND is_public = #{query.isPublic}
                    </if>
                </otherwise>
            </choose>
            <if test="query.gender != null and query.gender > 0">
                AND gender = #{query.gender}
            </if>
            <if test="query.typeId != null and query.typeId > 0">
                AND type_id = #{query.typeId}
            </if>
            <if test="query.status != null and query.status > 0">
                AND status = #{query.status}
            </if>
            <if test="query.tagIds != null and query.tagIds.size() > 0">
                AND id IN (
                SELECT agent_id FROM t_agent_tag_relation
                WHERE tag_id IN
                <foreach collection="query.tagIds" item="tagId" open="(" separator="," close=")">
                    #{tagId}
                </foreach>
                )
            </if>
            <if test="query.shelfStatus != null and query.shelfStatus > 0">
                AND shelf_status = #{query.shelfStatus}
            </if>
            <if test="query.startDateTime != null">
                AND DATE(create_time) >= #{query.startDateTime}
            </if>
            <if test="query.endDateTime != null">
                AND DATE(create_time) &lt;= #{query.endDateTime}
            </if>
            <if test="query.creator != null and query.creator != ''">
                AND creator LIKE CONCAT('%', #{query.creator}, '%')
            </if>
        </where>
        ORDER BY
        <choose>
            <when test="orderField != null and orderField.key != null and orderField.key != ''">
                ${orderField.key}
                <if test="orderField.direction == 1">
                    ASC
                </if>
                <if test="orderField.direction != 1">
                    DESC
                </if>
            </when>
            <otherwise>
                update_time DESC
            </otherwise>
        </choose>
    </select>

    <!-- 分页查询，只返回ID列表 -->
    <select id="pageIds" resultType="java.lang.Long">
        SELECT id FROM t_agent
        <where>
            deleted = 0
            <if test="query.keyword != null and query.keyword != ''">
                AND (name LIKE CONCAT('%', #{query.keyword}, '%')
                OR identity LIKE CONCAT('%', #{query.keyword}, '%')
                <if test="query.tagSearchIds != null and query.tagSearchIds.size() > 0">
                    OR id IN (
                    SELECT agent_id FROM t_agent_tag_relation
                    WHERE tag_id IN
                    <foreach collection="query.tagSearchIds" item="tagId" open="(" separator="," close=")">
                        #{tagId}
                    </foreach>
                    )
                </if>
                <if test="query.typeSearchId != null and query.typeSearchId > 0">
                    OR type_id = #{query.typeSearchId}
                </if>
                )
            </if>
            <choose>
                <when test="query.myPublicUserName != null and query.myPublicUserName != ''">
                    AND (creator = #{query.myPublicUserName} OR is_public = 2)
                </when>
                <otherwise>
                    <if test="query.username != null and query.username != ''">
                        AND creator = #{query.username}
                    </if>
                    <if test="query.isPublic != null and query.isPublic > 0">
                        AND is_public = #{query.isPublic}
                    </if>
                </otherwise>
            </choose>
            <if test="query.gender != null and query.gender > 0">
                AND gender = #{query.gender}
            </if>
            <if test="query.typeId != null and query.typeId > 0">
                AND type_id = #{query.typeId}
            </if>
            <if test="query.status != null and query.status > 0">
                AND status = #{query.status}
            </if>
            <if test="query.tagIds != null and query.tagIds.size() > 0">
                AND id IN (
                SELECT agent_id FROM t_agent_tag_relation
                WHERE tag_id IN
                <foreach collection="query.tagIds" item="tagId" open="(" separator="," close=")">
                    #{tagId}
                </foreach>
                )
            </if>
            <if test="query.shelfStatus != null and query.shelfStatus > 0">
                AND shelf_status = #{query.shelfStatus}
            </if>
            <if test="query.creator != null and query.creator != ''">
                AND creator LIKE CONCAT('%', #{query.creator}, '%')
            </if>
            <if test="query.startDateTime != null">
                AND DATE(create_time) >= #{query.startDateTime}
            </if>
            <if test="query.endDateTime != null">
                AND DATE(create_time) &lt;= #{query.endDateTime}
            </if>
        </where>
        ORDER BY
        <choose>
            <when test="orderField != null and orderField.key != null and orderField.key != ''">
                ${orderField.key}
                <if test="orderField.direction == 1">
                    ASC
                </if>
                <if test="orderField.direction != 1">
                    DESC
                </if>
            </when>
            <otherwise>
                update_time DESC
            </otherwise>
        </choose>
    </select>

    <!-- 根据ID查询智能体 -->
    <select id="findById" resultMap="BaseResultMap">
        SELECT *
        FROM t_agent
        WHERE id = #{agentId}
          AND deleted = 0
        LIMIT 1
    </select>
    <select id="findFirstByExclusive" resultMap="BaseResultMap">
        SELECT *
        FROM t_agent
        WHERE is_public = 3
          AND deleted = 0
        LIMIT 1
    </select>
    <!-- 根据智能体ID列表查询智能体，并按匹配的标签数量排序 -->
    <select id="findByIdsWithTagCount" resultMap="BaseResultMap">
        SELECT a.*, COUNT(r.tag_id) as tag_match_count
        FROM t_agent a
        INNER JOIN t_agent_tag_relation r ON a.id = r.agent_id
        WHERE a.id IN
        <foreach collection="agentIds" item="agentId" open="(" separator="," close=")">
            #{agentId}
        </foreach>
        AND r.tag_id IN
        <foreach collection="tagIds" item="tagId" open="(" separator="," close=")">
            #{tagId}
        </foreach>
        AND a.is_public = 2
        AND a.status = 1
        AND a.deleted = 0
        GROUP BY a.id
        ORDER BY tag_match_count DESC, a.popularity DESC
    </select>

    <insert id="insert" parameterType="com.gw.agent.entity.AgentEntity" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO t_agent (creator, updater, create_time, update_time, deleted,
                             name, popularity, introduction, is_public, status, platform, recommend_idx,
                             type_id, gender, avatar_url, bg_url, bg_thumbnail_url, identity, shelf_status, profile)
        VALUES (#{creator}, #{updater}, #{createTime}, #{updateTime}, #{deleted},
                #{name}, #{popularity}, #{introduction}, #{isPublic}, #{status}, #{platform}, #{recommendIdx},
                #{typeId}, #{gender}, #{avatarUrl}, #{bgUrl}, #{bgThumbnailUrl}, #{identity}, #{shelfStatus},
                #{profile,typeHandler=com.gw.agent.handler.AgentProfileTypeHandler})
    </insert>
    <update id="updateById" parameterType="com.gw.agent.entity.AgentEntity">
        UPDATE t_agent
        SET updater          = #{et.updater},
            update_time      = #{et.updateTime},
            name             = #{et.name},
            introduction     = #{et.introduction},
            is_public        = #{et.isPublic},
            status           = #{et.status},
            type_id          = #{et.typeId},
            gender           = #{et.gender},
            avatar_url       = #{et.avatarUrl},
            bg_url           = #{et.bgUrl},
            bg_thumbnail_url = #{et.bgThumbnailUrl},
            identity         = #{et.identity},
            shelf_status     = #{et.shelfStatus},
            shelf_reason     = #{et.shelfReason},
            platform         = #{et.platform},
            recommend_idx    = #{et.recommendIdx},
            profile          = #{et.profile,typeHandler=com.gw.agent.handler.AgentProfileTypeHandler}
        WHERE id = #{et.id}
    </update>

    <!-- 根据ID列表批量查询智能体 -->
    <select id="findAllByIds" resultMap="BaseResultMap">
        SELECT *
        FROM t_agent
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND deleted = 0
        ORDER BY update_time DESC, popularity DESC
    </select>
    <select id="findAllByIdsNotSort" resultMap="BaseResultMap">
        SELECT *
        FROM t_agent
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND deleted = 0
    </select>
    <select id="findValidPublicIdsOrderByPopularityAndUpdate" resultType="java.lang.Long">
        SELECT id
        from t_agent
        WHERE deleted = 0
          AND is_public = 2
          AND shelf_status = 1
          AND status = 256
        ORDER BY popularity DESC, update_time DESC
        LIMIT #{limit}
    </select>
    <!-- 分页查询用户点赞的智能体 -->
    <select id="pageLikedByUsername" resultMap="BaseResultMap">
        SELECT a.*
        FROM t_agent a
        INNER JOIN t_agent_like al ON a.id = al.agent_id
        <where>
            a.deleted = 0
            AND al.deleted = 0
            AND al.status = 1 <!-- 1表示点赞状态 -->
            <if test="username != null and username != ''">
                AND al.username = #{username}
            </if>
        </where>
        ORDER BY al.create_time DESC
    </select>

    <!-- 分页查询用户收藏的智能体 -->
    <select id="pageFavoriteByUsername" resultMap="BaseResultMap">
        SELECT a.*
        FROM t_agent a
        INNER JOIN t_agent_favorite af ON a.id = af.agent_id
        <where>
            a.deleted = 0
            AND af.status = 1
            AND af.deleted = 0
            <if test="username != null and username != ''">
                AND af.username = #{username}
            </if>
        </where>
        ORDER BY af.create_time DESC
    </select>

    <!-- 分页查询用户评论过的智能体 -->
    <select id="pageCommentByUsername" resultMap="BaseResultMap">
        SELECT a.*
        FROM t_agent a
        INNER JOIN (
        SELECT agent_id, MAX(create_time) as latest_comment_time
        FROM t_agent_comment
        WHERE deleted = 0
        <if test="username != null and username != ''">
            AND username = #{username}
        </if>
        GROUP BY agent_id
        ) ac ON a.id = ac.agent_id
        WHERE a.deleted = 0
        ORDER BY ac.latest_comment_time DESC
    </select>

    <!-- 根据ID列表批量查询智能体 -->
    <select id="selectBatchIds" resultMap="BaseResultMap">
        SELECT *
        FROM t_agent
        WHERE id IN
        <foreach collection="agentIds" item="agentId" open="(" separator="," close=")">
            #{agentId}
        </foreach>
        AND deleted = 0
    </select>
    <select id="findAllStatusByAgentIds" resultType="com.gw.agent.entity.AgentStatusEntity">
        SELECT id,name, deleted, status, is_public as isPublic, shelf_status as shelfStatus
        FROM t_agent
        WHERE id IN
        <foreach collection="agentIds" item="agentId" open="(" separator="," close=")">
            #{agentId}
        </foreach>
    </select>
</mapper>