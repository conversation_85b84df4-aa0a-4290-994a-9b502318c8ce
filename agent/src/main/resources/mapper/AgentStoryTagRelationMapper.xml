<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gw.agent.mapper.sql.AgentStoryTagRelationMapper">
    <!-- 根据多个智能体剧情ID查询标签关联 -->
    <select id="findByStoryIds" resultType="com.gw.agent.entity.AgentStoryTagRelationEntity">
        SELECT * FROM t_agent_story_tag_relation
        WHERE story_id IN
        <foreach collection="storyIds" item="storyId" open="(" separator="," close=")">
            #{storyId}
        </foreach>
    </select>
</mapper>