package com.gw.agent.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

import static com.baomidou.mybatisplus.annotation.IdType.AUTO;

@Data
@TableName("t_story_current_use_scene")
public class StoryCurrentUseSceneEntity {
    @TableId(type = AUTO)
    private Long id;
    private Long storyId;
    private Long sceneId;
    private String username;
    private LocalDateTime updateTime;
}
