package com.gw.agent.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

import static com.baomidou.mybatisplus.annotation.IdType.AUTO;

/**
 * 智能体剧情评论点赞实体
 */
@Data
@TableName("t_agent_story_comment_like")
public class AgentStoryCommentLikeEntity {
    @TableId(type = AUTO)
    private Long id;
    
    /**
     * 评论ID
     */
    private Long commentId;
    
    /**
     * 点赞用户名
     */
    private String username;
    
    /**
     * 点赞状态：1-已点赞，0-取消点赞
     */
    private Integer status;
    
    @TableLogic
    private Integer deleted = 0;
    
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime = LocalDateTime.now();
    
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime = LocalDateTime.now();
}
