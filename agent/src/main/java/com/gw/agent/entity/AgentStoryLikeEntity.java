package com.gw.agent.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

import static com.baomidou.mybatisplus.annotation.IdType.AUTO;

/**
 * 智能体点赞实体
 */
@Data
@TableName("t_agent_story_like")
public class AgentStoryLikeEntity {
    @TableId(type = AUTO)
    private Long id;
    private Long storyId;
    private String username;
    /*1 点赞， 0 取消点赞 */
    private Integer status;
    @TableLogic
    private Integer deleted = 0;
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime = LocalDateTime.now();
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime = LocalDateTime.now();
}