package com.gw.agent.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

import static com.baomidou.mybatisplus.annotation.IdType.AUTO;

@Data
@TableName("t_story_draft")
public class StoryDraftEntity {
    @TableId(type = AUTO)
    private Long id;
    private String name;
    private String content;
    private String username;
    private LocalDateTime updateTime;
}
