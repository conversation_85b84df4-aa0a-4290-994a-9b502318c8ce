package com.gw.agent.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gw.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 智能体标签实体
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_agent_tag")
public class AgentTagEntity extends BaseEntity {
    /**
     * 标签名称
     */
    private String name;

    /**
     * 标签类型：1-系统标签，2-用户自定义标签
     */
    private Integer type;
    /**
     * 标签分类：1-背景标签，2-性格标签
     */
    private Integer category;

    /**
     * 标签描述
     */
    private String description;
    /**
     * 使用次数
     */
    private Integer useCount;
}
