package com.gw.agent.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gw.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 智能体剧情实体
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_agent_story")
public class AgentStoryEntity extends BaseEntity {

    /**
     * 剧情标题
     */
    private String name;

    /**
     * 剧情简介
     */
    private String description;

    /**
     * 剧情封面图片URL
     */
    private String bgUrl;
    /**
     * 剧情封面缩略图图片URL
     */
    private String bgThumbnailUrl;
    /**
     * 我的名字
     */
    private String myName;

    /**
     * 我的性别：1-男，2-女，3-其他
     */
    private Integer myGender;

    /**
     * 我的身份
     */
    private String myIdentity;

    private Long storyTypeId;

    /**
     * 剧情状态：255-草稿，1-审核，2-创建，4-发布
     */
    private Integer status;

    /**
     * 是否公开：1-私有，2-公开
     */
    private Integer isPublic;
    /**
     * 上架状态：1-上架，2-下架
     */
    private Integer shelfStatus = 1;
    /**
     * 上下架原因
     */
    @TableField("shelf_reason")
    private String shelfReason;
    @TableField("security_check_result")
    private String securityCheckResult;
    /**
     * 热度值
     */
    private Integer popularity = 0;
    /**
     * 是否已收藏：0-未收藏，1-已收藏
     */
    @TableField(exist = false)
    private Integer isFavorite = 0;

    /**
     * 是否已点赞：0-未点赞，1-已点赞
     */
    @TableField(exist = false)
    private Integer isLiked = 0;

    /**
     * 用户是否对此智能体感兴趣：0-无兴趣，1-轻度兴趣，2-中度兴趣，3-高度兴趣
     */
    @TableField(exist = false)
    private Integer isInterested = 0;

    /**
     * 点赞数
     */
    @TableField(exist = false)
    private Integer likeCount = 0;
    @TableField(exist = false)
    private Integer favoriteCount = 0;
    /**
     * 评论数
     */
    @TableField(exist = false)
    private Integer commentCount = 0;

    /**
     * 兴趣数
     */
    @TableField(exist = false)
    private Integer interestCount = 0;

    /**
     * 场景数量
     */
    @TableField(exist = false)
    private Integer sceneCount = 0;

    /**
     * 标签，JSON数组格式存储
     */
    private String tags;

    @TableField(exist = false)
    private AgentStoryTypeEntity storyType;

    /**
     * 场景列表 - 不存储在数据库中
     */
    @TableField(exist = false)
    private List<AgentStorySceneEntity> scenes;


}