package com.gw.agent.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

import static com.baomidou.mybatisplus.annotation.IdType.AUTO;

/**
 * 智能体使用记录实体
 */
@Data
@TableName("t_agent_usage_record")
public class AgentUsageRecordEntity {
    @TableId(type = AUTO)
    private Long id;
    /**
     * 智能体ID
     */
    private Long agentId;

    /**
     * 用户名
     */
    private String username;

    /**
     * 使用次数
     */
    private Integer useCount;
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime = LocalDateTime.now();
    @TableLogic
    private Integer deleted = 0;
    /**
     * 状态：1-正常，0-禁用
     */
    private Integer status;
}