package com.gw.agent.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import static com.baomidou.mybatisplus.annotation.IdType.AUTO;

@Data
@TableName("t_agent_story_statistics")
@NoArgsConstructor
@AllArgsConstructor
public class AgentStoryStatisticsEntity {
    @TableId(type = AUTO)
    private Long id;
    private Long storyId;
    private Integer favoriteCount = 0;
    private Integer commentCount = 0;
    private Integer likeCount = 0;

    public AgentStoryStatisticsEntity(Long agentId) {
        this.storyId = agentId;
    }
}
