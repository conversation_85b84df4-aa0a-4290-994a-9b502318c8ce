package com.gw.agent.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

import static com.baomidou.mybatisplus.annotation.IdType.AUTO;

/**
 * 用户对智能体兴趣关系实体
 */
@Data
@TableName("t_agent_story_interest")
public class AgentStoryInterestEntity {
    @TableId(type = AUTO)
    private Long id;
    private LocalDateTime updateTime;
    /**
     * 用户名
     */
    private String username;

    /**
     * 智能体ID
     */
    private Long storyId;

    /**
     * 兴趣程度：1-轻度兴趣，2-中度兴趣，3-高度兴趣
     */
    private Integer interestLevel;

    /**
     * 来源：1-浏览, 2-搜索, 3-推荐, 4-历史行为
     */
    private Integer source;

    /**
     * 标记：0-未标记，1-已标记
     */
    private Integer marked;
} 