package com.gw.agent.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import static com.baomidou.mybatisplus.annotation.IdType.AUTO;

/**
 * 智能体故事标签关联实体
 */
@Data
@TableName("t_agent_story_tag_relation")
public class AgentStoryTagRelationEntity {
    @TableId(type = AUTO)
    private Long id;
    /**
     * 智能体故事ID
     */
    private Long storyId;

    /**
     * 标签ID
     */
    private Long tagId;
} 