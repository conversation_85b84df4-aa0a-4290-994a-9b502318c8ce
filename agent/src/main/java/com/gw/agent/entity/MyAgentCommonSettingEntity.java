package com.gw.agent.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import static com.baomidou.mybatisplus.annotation.IdType.AUTO;

@Data
@TableName("t_my_common_agent_setting")
public class MyAgentCommonSettingEntity {
    @TableId(type = AUTO)
    private Long id = null;
    private String username;
    private Long agentId;
    /**
     * 智能体对我称呼
     */
    private String myNickName = "";
    /**
     * 智能体对我的性别认知
     */
    private String myGender = "";
    /**
     * 智能体对我的身份认知
     */
    private String myIdentity = "";
    /**
     * 是否自动播放语音消息
     */
    private Boolean autoPlayVoice = false;
}
