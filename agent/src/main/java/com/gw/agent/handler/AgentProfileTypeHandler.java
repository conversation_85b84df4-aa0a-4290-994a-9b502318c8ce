package com.gw.agent.handler;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.gw.agent.entity.AgentProfileEntity;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;
import org.postgresql.util.PGobject;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

@MappedJdbcTypes(JdbcType.OTHER)
@MappedTypes({JSONObject.class, AgentProfileEntity.class})
public class AgentProfileTypeHandler extends BaseTypeHandler<Object> {
    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, Object parameter, JdbcType jdbcType)
            throws SQLException {
        PGobject jsonObject = new PGobject();
        jsonObject.setType("jsonb");

        if (parameter instanceof AgentProfileEntity) {
            // 将AgentProfileEntity转换为JSON字符串
            jsonObject.setValue(JSON.toJSONString(parameter));
        } else if (parameter instanceof JSONObject) {
            jsonObject.setValue(parameter.toString());
        } else {
            // 对于其他类型，尝试转为JSON字符串
            jsonObject.setValue(JSON.toJSONString(parameter));
        }

        ps.setObject(i, jsonObject);
    }

    @Override
    public AgentProfileEntity getNullableResult(ResultSet rs, String columnName) throws SQLException {
        String json = rs.getString(columnName);
        return parseJson(json);
    }

    @Override
    public AgentProfileEntity getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        String json = rs.getString(columnIndex);
        return parseJson(json);
    }

    @Override
    public AgentProfileEntity getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        String json = cs.getString(columnIndex);
        return parseJson(json);
    }

    private AgentProfileEntity parseJson(String json) {
        return json == null ? null : JSON.parseObject(json, AgentProfileEntity.class);
    }
}