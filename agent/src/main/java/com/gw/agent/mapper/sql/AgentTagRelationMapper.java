package com.gw.agent.mapper.sql;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gw.agent.entity.AgentTagEntity;
import com.gw.agent.entity.AgentTagRelationEntity;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * 智能体标签关联数据访问接口
 */
@Mapper
public interface AgentTagRelationMapper extends BaseMapper<AgentTagRelationEntity> {
    @Select("SELECT * FROM t_agent_tag_relation")
    List<AgentTagRelationEntity> findAll();

    /**
     * 获取智能体的所有标签
     *
     * @param agentId 智能体ID
     * @return 标签列表
     */
    @Select("SELECT t.* FROM t_agent_tag t " +
            "INNER JOIN t_agent_tag_relation r ON t.id = r.tag_id " +
            "WHERE r.agent_id = #{agentId} ORDER BY t.use_count DESC")
    List<AgentTagEntity> findTagsByAgentId(@Param("agentId") Long agentId);

    @Select("SELECT * FROM t_agent_tag_relation WHERE agent_id = #{agentId}")
    List<AgentTagRelationEntity> findByAgentId(@Param("agentId") Long agentId);


    List<AgentTagRelationEntity> findByAgentIds(@Param("agentIds") List<Long> agentIds);

    @Delete("DELETE FROM t_agent_tag_relation WHERE agent_id = #{agentId}")
    void deleteByAgentId(@Param("agentId") Long agentId);

    /**
     * 批量删除智能体标签关系
     *
     * @param agentIds 智能体ID列表
     */
    @Delete("<script>" +
            "DELETE FROM t_agent_tag_relation WHERE agent_id IN " +
            "<foreach collection='agentIds' item='id' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach>" +
            "</script>")
    int deleteByAgentIds(@Param("agentIds") List<Long> agentIds);


    /**
     * 批量插入智能体标签关系
     *
     * @param relations 关系实体列表
     * @return 插入数量
     */
    @Insert("<script>" +
            "INSERT INTO t_agent_tag_relation (agent_id, tag_id) VALUES " +
            "<foreach collection='relations' item='relation' separator=','>" +
            "(#{relation.agentId}, #{relation.tagId})" +
            "</foreach>" +
            "</script>")
    int insertBatch(@Param("relations") List<AgentTagRelationEntity> relations);


    @Select("<script>" +
            "SELECT DISTINCT tag_id FROM t_agent_tag_relation WHERE agent_id IN " +
            "<foreach collection='agentId' item='id' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach>" +
            "</script>")
    List<Long> findTagIdsByAgentIds(@Param("agentId") List<Long> agentId);

    @Select("<script>" +
            "SELECT DISTINCT agent_id FROM t_agent_tag_relation WHERE tag_id IN " +
            "<foreach collection='tagIds' item='tagId' open='(' separator=',' close=')'>" +
            "#{tagId}" +
            "</foreach>" +
            "</script>")
    List<Long> findAgentIdsByTagIds(@Param("tagIds") List<Long> tagIds);
}