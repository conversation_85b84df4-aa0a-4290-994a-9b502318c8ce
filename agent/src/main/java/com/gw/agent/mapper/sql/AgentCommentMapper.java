package com.gw.agent.mapper.sql;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gw.agent.entity.AgentCommentEntity;
import com.gw.agent.entity.AgentCountEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 智能体评论数据访问接口
 */
@Mapper
public interface AgentCommentMapper extends BaseMapper<AgentCommentEntity> {

    /**
     * 根据智能体ID查询评论列表（只查询顶级评论）
     *
     * @param agentId 智能体ID
     * @return 评论列表
     */
    @Select("SELECT * FROM t_agent_comment WHERE agent_id = #{agentId} AND parent_id IS NULL AND status = 1 AND deleted = 0 ORDER BY create_time DESC")
    List<AgentCommentEntity> findByAgentId(@Param("agentId") Long agentId);

    @Select("SELECT * FROM t_agent_comment WHERE id = #{id} AND status = 1 AND deleted = 0")
    Optional<AgentCommentEntity> findById(@Param("id") Long id);

    /**
     * 批量根据ID查询评论
     *
     * @param ids 评论ID列表
     * @return 评论列表
     */
    @Select("<script>" +
            "SELECT * FROM t_agent_comment WHERE id IN " +
            "<foreach item='id' collection='ids' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach> " +
            "AND status = 1 AND deleted = 0" +
            "</script>")
    List<AgentCommentEntity> findByIds(@Param("ids") List<Long> ids);

    /**
     * 根据父评论ID查询回复列表
     *
     * @param parentId 父评论ID
     * @return 回复列表
     */
    @Select("SELECT * FROM t_agent_comment WHERE parent_id = #{parentId} AND status = 1 AND deleted = 0 ORDER BY create_time ASC")
    List<AgentCommentEntity> findRepliesByParentId(@Param("parentId") Long parentId);

    /**
     * 根据用户名查询评论列表
     *
     * @param username 用户名
     * @return 评论列表
     */
    @Select("SELECT * FROM t_agent_comment WHERE username = #{username} AND status = 1 AND deleted = 0 ORDER BY create_time DESC")
    List<AgentCommentEntity> findByUsername(@Param("username") String username);

    @Select("SELECT DISTINCT agent_id FROM t_agent_comment WHERE username = #{username} AND status = 1 AND deleted = 0")
    List<Long> findAgentIdsCommentedByUser(@Param("username") String username);

    /**
     * 统计评论的回复数量
     *
     * @param parentId 父评论ID
     * @return 回复数量
     */
    @Select("SELECT COUNT(*) FROM t_agent_comment WHERE parent_id = #{parentId} AND status = 1 AND deleted = 0")
    int countRepliesByParentId(@Param("parentId") Long parentId);

    /**
     * 批量统计多个评论的回复数量
     *
     * @param parentIds 父评论ID列表
     * @return 父评论ID和回复数量的映射
     */
    @Select("<script>" +
            "SELECT parent_id, COUNT(*) as reply_count " +
            "FROM t_agent_comment " +
            "WHERE parent_id IN " +
            "<foreach item='id' collection='parentIds' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach> " +
            "AND status = 1 AND deleted = 0 " +
            "GROUP BY parent_id" +
            "</script>")
    List<Map<String, Object>> batchCountRepliesByParentIds(@Param("parentIds") List<Long> parentIds);

    /**
     * 根据评论ID查询被回复的用户名
     *
     * @param commentId 评论ID
     * @return 被回复的用户名
     */
    @Select("SELECT username FROM t_agent_comment WHERE id = #{commentId} AND status = 1 AND deleted = 0")
    Optional<String> findUsernameByCommentId(@Param("commentId") Long commentId);

    /**
     * 更新评论状态
     *
     * @param id     评论ID
     * @param status 状态：1-正常，0-隐藏
     * @return 影响行数
     */
    @Update("UPDATE t_agent_comment SET status = #{status}, update_time = NOW() WHERE id = #{id}")
    int updateStatus(@Param("id") Long id, @Param("status") Integer status);

    /**
     * 统计智能体的评论数
     *
     * @param agentId 智能体ID
     * @return 评论数
     */
    @Select("SELECT COUNT(*) FROM t_agent_comment WHERE agent_id = #{agentId} AND status = 1 AND deleted = 0")
    int countByAgentId(@Param("agentId") Long agentId);

    @Select("SELECT COUNT(*) FROM t_agent_comment WHERE username = #{username} AND status = 1 AND deleted = 0")
    int countByUsername(@Param("username") String username);

    @Select("SELECT COUNT(1) FROM t_agent_comment WHERE username = #{username} AND status = 1 AND deleted = 0")
    int countByUsernameAndDistinctAgent(@Param("username") String username);
    @Select("SELECT DISTINCT agent_id FROM t_agent_comment WHERE username = #{username} AND status = 1 AND deleted = 0")
    List<Long> countAgentIdsCommentedByUser(@Param("username") String username);
    /**
     * 批量统计多个智能体的评论数
     *
     * @param agentIds 智能体ID列表
     * @return 智能体ID到评论数的映射
     */
    @Select("<script>SELECT agent_id, COUNT(*) as count FROM t_agent_comment " +
            "WHERE agent_id IN " +
            "<foreach collection='agentIds' item='id' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach>" +
            " AND status = 1 AND deleted = 0 GROUP BY agent_id</script>")
    List<AgentCountEntity> batchCountByAgentIds(@Param("agentIds") List<Long> agentIds);

    /**
     * 分页查询智能体评论列表
     *
     * @param agentId 智能体ID
     * @param parentId 父评论ID，为null时查询顶级评论，不为null时查询指定父评论的回复
     * @return 评论列表
     */
    @Select("<script>" +
            "SELECT * FROM t_agent_comment WHERE agent_id = #{agentId} " +
            "<choose>" +
            "<when test='parentId == null'>" +
            "AND parent_id IS NULL " +
            "</when>" +
            "<otherwise>" +
            "AND parent_id = #{parentId} " +
            "</otherwise>" +
            "</choose>" +
            "AND status = 1 AND deleted = 0 ORDER BY create_time DESC" +
            "</script>")
    List<AgentCommentEntity> pageAgentComments(@Param("agentId") Long agentId, @Param("parentId") Long parentId);

    /**
     * 后台分页查询智能体评论列表（支持多条件查询）
     *
     * @param agentIds 智能体ID列表
     * @param parentId 父评论ID，为null时查询顶级评论，不为null时查询指定父评论的回复
     * @param status 评论状态
     * @param username 用户名
     * @return 评论列表
     */
    @Select("<script>" +
            "SELECT * FROM t_agent_comment WHERE 1=1 " +
            "<if test='agentIds != null and agentIds.size() > 0'>" +
            "AND agent_id IN " +
            "<foreach collection='agentIds' item='agentId' open='(' separator=',' close=')'>" +
            "#{agentId}" +
            "</foreach> " +
            "</if>" +
            "<choose>" +
            "<when test='parentId == null'>" +
            "AND parent_id IS NULL " +
            "</when>" +
            "<otherwise>" +
            "AND parent_id = #{parentId} " +
            "</otherwise>" +
            "</choose>" +
            "<if test='status != null'>" +
            "AND status = #{status} " +
            "</if>" +
            "<if test='username != null and username != \"\"'>" +
            "AND username = #{username} " +
            "</if>" +
            "AND deleted = 0 ORDER BY create_time DESC" +
            "</script>")
    List<AgentCommentEntity> pageAgentCommentsAdmin(@Param("agentIds") List<Long> agentIds,
                                                    @Param("parentId") Long parentId,
                                                    @Param("status") Integer status,
                                                    @Param("username") String username);

    /**
     * 更新评论的子评论数量
     *
     * @param commentId 评论ID
     * @param increment 增量（可以是负数）
     * @return 影响行数
     */
    @Update("UPDATE t_agent_comment SET child_cnt = child_cnt + #{increment}, update_time = NOW() WHERE id = #{commentId}")
    int updateChildCount(@Param("commentId") Long commentId, @Param("increment") Integer increment);

    /**
     * 更新评论的点赞数量
     *
     * @param commentId 评论ID
     * @param increment 增量（可以是负数）
     * @return 影响行数
     */
    @Update("UPDATE t_agent_comment SET like_cnt = GREATEST(0, like_cnt + #{increment}), update_time = NOW() WHERE id = #{commentId}")
    int updateLikeCount(@Param("commentId") Long commentId, @Param("increment") Integer increment);

    /**
     * 更新评论的最新子评论ID
     *
     * @param parentId 父评论ID
     * @param lastChildId 最新子评论ID
     * @return 影响行数
     */
    @Update("UPDATE t_agent_comment SET last_child_id = #{lastChildId}, update_time = NOW() WHERE id = #{parentId}")
    int updateLastChildId(@Param("parentId") Long parentId, @Param("lastChildId") Long lastChildId);

    /**
     * 重新计算并更新评论的最新子评论ID
     *
     * @param parentId 父评论ID
     * @return 影响行数
     */
    @Update("UPDATE t_agent_comment SET last_child_id = (" +
            "SELECT MAX(id) FROM t_agent_comment child " +
            "WHERE child.parent_id = #{parentId} AND child.status = 1 AND child.deleted = 0" +
            "), update_time = NOW() WHERE id = #{parentId}")
    int recalculateLastChildId(@Param("parentId") Long parentId);
}