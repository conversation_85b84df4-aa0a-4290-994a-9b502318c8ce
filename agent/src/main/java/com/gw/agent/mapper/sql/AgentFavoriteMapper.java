package com.gw.agent.mapper.sql;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gw.agent.entity.AgentCountEntity;
import com.gw.agent.entity.AgentFavoriteEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;
import java.util.Optional;

/**
 * 智能体收藏数据访问接口
 */
@Mapper
public interface AgentFavoriteMapper extends BaseMapper<AgentFavoriteEntity> {

    /**
     * 根据用户名查询收藏的智能体列表
     *
     * @param username 用户名
     * @return 收藏的智能体ID列表
     */
    @Select("SELECT * FROM t_agent_favorite WHERE username = #{username} AND status = 1 AND deleted = 0 ORDER BY create_time DESC")
    List<AgentFavoriteEntity> findByUsername(@Param("username") String username);

    @Select("SELECT agent_id FROM t_agent_favorite WHERE username = #{username} AND status = 1 AND deleted = 0")
    List<Long> findUserFavoriteAgentIds(@Param("username") String username);

    /**
     * 根据智能体ID和用户名查询收藏记录
     *
     * @param agentId  智能体ID
     * @param username 用户名
     * @return 收藏记录
     */
    @Select("SELECT * FROM t_agent_favorite WHERE agent_id = #{agentId} AND username = #{username} AND deleted = 0 LIMIT 1")
    Optional<AgentFavoriteEntity> findByAgentIdAndUsername(@Param("agentId") Long agentId,
                                                           @Param("username") String username);

    /**
     * 更新收藏状态
     *
     * @param id     收藏记录ID
     * @param status 状态：1-已收藏，0-取消收藏
     * @return 影响行数
     */
    @Update("UPDATE t_agent_favorite SET status = #{status}, update_time = NOW() WHERE id = #{id}")
    int updateStatus(@Param("id") Long id, @Param("status") Integer status);

    /**
     * 根据智能体ID查询收藏记录
     *
     * @param agentId 智能体ID
     * @return 收藏记录列表
     */
    @Select("SELECT * FROM t_agent_favorite WHERE agent_id = #{agentId} AND status = 1 AND deleted = 0")
    List<AgentFavoriteEntity> findByAgentId(@Param("agentId") Long agentId);

    @Select("SELECT COUNT(*) FROM t_agent_favorite WHERE agent_id = #{agentId} AND status = 1 AND deleted = 0")
    int countByAgentId(@Param("agentId") Long agentId);

    @Select("SELECT COUNT(*) FROM t_agent_favorite WHERE username = #{username} AND status = 1 AND deleted = 0")
    int countByUsername(@Param("username") String username);

    /**
     * 批量统计多个智能体的收藏数
     *
     * @param agentIds 智能体ID列表
     * @return 智能体ID到收藏数的映射
     */
    @Select("<script>SELECT agent_id, COUNT(*) as count FROM t_agent_favorite " +
            "WHERE agent_id IN " +
            "<foreach collection='agentIds' item='id' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach>" +
            " AND status = 1 AND deleted = 0 GROUP BY agent_id</script>")
    List<AgentCountEntity> batchCountByAgentIds(@Param("agentIds") List<Long> agentIds);
}