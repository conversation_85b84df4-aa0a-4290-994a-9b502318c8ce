package com.gw.agent.mapper.sql;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gw.agent.entity.AgentStoryTagEntity;
import com.gw.agent.entity.AgentStoryTagRelationEntity;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * 智能体剧情标签关联数据访问接口
 */
@Mapper
public interface AgentStoryTagRelationMapper extends BaseMapper<AgentStoryTagRelationEntity> {
    @Select("SELECT * FROM t_agent_story_tag_relation")
    List<AgentStoryTagRelationEntity> findAll();

    /**
     * 获取智能体剧情的所有标签
     *
     * @param storyId 智能体剧情ID
     * @return 标签列表
     */
    @Select("SELECT t.* FROM t_agent_story_tag t " +
            "INNER JOIN t_agent_story_tag_relation r ON t.id = r.tag_id " +
            "WHERE r.story_id = #{storyId} ORDER BY t.use_count DESC")
    List<AgentStoryTagEntity> findTagsByStoryId(@Param("storyId") Long storyId);

    @Select("SELECT * FROM t_agent_story_tag_relation WHERE story_id = #{storyId}")
    List<AgentStoryTagRelationEntity> findByStoryId(@Param("storyId") Long storyId);

    List<AgentStoryTagRelationEntity> findByStoryIds(@Param("storyIds") List<Long> storyIds);

    @Delete("DELETE FROM t_agent_story_tag_relation WHERE story_id = #{storyId}")
    void deleteByStoryId(@Param("storyId") Long storyId);

    /**
     * 批量删除智能体剧情标签关系
     *
     * @param storyIds 智能体剧情ID列表
     */
    @Delete("<script>" +
            "DELETE FROM t_agent_story_tag_relation WHERE story_id IN " +
            "<foreach collection='storyIds' item='id' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach>" +
            "</script>")
    int deleteByStoryIds(@Param("storyIds") List<Long> storyIds);

    /**
     * 批量插入智能体剧情标签关系
     *
     * @param relations 关系实体列表
     * @return 插入数量
     */
    @Insert("<script>" +
            "INSERT INTO t_agent_story_tag_relation (story_id, tag_id) VALUES " +
            "<foreach collection='relations' item='relation' separator=','>" +
            "(#{relation.storyId}, #{relation.tagId})" +
            "</foreach>" +
            "</script>")
    int insertBatch(@Param("relations") List<AgentStoryTagRelationEntity> relations);

    @Select("<script>" +
            "SELECT DISTINCT tag_id FROM t_agent_story_tag_relation WHERE story_id IN " +
            "<foreach collection='storyIds' item='id' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach>" +
            "</script>")
    List<Long> findTagIdsByStoryIds(@Param("storyIds") List<Long> storyIds);

    @Select("<script>" +
            "SELECT DISTINCT story_id FROM t_agent_story_tag_relation WHERE tag_id IN " +
            "<foreach collection='tagIds' item='tagId' open='(' separator=',' close=')'>" +
            "#{tagId}" +
            "</foreach>" +
            "</script>")
    List<Long> findStoryIdsByTagIds(@Param("tagIds") List<Long> tagIds);
}