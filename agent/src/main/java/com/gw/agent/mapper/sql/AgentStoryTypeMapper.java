package com.gw.agent.mapper.sql;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gw.agent.entity.AgentStoryTypeEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Optional;

/**
 * 智能体剧情类型数据访问接口
 */
@Mapper
public interface AgentStoryTypeMapper extends BaseMapper<AgentStoryTypeEntity> {

    /**
     * 获取热门类型
     *
     * @param limit 限制数量
     * @return 类型列表
     */
    @Select("SELECT * FROM t_agent_story_type WHERE deleted = 0 ORDER BY use_count DESC LIMIT #{limit}")
    List<AgentStoryTypeEntity> findHotTags(@Param("limit") Integer limit);

    @Select("SELECT * FROM t_agent_story_type WHERE deleted = 0 ORDER BY sequence")
    List<AgentStoryTypeEntity> findAll();

    @Select("SELECT * FROM t_agent_story_type WHERE deleted = 0 AND name = #{name}")
    Optional<AgentStoryTypeEntity> findByName(String name);

    @Select("SELECT * FROM t_agent_story_type WHERE deleted = 0 AND id = #{id}")
    Optional<AgentStoryTypeEntity> findById(Long id);
}