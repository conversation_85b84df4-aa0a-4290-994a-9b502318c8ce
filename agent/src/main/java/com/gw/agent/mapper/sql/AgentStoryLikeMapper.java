package com.gw.agent.mapper.sql;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gw.agent.entity.AgentCountEntity;
import com.gw.agent.entity.AgentStoryLikeEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;
import java.util.Optional;

/**
 * 智能体剧情点赞数据访问接口
 */
@Mapper
public interface AgentStoryLikeMapper extends BaseMapper<AgentStoryLikeEntity> {

    /**
     * 根据用户名查询点赞的智能体剧情列表
     *
     * @param username 用户名
     * @return 点赞的智能体剧情列表
     */
    @Select("SELECT * FROM t_agent_story_like WHERE username = #{username} AND status = 1 AND deleted = 0")
    List<AgentStoryLikeEntity> findByUsername(@Param("username") String username);

    @Select("SELECT story_id FROM t_agent_story_like WHERE username = #{username} AND status = 1 AND deleted = 0")
    List<Long> findUserLikesStoryIdsByUsername(@Param("username") String username);

    @Select("SELECT count(1) FROM t_agent_story_like WHERE username = #{username} AND status = 1 AND deleted = 0")
    Integer cntByUsername(@Param("username") String username);

    /**
     * 根据智能体剧情ID和用户名查询点赞记录
     *
     * @param storyId  智能体剧情ID
     * @param username 用户名
     * @return 点赞记录
     */
    @Select("SELECT * FROM t_agent_story_like WHERE story_id = #{storyId} AND username = #{username} AND deleted = 0 LIMIT 1")
    Optional<AgentStoryLikeEntity> findByStoryIdAndUsername(@Param("storyId") Long storyId,
                                                            @Param("username") String username);

    /**
     * 更新点赞状态
     *
     * @param id     点赞记录ID
     * @param status 状态：1-已点赞，0-取消点赞
     * @return 影响行数
     */
    @Update("UPDATE t_agent_story_like SET status = #{status}, update_time = NOW() WHERE id = #{id}")
    int updateStatus(@Param("id") Long id, @Param("status") Integer status);

    /**
     * 根据智能体剧情ID查询点赞记录
     *
     * @param storyId 智能体剧情ID
     * @return 点赞记录列表
     */
    @Select("SELECT * FROM t_agent_story_like WHERE story_id = #{storyId} AND status = 1 AND deleted = 0")
    List<AgentStoryLikeEntity> findByStoryId(@Param("storyId") Long storyId);

    /**
     * 统计智能体剧情的点赞数
     *
     * @param storyId 智能体剧情ID
     * @return 点赞数
     */
    @Select("SELECT COUNT(*) FROM t_agent_story_like WHERE story_id = #{storyId} AND status = 1 AND deleted = 0")
    int countByStoryId(@Param("storyId") Long storyId);

    /**
     * 批量查询用户对多个智能体剧情的点赞状态
     *
     * @param storyIds 智能体剧情ID列表
     * @param username 用户名
     * @return 点赞记录列表
     */
    @Select("<script>SELECT * FROM t_agent_story_like WHERE username = #{username} AND story_id IN " +
            "<foreach collection='storyIds' item='id' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach>" +
            " AND deleted = 0</script>")
    List<AgentStoryLikeEntity> findByStoryIdsAndUsername(@Param("storyIds") List<Long> storyIds, @Param("username") String username);

    /**
     * 批量统计智能体剧情的点赞数
     *
     * @param storyIds 智能体剧情ID列表
     * @return 智能体剧情ID到点赞数的映射
     */
    @Select("<script>SELECT story_id as agent_id, COUNT(*) as count FROM t_agent_story_like " +
            "WHERE story_id IN " +
            "<foreach collection='storyIds' item='id' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach>" +
            " AND status = 1 AND deleted = 0 GROUP BY story_id</script>")
    List<AgentCountEntity> batchCountByStoryIds(@Param("storyIds") List<Long> storyIds);

    /**
     * 获取用户标记为不感兴趣的智能体剧情ID列表
     *
     * @param username 用户名
     * @return 不感兴趣的智能体剧情ID列表
     */
    @Select("SELECT story_id FROM t_agent_story_like WHERE username = #{username} AND status = 0 AND deleted = 0")
    List<Long> findDislikedByUsername(@Param("username") String username);
}