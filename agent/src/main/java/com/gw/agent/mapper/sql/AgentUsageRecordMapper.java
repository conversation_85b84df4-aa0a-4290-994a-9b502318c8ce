package com.gw.agent.mapper.sql;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gw.agent.entity.AgentUsageRecordEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 智能体使用记录数据访问接口
 */
@Mapper
public interface AgentUsageRecordMapper extends BaseMapper<AgentUsageRecordEntity> {

    /**
     * 根据智能体ID和用户名查询使用记录
     *
     * @param agentId  智能体ID
     * @param username 用户名
     * @return 使用记录
     */
    @Select("SELECT * FROM t_agent_usage_record WHERE agent_id = #{agentId} AND username = #{username} AND deleted = 0 LIMIT 1")
    Optional<AgentUsageRecordEntity> findByAgentIdAndUsername(@Param("agentId") Long agentId,
                                                              @Param("username") String username);

    /**
     * 更新使用次数
     *
     * @param id        记录ID
     * @param increment 增量
     * @return 影响行数
     */
    @Update("UPDATE t_agent_usage_record SET use_count = use_count + #{increment} WHERE id = #{id}")
    int updateUseCount(@Param("id") Long id, @Param("increment") Integer increment);

    /**
     * 统计智能体的使用人数
     *
     * @param agentId 智能体ID
     * @return 使用人数
     */
    @Select("SELECT COUNT(*) FROM t_agent_usage_record WHERE agent_id = #{agentId} AND deleted = 0")
    int countByAgentId(@Param("agentId") Long agentId);

    @Select("SELECT COUNT(*) FROM t_agent_usage_record WHERE username = #{username} AND deleted = 0")
    int countByUsername(@Param("username") String username);

    /**
     * 查询用户使用过的所有智能体ID列表
     *
     * @param username 用户名
     * @return 智能体ID列表
     */
    @Select("SELECT agent_id FROM t_agent_usage_record WHERE username = #{username} AND deleted = 0")
    List<Long> findAgentIdsByUsername(@Param("username") String username);

    /**
     * 统计指定时间范围内的活跃用户数
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 活跃用户数
     */
    @Select("SELECT COUNT(DISTINCT username) FROM t_agent_usage_record WHERE update_time BETWEEN #{startTime} AND #{endTime} AND deleted = 0")
    int countActiveUsersByTimeRange(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);
}