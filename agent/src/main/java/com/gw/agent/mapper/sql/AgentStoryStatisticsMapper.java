package com.gw.agent.mapper.sql;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gw.agent.entity.AgentStoryStatisticsEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

@Mapper
public interface AgentStoryStatisticsMapper extends BaseMapper<AgentStoryStatisticsEntity> {
    @Select("SELECT * FROM t_agent_story_statistics WHERE story_id = #{storyId} ORDER BY id DESC LIMIT 1")
    AgentStoryStatisticsEntity findFirstByStoryId(@Param("storyId") Long storyId);

    @Update("UPDATE t_agent_story_statistics SET favorite_count =  #{cnt} WHERE id = #{id}")
    int updateFavoriteCount(@Param("id") Long id, @Param("cnt") Integer cnt);

    @Update("UPDATE t_agent_story_statistics SET like_count =  #{cnt} WHERE id = #{id}")
    int updateLikeCount(@Param("id") Long id, @Param("cnt") Integer cnt);

    @Update("UPDATE t_agent_story_statistics SET comment_count =  #{cnt} WHERE id = #{id}")
    int updateCommentCount(@Param("id") Long id, @Param("cnt") Integer cnt);

    @Select("SELECT * FROM t_agent_story_statistics")
    List<AgentStoryStatisticsEntity> findAll();
}
