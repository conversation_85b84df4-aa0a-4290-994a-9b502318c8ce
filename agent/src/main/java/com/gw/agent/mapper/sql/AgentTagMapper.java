package com.gw.agent.mapper.sql;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gw.agent.entity.AgentTagEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Optional;

/**
 * 智能体标签数据访问接口
 */
@Mapper
public interface AgentTagMapper extends BaseMapper<AgentTagEntity> {

    /**
     * 获取热门标签
     *
     * @param limit 限制数量
     * @return 标签列表
     */
    @Select("SELECT * FROM t_agent_tag ORDER BY use_count DESC LIMIT #{limit}")
    List<AgentTagEntity> findHotTags(@Param("limit") Integer limit);

    @Select("SELECT * FROM t_agent_tag  where deleted = 0")
    List<AgentTagEntity> findAll();

    @Select("SELECT * FROM t_agent_tag  where deleted = 0 and name = #{name} and category = #{category}")
    Optional<AgentTagEntity> findByNameAndCategory(String name, Integer category);

    @Select("SELECT * FROM t_agent_tag  where deleted = 0 and id = #{id}")
    Optional<AgentTagEntity> findById(Long id);
}