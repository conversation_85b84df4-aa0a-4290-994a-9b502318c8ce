package com.gw.agent.mapper.sql;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gw.agent.entity.AgentStoryInterestEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 智能体剧情兴趣关系Mapper
 */
@Mapper
public interface AgentStoryInterestMapper extends BaseMapper<AgentStoryInterestEntity> {

    /**
     * 根据智能体剧情ID和用户名查询兴趣关系
     *
     * @param storyId  智能体剧情ID
     * @param username 用户名
     * @return 兴趣关系
     */
    @Select("SELECT * FROM t_agent_interest WHERE story_id = #{storyId} AND username = #{username}")
    AgentStoryInterestEntity findByStoryIdAndUsername(@Param("storyId") Long storyId, @Param("username") String username);

    /**
     * 统计智能体剧情的兴趣数量
     *
     * @param storyId 智能体剧情ID
     * @return 兴趣数量
     */
    @Select("SELECT COUNT(*) FROM t_agent_interest WHERE story_id = #{storyId}")
    int countByStoryId(@Param("storyId") Long storyId);

    /**
     * 查询用户对智能体剧情的兴趣列表
     *
     * @param username 用户名
     * @return 兴趣列表
     */
    @Select("SELECT * FROM t_agent_interest WHERE username = #{username} ORDER BY update_time DESC")
    List<AgentStoryInterestEntity> findByUsername(@Param("username") String username);

    /**
     * 查询智能体剧情的兴趣列表
     *
     * @param storyId 智能体剧情ID
     * @return 兴趣列表
     */
    @Select("SELECT * FROM t_agent_interest WHERE story_id = #{storyId} ORDER BY update_time DESC")
    List<AgentStoryInterestEntity> findByStoryId(@Param("storyId") Long storyId);

    /**
     * 查询用户所有未标记的智能体剧情兴趣列表
     *
     * @param username 用户名
     * @return 未标记的智能体剧情ID列表
     */
    @Select("SELECT story_id FROM t_agent_interest WHERE username = #{username} AND marked = 0 ORDER BY update_time DESC")
    List<Long> findUnmarkedByUsername(@Param("username") String username);
}