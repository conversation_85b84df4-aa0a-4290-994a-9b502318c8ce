package com.gw.agent.mapper.sql;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gw.agent.entity.AgentCommentLikeEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 智能体评论点赞数据访问接口
 */
@Mapper
public interface AgentCommentLikeMapper extends BaseMapper<AgentCommentLikeEntity> {

    /**
     * 根据评论ID和用户名查询点赞记录
     *
     * @param commentId 评论ID
     * @param username  用户名
     * @return 点赞记录
     */
    @Select("SELECT * FROM t_agent_comment_like WHERE comment_id = #{commentId} AND username = #{username} AND deleted = 0")
    Optional<AgentCommentLikeEntity> findByCommentIdAndUsername(@Param("commentId") Long commentId, @Param("username") String username);

    /**
     * 统计评论的点赞数
     *
     * @param commentId 评论ID
     * @return 点赞数
     */
    @Select("SELECT COUNT(*) FROM t_agent_comment_like WHERE comment_id = #{commentId} AND status = 1 AND deleted = 0")
    int countByCommentId(@Param("commentId") Long commentId);

    /**
     * 批量统计多个评论的点赞数
     *
     * @param commentIds 评论ID列表
     * @return 评论ID和点赞数的映射
     */
    @Select("<script>" +
            "SELECT comment_id, COUNT(*) as like_count " +
            "FROM t_agent_comment_like " +
            "WHERE comment_id IN " +
            "<foreach item='id' collection='commentIds' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach> " +
            "AND status = 1 AND deleted = 0 " +
            "GROUP BY comment_id" +
            "</script>")
    List<Map<String, Object>> batchCountByCommentIds(@Param("commentIds") List<Long> commentIds);

    /**
     * 批量查询用户对多个评论的点赞状态
     *
     * @param commentIds 评论ID列表
     * @param username   用户名
     * @return 评论ID和点赞状态的映射
     */
    @Select("<script>" +
            "SELECT comment_id, status " +
            "FROM t_agent_comment_like " +
            "WHERE comment_id IN " +
            "<foreach item='id' collection='commentIds' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach> " +
            "AND username = #{username} AND deleted = 0" +
            "</script>")
    List<Map<String, Object>> batchFindUserLikeStatus(@Param("commentIds") List<Long> commentIds, @Param("username") String username);

    /**
     * 更新点赞状态
     *
     * @param id     点赞记录ID
     * @param status 状态：1-已点赞，0-取消点赞
     * @return 影响行数
     */
    @Update("UPDATE t_agent_comment_like SET status = #{status}, update_time = NOW() WHERE id = #{id}")
    int updateStatus(@Param("id") Long id, @Param("status") Integer status);

    /**
     * 根据评论ID查询点赞记录
     *
     * @param commentId 评论ID
     * @return 点赞记录列表
     */
    @Select("SELECT * FROM t_agent_comment_like WHERE comment_id = #{commentId} AND status = 1 AND deleted = 0")
    List<AgentCommentLikeEntity> findByCommentId(@Param("commentId") Long commentId);

    /**
     * 根据用户名查询点赞的评论列表
     *
     * @param username 用户名
     * @return 点赞的评论列表
     */
    @Select("SELECT * FROM t_agent_comment_like WHERE username = #{username} AND status = 1 AND deleted = 0")
    List<AgentCommentLikeEntity> findByUsername(@Param("username") String username);
}
