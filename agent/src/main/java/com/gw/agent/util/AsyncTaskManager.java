package com.gw.agent.util;

import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

/**
 * 异步任务管理器
 * 提供统一的异步任务执行、超时控制和异常处理
 */
@Log4j2
@Component
public class AsyncTaskManager {

    @Autowired
    @Qualifier("taskExecutor")
    private ThreadPoolTaskExecutor taskExecutor;

    /**
     * 执行异步任务（无返回值），带超时控制
     *
     * @param task           要执行的任务
     * @param timeoutSeconds 超时时间（秒）
     * @param taskName       任务名称（用于日志）
     * @return CompletableFuture<Void>
     */
    public CompletableFuture<Void> runAsyncWithTimeout(Runnable task, long timeoutSeconds, String taskName) {
        return CompletableFuture.runAsync(() -> {
                    try {
                        long startTime = System.currentTimeMillis();
                        task.run();
                        log.debug("异步任务 [{}] 执行完成，耗时: {}ms", taskName, System.currentTimeMillis() - startTime);
                    } catch (Exception e) {
                        log.warn("异步任务 [{}] 执行失败: {}", taskName, e.getMessage());
                        throw e; // 重新抛出异常，让exceptionally处理
                    }
                }, taskExecutor)
                .orTimeout(timeoutSeconds, TimeUnit.SECONDS)
                .exceptionally(ex -> {
                    if (ex instanceof java.util.concurrent.TimeoutException) {
                        log.warn("异步任务 [{}] 执行超时 ({}秒)", taskName, timeoutSeconds);
                    } else {
                        log.warn("异步任务 [{}] 执行失败: {}", taskName, ex.getMessage());
                    }
                    return null;
                });
    }

    /**
     * 执行异步任务（有返回值），带超时控制
     *
     * @param task           要执行的任务
     * @param timeoutSeconds 超时时间（秒）
     * @param taskName       任务名称（用于日志）
     * @param <T>            返回值类型
     * @return CompletableFuture<T>
     */
    public <T> CompletableFuture<T> supplyAsyncWithTimeout(Supplier<T> task, long timeoutSeconds, String taskName) {
        return CompletableFuture.supplyAsync(() -> {
                    try {
                        long startTime = System.currentTimeMillis();
                        T result = task.get();
                        log.debug("异步任务 [{}] 执行完成，耗时: {}ms", taskName, System.currentTimeMillis() - startTime);
                        return result;
                    } catch (Exception e) {
                        log.warn("异步任务 [{}] 执行失败: {}", taskName, e.getMessage());
                        throw e; // 重新抛出异常，让exceptionally处理
                    }
                }, taskExecutor)
                .orTimeout(timeoutSeconds, TimeUnit.SECONDS)
                .exceptionally(ex -> {
                    if (ex instanceof java.util.concurrent.TimeoutException) {
                        log.warn("异步任务 [{}] 执行超时 ({}秒)", taskName, timeoutSeconds);
                    } else {
                        log.warn("异步任务 [{}] 执行失败: {}", taskName, ex.getMessage());
                    }
                    return null;
                });
    }

    /**
     * 执行缓存相关的异步任务
     * 专门用于缓存操作，使用较短的超时时间
     *
     * @param cacheTask 缓存任务
     * @param cacheKey  缓存键（用于日志）
     * @return CompletableFuture<Void>
     */
    public CompletableFuture<Void> runCacheTaskAsync(Runnable cacheTask, String cacheKey) {
        return runAsyncWithTimeout(cacheTask, 10, "缓存操作-" + cacheKey);
    }

    /**
     * 执行数据刷新相关的异步任务
     * 专门用于数据刷新操作，使用中等超时时间
     *
     * @param refreshTask 刷新任务
     * @param description 任务描述（用于日志）
     * @return CompletableFuture<Void>
     */
    public CompletableFuture<Void> runRefreshTaskAsync(Runnable refreshTask, String description) {
        return runAsyncWithTimeout(refreshTask, 15, "数据刷新-" + description);
    }

    /**
     * 执行批量处理相关的异步任务
     * 专门用于批量处理操作，使用较长的超时时间
     *
     * @param batchTask   批量任务
     * @param description 任务描述（用于日志）
     * @return CompletableFuture<Void>
     */
    public CompletableFuture<Void> runBatchTaskAsync(Runnable batchTask, String description) {
        return runAsyncWithTimeout(batchTask, 30, "批量处理-" + description);
    }
}