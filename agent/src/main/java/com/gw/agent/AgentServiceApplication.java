package com.gw.agent;

import com.baomidou.mybatisplus.autoconfigure.DdlApplicationRunner;
import com.baomidou.mybatisplus.extension.ddl.IDdl;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;

import java.util.List;

/**
 * 智能体服务主应用类
 */
@SpringBootApplication
@EnableDiscoveryClient
@MapperScan({"com.gw.agent.mapper.sql", "com.gw.agent.mapper"})
@EnableFeignClients(basePackages = {"com.gw.common.user.client", "com.gw.common.notify.client", "com.gw.common.agent.client", "com.gw.common.membership.client"})
@ComponentScan(basePackages = {
        "com.gw.agent",
        "com.gw.common.user",
        "com.gw.common.membership",
        "com.gw.common.agent",
        "com.gw.common.exception",
        "com.gw.common.notify"
})
@EnableAsync
public class AgentServiceApplication {
    public static void main(String[] args) {
        SpringApplication.run(AgentServiceApplication.class, args);
    }

    @Bean
    public DdlApplicationRunner ddlApplicationRunner(@Autowired(required = false) List<IDdl> ddlList) {
        return new DdlApplicationRunner(ddlList);
    }
}