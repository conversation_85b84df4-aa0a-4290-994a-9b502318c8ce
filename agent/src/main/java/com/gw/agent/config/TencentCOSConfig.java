package com.gw.agent.config;

import com.qcloud.cos.COSClient;
import com.qcloud.cos.ClientConfig;
import com.qcloud.cos.auth.BasicCOSCredentials;
import com.qcloud.cos.auth.COSCredentials;
import com.qcloud.cos.region.Region;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 腾讯云COS配置类
 */
@Configuration
@Log4j2
public class TencentCOSConfig {

    @Value("${tencent.cos.secret-id}")
    private String secretId;

    @Value("${tencent.cos.secret-key}")
    private String secretKey;

    @Value("${tencent.cos.region:ap-beijing}")
    private String region;

    /**
     * 配置腾讯云COS客户端
     */
    @Bean
    public COSClient cosClient() {
        try {
            // 配置用户身份信息
            COSCredentials credentials = new BasicCOSCredentials(secretId, secretKey);

            // 配置COS区域
            Region cosRegion = new Region(region);
            ClientConfig clientConfig = new ClientConfig(cosRegion);

            // 创建COS客户端
            COSClient cosClient = new COSClient(credentials, clientConfig);

            log.info("腾讯云COS客户端初始化成功, region: {}", region);
            return cosClient;

        } catch (Exception e) {
            log.error("腾讯云COS客户端初始化失败", e);
            throw new RuntimeException("腾讯云COS客户端初始化失败", e);
        }
    }
} 