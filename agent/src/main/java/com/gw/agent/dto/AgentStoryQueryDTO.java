package com.gw.agent.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 智能体剧情查询DTO
 */
@Data
public class AgentStoryQueryDTO {

    /**
     * 关键词搜索
     */
    @Schema(description = "关键词搜索")
    private String keyword;

    @Schema(description = "剧情状态：255-草稿，1-审核，2-创建，4-发布")
    private Integer status;

    /**
     * 是否公开：1-私有，2-公开
     */
    @Schema(description = "是否公开：1-私有，2-公开")
    private Integer isPublic;

    /**
     * 上架状态：1-上架，2-下架
     */
    @Schema(description = "上架状态：1-上架，2-下架")
    private Integer shelfStatus = 1;
    @JsonIgnore
    private String myPublicUserName;
    /**
     * 创建者
     */
    @Schema(description = "创建者")
    private String creator;

    @Schema(description = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate startDate;
    @Schema(description = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate endDate;

    @JsonIgnore
    private LocalDateTime startDateTime;
    @JsonIgnore
    private LocalDateTime endDateTime;

    /**
     * 当前用户名（用于查询用户相关数据）
     */
    @Schema(description = "当前用户名（用于查询用户相关数据）")
    private String username;

    public LocalDateTime getStartDateTime() {
        if (startDate == null) {
            return null;
        }
        return startDate.atStartOfDay();
    }

    public void setStartDateTime(LocalDate startDate) {
        if (startDate == null) {
            return;
        }
        startDateTime = startDate.atStartOfDay();
    }

    public LocalDateTime getEndDateTime() {
        if (endDate == null) {
            return null;
        }
        return endDate.atTime(23, 59, 59);
    }

    public void setEndDateTime(LocalDate endDate) {
        if (endDate == null) {
            return;
        }
        endDateTime = endDate.atTime(23, 59, 59);
    }
}