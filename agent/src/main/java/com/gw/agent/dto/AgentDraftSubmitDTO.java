package com.gw.agent.dto;

import com.alibaba.fastjson2.JSONObject;
import com.gw.agent.entity.AgentDraftEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "智能体草稿箱提交")
public class AgentDraftSubmitDTO {
    @Schema(description = "草稿箱ID 如果是第一次提交是0")
    private Long id = 0L;
    @Schema(description = "草稿箱名称")
    private String name;
    @Schema(description = "草稿箱内容")
    private JSONObject content;

    public static AgentDraftEntity toEntity(AgentDraftSubmitDTO dto) {
        AgentDraftEntity entity = new AgentDraftEntity();
        if(dto.getId() == null){
            dto.setId(0L);
        }
        entity.setId(dto.getId());
        entity.setName(dto.getName());
        entity.setContent(dto.getContent().toJSONString());
        return entity;
    }
}
