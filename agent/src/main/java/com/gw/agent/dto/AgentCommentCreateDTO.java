package com.gw.agent.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.List;

/**
 * 智能体评论创建请求DTO
 */
@Data
@Schema(description = "智能体评论创建请求")
public class AgentCommentCreateDTO {

    @NotNull(message = "智能体ID不能为空")
    @Schema(description = "智能体ID", required = true)
    private Long agentId;

    @NotBlank(message = "评论内容不能为空")
    @Schema(description = "评论内容", required = true)
    private String content;

    @Schema(description = "父评论ID，如果是顶级评论则为null")
    private Long parentId;

    @Schema(description = "被回复的用户名（仅在回复评论时有值）")
    private String replyToUsername;

    @Size(max = 9, message = "图片数量不能超过9张")
    @Schema(description = "图片列表，最多9张")
    private List<String> images;

    @Schema(description = "消息列表")
    private List<String> messages;
}
