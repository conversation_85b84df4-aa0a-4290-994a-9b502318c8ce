package com.gw.agent.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 智能体剧情兴趣DTO
 */
@Data
@Schema(description = "智能体剧情兴趣请求")
public class AgentStoryInterestDTO {

    /**
     * 剧情ID
     */
    @NotNull(message = "剧情ID不能为空")
    @Schema(description = "剧情ID")
    private Long storyId;

    @Min(value = 1, message = "兴趣级别最小为1")
    @Max(value = 3, message = "兴趣级别最大为3")
    @Schema(description = "兴趣级别：1-轻度兴趣，2-中度兴趣，3-高度兴趣")
    private Integer interestLevel = 3;

    /**
     * 来源：1-浏览, 2-搜索, 3-推荐, 4-历史行为
     */
    @Schema(description = "来源：1-浏览, 2-搜索, 3-推荐, 4-历史行为")
    private Integer source = 1;
}