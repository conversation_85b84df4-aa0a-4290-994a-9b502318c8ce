package com.gw.agent.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.util.List;

/**
 * 智能体剧情提交DTO
 */
@Data
public class AgentStorySubmitDTO {

    /**
     * 剧情标题
     */
    @NotBlank(message = "剧情标题不能为空")
    @Schema(description = "剧情标题")
    private String name;

    /**
     * 剧情简介
     */
    @Schema(description = "剧情简介")
    private String description;

    /**
     * 剧情封面图片URL
     */
    @Schema(description = "剧情封面图片URL")
    private String bgUrl;

    /**
     * 我的名字
     */
    @Schema(description = "我的名字")
    private String myName;

    /**
     * 我的性别：1-男，2-女，3-其他
     */
    @Schema(description = "我的性别：1-男，2-女，3-其他")
    private Integer myGender;

    /**
     * 我的身份
     */
    @Schema(description = "我的身份")
    private String myIdentity;

    @Schema(description = "剧情类型ID")
    private Long storyTypeId;

    /**
     * 是否公开：1-私有，2-公开
     */
    @Schema(description = "是否公开：1-私有，2-公开")
    private Integer isPublic = 1;
    private Long draftId;
    @Schema(description = "场景列表")
    private List<AgentSceneDTO> scenes;
}