package com.gw.agent.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * 智能体场景提交DTO
 */
@Data
public class AgentSceneDTO {
    /**
     * 主键ID 如果新增则 ID为0
     */
    @Schema(description = "主键ID，新增时为0")
    private Long id;
    /**
     * 所属剧情ID 如果是和新增剧情一起添加为0
     */
    @Schema(description = "所属剧情ID，新增剧情时为0")
    private Long storyId;

    /**
     * 场景名称
     */
    @NotBlank(message = "场景名称不能为空")
    @Schema(description = "场景名称")
    private String sceneName;

    /**
     * 场景简介
     */
    @Schema(description = "场景简介")
    private String sceneDescription;


    /**
     * 场景顺序 如果不需要可以设置为0
     */
    @Schema(description = "场景顺序，不需要时可设为0")
    private Integer sceneOrder = 0;

    /**
     * 场景封面图片URL
     */
    @Schema(description = "场景封面图片URL")
    private String bgUrl;
    /**
     * 场景背景音乐URL
     */
    @TableField("bg_music_url")
    @Schema(description = "场景背景音乐URL")
    private String bgMusicUrl;
    /**
     * 场景氛围：温馨、紧张、神秘、浪漫等
     */
    @Schema(description = "场景氛围，例如温馨、紧张、神秘、浪漫等")
    private String atmosphere;

    /**
     * 天气设置：晴天、雨天、雪天等
     */
    @Schema(description = "天气设置，例如晴天、雨天、雪天等")
    private String weather;

    /**
     * 时间段：早晨、中午、黄昏、夜晚等
     */
    @Schema(description = "时间段，例如早晨、中午、黄昏、夜晚等")
    private String timePeriod;

    /**
     * 地点描述
     */
    @Schema(description = "地点描述")
    private String location;

    /**
     * 场景开场白
     */
    @Schema(description = "场景开场白")
    @NotNull(message = "开场白不能为空")
    private String openingText;
    @Schema(description = "场景开场白对应的智能体ID")
    @NotNull(message = "开场白对应的智能体ID不能为空")
    private Long openingAgentId;
    /**
     * 场景脚本内容
     */
    @Schema(description = "场景脚本内容")
    private String sceneScript;
    @Schema(description = "智能体关联列表")
    private List<SceneAgentRelationDTO> agentRelations;
}