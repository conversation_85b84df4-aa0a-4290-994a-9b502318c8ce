package com.gw.agent.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 智能体剧情评论点赞请求DTO
 */
@Data
@Schema(description = "智能体剧情评论点赞请求")
public class AgentStoryCommentLikeDTO {

    @NotNull(message = "评论ID不能为空")
    @Schema(description = "评论ID", required = true)
    private Long commentId;

    @Schema(description = "点赞操作：true-点赞，false-取消点赞，null-切换状态")
    private Boolean liked;
}
