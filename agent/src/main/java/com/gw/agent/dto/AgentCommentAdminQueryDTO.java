package com.gw.agent.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 后台智能体评论查询DTO
 */
@Data
@Schema(description = "后台智能体评论查询参数")
public class AgentCommentAdminQueryDTO {

    @Schema(description = "用户名")
    private String username;

    @Schema(description = "智能体名称（模糊查询）")
    private String agentName;

    @Schema(description = "智能体ID")
    private Long agentId;

    @Schema(description = "父评论ID，为null或0时查询顶级评论")
    @JsonIgnore
    private Long parentId = 0L;

    @Schema(description = "评论状态：1-正常，0-隐藏")
    @JsonIgnore
    private Integer status;
}
