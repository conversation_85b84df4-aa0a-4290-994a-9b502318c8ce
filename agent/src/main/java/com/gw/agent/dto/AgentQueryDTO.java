package com.gw.agent.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Schema(description = "智能体查询参数")
public class AgentQueryDTO {
    //    private String name;
    @JsonIgnore
    private List<Long> tagSearchIds;
    @JsonIgnore
    private Long typeSearchId;
    private List<Long> tagIds;
    /**
     * 是否公开：1-私密，2-公开
     */
    @Schema(description = "是否公开：1-私密，2-公开")
    private Integer isPublic;
    @Schema(description = "性别：1-男，2-女")
    private Integer gender;
    private Long typeId;
    private Integer status;
    private Integer popularity;
    private String creator;
    @JsonIgnore
    private String username;

    @Schema(description = "排序字段")
    private String sortField;

    @Schema(description = "排序方式：asc-升序，desc-降序")
    private String sortOrder;

    @Schema(description = "关键词搜索")
    private String keyword;

    @Schema(description = "上架状态：0 全部 1-上架，2-下架")
    private Integer shelfStatus = 0;

    @Schema(description = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate startDate;
    @Schema(description = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate endDate;

    @JsonIgnore
    private LocalDateTime startDateTime;
    @JsonIgnore
    private LocalDateTime endDateTime;
    @JsonIgnore
    private String myPublicUserName;

    public LocalDateTime getStartDateTime() {
        if (startDate == null) {
            return null;
        }
        return startDate.atStartOfDay();
    }

    public void setStartDateTime(LocalDate startDate) {
        if (startDate == null) {
            return;
        }
        startDateTime = startDate.atStartOfDay();
    }

    public LocalDateTime getEndDateTime() {
        if (endDate == null) {
            return null;
        }
        return endDate.atTime(23, 59, 59);
    }

    public void setEndDateTime(LocalDate endDate) {
        if (endDate == null) {
            return;
        }
        endDateTime = endDate.atTime(23, 59, 59);
    }
}
