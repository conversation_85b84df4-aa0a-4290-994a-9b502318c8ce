package com.gw.agent.vo;

import com.gw.agent.entity.AgentEntity;
import com.gw.agent.entity.AgentStoryEntity;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 智能体场景详情VO
 */
@Data
public class AgentSceneDetailVO {

    /**
     * 场景ID
     */
    private Long id;

    /**
     * 所属剧情ID
     */
    private Long storyId;

    /**
     * 场景名称
     */
    private String sceneName;

    /**
     * 场景简介
     */
    private String sceneDescription;

    /**
     * 场景顺序
     */
    private Integer sceneOrder;

    /**
     * 场景背景图片URL
     */
    private String backgroundUrl;

    /**
     * 场景氛围：温馨、紧张、神秘、浪漫等
     */
    private String atmosphere;

    /**
     * 天气设置：晴天、雨天、雪天等
     */
    private String weather;

    /**
     * 时间段：早晨、中午、黄昏、夜晚等
     */
    private String timePeriod;

    /**
     * 地点描述
     */
    private String location;

    /**
     * 场景开场白
     */
    private String openingText;

    /**
     * 场景脚本内容
     */
    private String sceneScript;

    /**
     * 是否允许跳过
     */
    private Boolean allowSkip;

    /**
     * 场景状态：0-禁用，1-正常
     */
    private Integer status;

    /**
     * 该场景的游戏次数
     */
    private Integer playCount;

    /**
     * 创建者
     */
    private String creator;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 所属剧情信息
     */
    private AgentStoryEntity story;

    /**
     * 场景中的智能体列表
     */
    private List<AgentEntity> agents;

    /**
     * 智能体数量
     */
    private Integer agentCount;
} 