package com.gw.agent.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class AgentStoryVO {
    @Schema(description = "剧情ID")
    private Long id;
    /**
     * 剧情标题
     */
    @NotBlank(message = "剧情标题不能为空")
    @Schema(description = "剧情标题")
    private String name;

    /**
     * 剧情简介
     */
    @Schema(description = "剧情简介")
    private String description;

    /**
     * 剧情封面图片URL
     */
    @Schema(description = "剧情封面图片URL")
    private String bgUrl;

    /**
     * 我的名字
     */
    @Schema(description = "我的名字")
    private String myName;

    /**
     * 我的性别：1-男，2-女，3-其他
     */
    @Schema(description = "我的性别：1-男，2-女，3-其他")
    private Integer myGender;

    /**
     * 我的身份
     */
    @Schema(description = "我的身份")
    private String myIdentity;

    @Schema(description = "剧情类型ID")
    private Long storyTypeId;

    /**
     * 剧情状态：255-草稿，1-审核，2-创建，4-发布
     */
    @Schema(description = "剧情状态：255-草稿，1-审核中，2-创建中，4-发布中，255 草稿，256 发布成功")
    private Integer status;

    /**
     * 是否公开：1-私有，2-公开
     */
    @Schema(description = "是否公开：1-私有，2-公开")
    private Integer isPublic;
    /**
     * 上架状态：1-上架，2-下架
     */
    @Schema(description = "上架状态：1-上架，2-下架")
    private Integer shelfStatus = 1;
    private String shelfReason;
    @Schema(description = "场景列表")
    private List<AgentSceneVO> scenes;
    /**
     * 剧情封面缩略图图片URL
     */
    private String bgThumbnailUrl;
    /**
     * 是否已收藏：0-未收藏，1-已收藏
     */
    @Schema(description = "是否已收藏：0-未收藏，1-已收藏")
    private Integer isFavorite = 0;
    @Schema(description = "是否已点赞：0-未点赞，1-已点赞")
    private Integer isLike = 0;
    @Schema(description = "点赞数")
    private Integer likeCount = 0;
    @Schema(description = "评论数")
    private Integer commentCount = 0;
    @Schema(description = "收藏数")
    private Integer favoriteCount = 0;
    @Schema(description = "热度值")
    private Integer popularity;
    @Schema(description = "用户使用人数")
    private Integer userCount;
    private List<AgentSummaryVO> agents;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    private String securityCheckResult;
    private String creator;

}