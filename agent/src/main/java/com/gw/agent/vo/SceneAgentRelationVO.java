package com.gw.agent.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class SceneAgentRelationVO {
    private Long id;
    /**
     * 场景ID
     */
    @NotNull(message = "场景ID不能为空")
    @Schema(description = "场景ID")
    private Long sceneId;

    /**
     * 智能体ID
     */
    @NotNull(message = "智能体ID不能为空")
    @Schema(description = "智能体ID")
    private Long agentId;
    private String agentName;
    private AgentSummaryVO agent;
    /**
     * 智能体在该场景中的角色
     */
    @Schema(description = "智能体在该场景中的角色")
    private String agentRole;

    /**
     * 出场顺序
     */
    @Schema(description = "出场顺序")
    private Integer appearanceOrder = 1;

    /**
     * 是否为主要角色
     */
    @Schema(description = "是否为主要角色")
    private Boolean isMainCharacter = true;

    /**
     * 初始情绪状态
     */
    @Schema(description = "初始情绪状态")
    private String initialMood;
}
