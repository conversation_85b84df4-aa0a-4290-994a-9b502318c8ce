package com.gw.agent.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 智能体剧情评论VO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "智能体剧情评论信息")
public class AgentStoryCommentVO {

    @Schema(description = "评论ID")
    private Long id;

    @Schema(description = "剧情ID")
    private Long storyId;
    @Schema(description = "剧情名称")
    private String storyName;
    @Schema(description = "评论用户名")
    private String username;

    @Schema(description = "用户昵称")
    private String nickname;

    @Schema(description = "用户头像")
    private String avatarUrl;

    @Schema(description = "评论内容")
    private String content;

    @Schema(description = "父评论ID，顶级评论为null")
    private Long parentId;

    @Schema(description = "被回复的用户名")
    private String replyToUsername;
    @Schema(description = "被回复的用户名")
    private String replyToNickname;
    @Schema(description = "被回复的用户名")
    private String replyToAvatarUrl;
    @Schema(description = "评论状态：1-正常，0-隐藏")
    private Integer status;

    @Schema(description = "评论点赞数")
    private Integer likeCount;

    @Schema(description = "当前用户是否已点赞：1-已点赞，0-未点赞")
    private Integer isLiked;

    @Schema(description = "子评论数量")
    private Integer replyCount;

    @Schema(description = "图片列表")
    private List<String> imageList;

    @Schema(description = "消息列表")
    private List<String> messageList;

    @Schema(description = "最新子评论")
    private AgentStoryCommentVO latestChild;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
}
