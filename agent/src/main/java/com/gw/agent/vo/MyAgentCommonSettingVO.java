package com.gw.agent.vo;

import com.gw.agent.entity.MyAgentCommonSettingEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class MyAgentCommonSettingVO {
    /**
     * 主键ID
     */
    private Long id;
    private Long agentId;
    /**
     * 智能体对我称呼
     */
    private String myNickName;

    /**
     * 智能体对我的性别认知
     */
    private String myGender;

    /**
     * 智能体对我的身份认知
     */
    private String myIdentity;

    /**
     * 是否自动播放语音消息
     */
    private Boolean autoPlayVoice = false;

    public MyAgentCommonSettingVO(MyAgentCommonSettingEntity entity) {
        if (entity != null) {
            BeanUtils.copyProperties(entity, this);
        }
    }
} 