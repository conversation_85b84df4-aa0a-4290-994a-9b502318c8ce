package com.gw.agent.vo;

import com.gw.agent.entity.AgentEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Schema(description = "智能体信息")
@NoArgsConstructor
@AllArgsConstructor
public class AgentSummaryVO {
    private Long id;
    @Schema(description = "智能体名称")
    private String name;
    @Schema(description = "头像地址")
    @NotBlank(message = "头像地址不能为空")
    private String avatarUrl;
    @Schema(description = "背景图缩略图地址")
    private String bgThumbnailUrl;
    @Schema(description = "背景图地址")
    private String bgUrl;

    public AgentSummaryVO(AgentEntity entity) {
        this.id = entity.getId();
        this.name = entity.getName();
        this.avatarUrl = entity.getAvatarUrl();
        this.bgThumbnailUrl = entity.getBgThumbnailUrl();
        this.bgUrl = entity.getBgUrl();
    }
}
