package com.gw.agent.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gw.agent.entity.AgentInterestEntity;
import com.gw.agent.mapper.sql.AgentInterestMapper;
import com.gw.agent.service.AgentInterestService;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 智能体兴趣服务实现
 */
@Service
@RequiredArgsConstructor
@Log4j2
public class AgentInterestServiceImpl extends ServiceImpl<AgentInterestMapper, AgentInterestEntity> implements AgentInterestService {
    private final String CacheAgentUsrInterestValue = "agentUsrInterest";

    @Override
    public int getInterestLevel(Long agentId, String username) {
        if (agentId == null || username == null) {
            return 0;
        }
        AgentInterestEntity entity = this.baseMapper.findByAgentIdAndUsername(agentId, username);
        return entity != null ? entity.getInterestLevel() : 0;
    }

    @Override
    @CacheEvict(value = CacheAgentUsrInterestValue, key = "'agent:' + #username", beforeInvocation = true)
    public boolean markInterest(Long agentId, String username, Integer interestLevel, Integer source) {
        if (agentId == null || username == null || interestLevel == null) {
            return false;
        }

        try {
            AgentInterestEntity entity = this.baseMapper.findByAgentIdAndUsername(agentId, username);
            LocalDateTime now = LocalDateTime.now();

            if (entity == null) {
                // 创建新的兴趣关系
                entity = new AgentInterestEntity();
                entity.setAgentId(agentId);
                entity.setUsername(username);
                entity.setInterestLevel(interestLevel);
                entity.setSource(source != null ? source : 1); // 默认来源为浏览
                entity.setMarked(1);
                entity.setUpdateTime(now);
                return this.save(entity);
            } else {
                // 更新已有的兴趣关系
                entity.setInterestLevel(interestLevel);
                if (source != null) {
                    entity.setSource(source);
                }
                entity.setMarked(1);
                entity.setUpdateTime(now);
                return this.updateById(entity);
            }
        } catch (Exception e) {
            log.error("标记智能体兴趣失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    @CacheEvict(value = CacheAgentUsrInterestValue, key = "'agent:' + #username", beforeInvocation = true)
    public boolean unmarkInterest(Long agentId, String username) {
        if (agentId == null || username == null) {
            return false;
        }

        try {
            AgentInterestEntity entity = this.baseMapper.findByAgentIdAndUsername(agentId, username);
            if (entity != null) {
                // 设置为未标记状态
                entity.setMarked(0);
                entity.setUpdateTime(LocalDateTime.now());
                return this.updateById(entity);
            }
            return true; // 如果不存在，直接返回成功
        } catch (Exception e) {
            log.error("取消智能体兴趣标记失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public int getInterestCount(Long agentId) {
        if (agentId == null) {
            return 0;
        }
        return this.baseMapper.countByAgentId(agentId);
    }

    @Override
    public List<Long> getInterestedAgentIds(String username) {
        if (username == null) {
            return Collections.emptyList();
        }
        List<AgentInterestEntity> interests = this.baseMapper.findByUsername(username);
        return interests.stream()
                .map(AgentInterestEntity::getAgentId)
                .collect(Collectors.toList());
    }

    @Override
    public List<String> getInterestedUsernames(Long agentId) {
        if (agentId == null) {
            return Collections.emptyList();
        }
        List<AgentInterestEntity> interests = this.baseMapper.findByAgentId(agentId);
        return interests.stream()
                .map(AgentInterestEntity::getUsername)
                .collect(Collectors.toList());
    }

    @Override
    @Cacheable(value = CacheAgentUsrInterestValue, key = "'agent:' + #username", unless = "#result == null")
    public List<Long> findUnmarkedByUsername(String username) {
        return this.baseMapper.findUnmarkedByUsername(username);
    }
} 