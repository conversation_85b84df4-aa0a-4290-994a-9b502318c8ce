package com.gw.agent.service.impl;

import com.gw.agent.mapper.sql.AgentUsageRecordMapper;
import com.gw.agent.service.UserStatisticsService;
import com.gw.agent.vo.DailyStatisticsVO;
import com.gw.agent.vo.MonthlyStatisticsVO;
import com.gw.common.user.service.UserProxyService;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * 用户统计服务实现类
 */
@Log4j2
@Service
@RequiredArgsConstructor
public class UserStatisticsServiceImpl implements UserStatisticsService {

    private final UserProxyService userProxyService;
    private final AgentUsageRecordMapper agentUsageRecordMapper;

    @Override
    public List<MonthlyStatisticsVO> getMonthlyStatistics(int months) {
        List<MonthlyStatisticsVO> result = new ArrayList<>();
        LocalDate endDate = LocalDate.now();

        // 遍历过去指定个月
        for (int i = 0; i < months; i++) {
            LocalDate currentDate = endDate.minusMonths(i);
            int year = currentDate.getYear();
            int month = currentDate.getMonthValue();

            // 获取当月第一天和最后一天
            LocalDate firstDayOfMonth = LocalDate.of(year, month, 1);
            LocalDate lastDayOfMonth = firstDayOfMonth.plusMonths(1).minusDays(1);

            // 设置时间范围
            LocalDateTime startTime = LocalDateTime.of(firstDayOfMonth, LocalTime.MIN);
            LocalDateTime endTime = LocalDateTime.of(lastDayOfMonth, LocalTime.MAX);

            // 查询当月新增用户数
            int newUserCount = userProxyService.countNewUsersByTimeRange(startTime, endTime);

            // 查询当月活跃用户数
            int activeUserCount = userProxyService.countActiveUsersByTimeRange(startTime, endTime);

            // 格式化年月
            String yearMonth = firstDayOfMonth.format(DateTimeFormatter.ofPattern("yyyy-MM"));

            // 构建月度统计数据
            MonthlyStatisticsVO monthStats = new MonthlyStatisticsVO();
            monthStats.setYearMonth(yearMonth);
            monthStats.setNewUserCount(newUserCount);
            monthStats.setActiveUserCount(activeUserCount);

            result.add(monthStats);
        }

        return result;
    }

    @Override
    public List<DailyStatisticsVO> getDailyStatistics(int days) {
        List<DailyStatisticsVO> result = new ArrayList<>();
        LocalDate endDate = LocalDate.now();

        // 遍历过去指定天数
        for (int i = 0; i < days; i++) {
            LocalDate currentDate = endDate.minusDays(i);

            // 设置时间范围
            LocalDateTime startTime = LocalDateTime.of(currentDate, LocalTime.MIN);
            LocalDateTime endTime = LocalDateTime.of(currentDate, LocalTime.MAX);

            // 查询当天新增用户数
            int newUserCount = userProxyService.countNewUsersByTimeRange(startTime, endTime);

            // 查询当天活跃用户数
            int activeUserCount = userProxyService.countActiveUsersByTimeRange(startTime, endTime);

            // 格式化日期
            String date = currentDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

            // 构建日度统计数据
            DailyStatisticsVO dayStats = new DailyStatisticsVO();
            dayStats.setDate(date);
            dayStats.setNewUserCount(newUserCount);
            dayStats.setActiveUserCount(activeUserCount);

            result.add(dayStats);
        }

        return result;
    }

    @Override
    public List<DailyStatisticsVO> getMonthlyDailyStatistics(int months) {
        List<DailyStatisticsVO> result = new ArrayList<>();
        LocalDate endDate = LocalDate.now();
        LocalDate startDate = endDate.minusMonths(months);

        // 从开始日期遍历到结束日期的每一天
        for (LocalDate date = startDate; !date.isAfter(endDate); date = date.plusDays(1)) {
            // 设置时间范围
            LocalDateTime startTime = LocalDateTime.of(date, LocalTime.MIN);
            LocalDateTime endTime = LocalDateTime.of(date, LocalTime.MAX);

            // 查询当天新增用户数
            int newUserCount = userProxyService.countNewUsersByTimeRange(startTime, endTime);

            // 查询当天活跃用户数
            int activeUserCount = userProxyService.countActiveUsersByTimeRange(startTime, endTime);

            // 格式化日期
            String dateStr = date.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

            // 构建日度统计数据
            DailyStatisticsVO dayStats = new DailyStatisticsVO();
            dayStats.setDate(dateStr);
            dayStats.setNewUserCount(newUserCount);
            dayStats.setActiveUserCount(activeUserCount);

            result.add(dayStats);
        }

        return result;
    }
} 