package com.gw.agent.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gw.agent.entity.AgentSearchRankEntity;
import com.gw.agent.mapper.sql.AgentSearchRankMapper;
import com.gw.agent.service.AgentSearchRankService;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 智能体热搜榜Service实现类
 */
@Service
@RequiredArgsConstructor
@Log4j2
public class AgentSearchRankServiceImpl extends ServiceImpl<AgentSearchRankMapper, AgentSearchRankEntity> implements AgentSearchRankService {


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean recordSearch(String username, String keyword, Boolean recGlobal) {
        if (keyword == null || StringUtils.isBlank(keyword)) {
            return false;
        }
        if (recGlobal) {
            AgentSearchRankEntity globalEntity;
            globalEntity = this.baseMapper.findFirstKeywordAndUsernameIsNull(keyword);
            if (globalEntity != null) {
                this.baseMapper.updateIncreaseSearchCountById(globalEntity.getId(), 1);
            } else {
                globalEntity = new AgentSearchRankEntity();
                globalEntity.setKeyword(keyword);
                globalEntity.setUsername(null);
                globalEntity.setSearchCount(1);
                this.baseMapper.insert(globalEntity);
            }
        }
        if (StringUtils.isNotBlank(username)) {
            AgentSearchRankEntity entity = this.baseMapper.findFirstKeywordAndUsername(keyword, username);
            if (entity != null) {
                this.baseMapper.updateIncreaseSearchCountById(entity.getId(), 1);
            } else {
                AgentSearchRankEntity newEntity = new AgentSearchRankEntity();
                newEntity.setKeyword(keyword);
                newEntity.setUsername(username);
                newEntity.setSearchCount(1);
                this.baseMapper.insert(newEntity);
            }
        }
        return true;
    }

    @Override
    public AgentSearchRankEntity findById(Long id) {
        return this.baseMapper.selectById(id);
    }

    @Override
    public void deleteById(Long id) {
        this.baseMapper.deleteById(id);
    }

    @Override
    public void deleteByUsername(String username) {
        if (StringUtils.isNotBlank(username)) {
            this.lambdaUpdate()
                    .eq(AgentSearchRankEntity::getUsername, username)
                    .remove();
        }
    }

    @Override
    public List<AgentSearchRankEntity> getUserSearchRank(String username, Integer limit) {
        return this.baseMapper.findByUsernameOrderByUpdateTimeDesc(username, limit);
    }

    @Override
    public List<AgentSearchRankEntity> getGlobalSearchRank(Integer limit) {
        return this.baseMapper.findByUsernameIsNullOrderBySearchCountDesc(limit);
    }
} 