package com.gw.agent.service;

import com.gw.agent.entity.AgentSearchRankEntity;

import java.util.List;

/**
 * 智能体热搜榜Service
 */
public interface AgentSearchRankService {

    /**
     * 记录搜索，更新排行榜
     *
     * @param username 用户名
     * @param keyword  搜索关键词
     * @return 是否成功
     */
    boolean recordSearch(String username, String keyword, Boolean recGlobal);

    AgentSearchRankEntity findById(Long id);

    void deleteById(Long id);

    void deleteByUsername(String username);

    /**
     * 获取用户搜索排行榜
     *
     * @param username 用户名
     * @param limit    限制数量
     * @return 搜索排行榜列表
     */
    List<AgentSearchRankEntity> getUserSearchRank(String username, Integer limit);

    List<AgentSearchRankEntity> getGlobalSearchRank(Integer limit);
} 