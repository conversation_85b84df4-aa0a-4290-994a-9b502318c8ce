package com.gw.agent.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gw.agent.entity.AgentStoryTagEntity;
import com.gw.agent.mapper.sql.AgentStoryTagMapper;
import com.gw.agent.service.AgentStoryTagService;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 智能体剧情标签服务实现
 */
@Service
@RequiredArgsConstructor
@Log4j2
public class AgentStoryTagServiceImpl extends ServiceImpl<AgentStoryTagMapper, AgentStoryTagEntity> implements AgentStoryTagService {

    @Override
    @CacheEvict(value = "agentStoryTag", allEntries = true, beforeInvocation = true)
    public void insert(AgentStoryTagEntity entity) {
        this.baseMapper.insert(entity);
    }

    @Override
    @CacheEvict(value = "agentStoryTag", allEntries = true, beforeInvocation = true)
    public void update(AgentStoryTagEntity entity) {
        this.baseMapper.updateById(entity);
    }

    @Override
    @CacheEvict(value = "agentStoryTag", allEntries = true, beforeInvocation = true)
    public void delete(AgentStoryTagEntity entity) {
        this.baseMapper.deleteById(entity.getId());
    }

    @Override
    @Cacheable(value = "agentStoryTag", key = "#root.methodName + ':' + #id", unless = "#result == null")
    public AgentStoryTagEntity findById(Long id) {
        return this.baseMapper.findById(id).orElse(null);
    }

    @Override
    @Cacheable(value = "agentStoryTag", key = "#root.methodName", unless = "#result == null")
    public List<AgentStoryTagEntity> findAll() {
        return this.baseMapper.findAll();
    }

    @Override
    public boolean isNameExistByCategory(String name, Integer category) {
        return this.baseMapper.findByNameAndCategory(name, category).isPresent();
    }

    @Override
    @Cacheable(value = "agentStoryTag", key = "#root.methodName", unless = "#result == null")
    public Map<Long, AgentStoryTagEntity> findTagsMap() {
        List<AgentStoryTagEntity> tags = this.baseMapper.findAll();

        if (CollectionUtils.isEmpty(tags)) {
            return Collections.emptyMap();
        }

        // 使用ID作为键构建映射
        return tags.stream()
                .collect(Collectors.toMap(AgentStoryTagEntity::getId, tag -> tag, (existing, replacement) -> existing, HashMap::new));
    }

    @Override
    @Cacheable(value = "agentStoryTag", key = "'hotTags:' + #limit", unless = "#result == null")
    public List<AgentStoryTagEntity> findHotTags(Integer limit) {
        try {
            return this.baseMapper.findHotTags(limit);
        } catch (Exception e) {
            log.error("获取热门标签失败: {}", e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    @Override
    @CacheEvict(value = "agentStoryTag", allEntries = true)
    public void incrementUseCount(Long tagId, Integer increment) {
        if (tagId == null || increment == null || increment <= 0) {
            return;
        }

        try {
            AgentStoryTagEntity tag = this.baseMapper.findById(tagId).orElse(null);
            if (tag != null) {
                tag.setUseCount((tag.getUseCount() == null ? 0 : tag.getUseCount()) + increment);
                this.baseMapper.updateById(tag);
            }
        } catch (Exception e) {
            log.error("增加标签{}使用次数失败: {}", tagId, e.getMessage(), e);
        }
    }
}