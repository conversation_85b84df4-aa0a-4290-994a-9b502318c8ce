package com.gw.agent.service.impl;

import com.github.pagehelper.PageInfo;
import com.gw.agent.constant.AgentCacheConstant;
import com.gw.agent.constant.AgentRecommendationConstants;
import com.gw.agent.entity.*;
import com.gw.agent.handler.RecommendationCacheHelper;
import com.gw.agent.mapper.sql.AgentTagRelationMapper;
import com.gw.agent.service.*;
import com.gw.agent.vo.AgentCacheVO;
import com.gw.common.agent.constant.AgentConstant;
import com.gw.common.agent.constant.AgentStatus;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Log4j2
@Service
@RequiredArgsConstructor
public class AgentRecommendationServiceImpl implements AgentRecommendationService {

    private static final int PAGINATION_UNDERLYING_LIST_LIMIT = 200;
    private final AgentService agentService;
    private final AgentUsageRecordService agentUsageRecordService;
    private final AgentTagRelationMapper agentTagRelationMapper;
    private final RecommendationCacheHelper cacheHelper;
    private final AgentInterestService agentInterestService;
    private final CacheManager cacheManager;
    private final AgentLikeService agentLikeService;
    private final AgentCommentService agentCommentService;
    private final AgentFavoriteService agentFavoriteService;
    private final AgentTypeService agentTypeService;
    private final AgentTagService agentTagService;
    private final AgentCacheService agentCacheService;
    private final UserPreferenceService userPreferenceService;
    private final AgentStatisticsService statisticsService;
    @Autowired
    @Qualifier("taskExecutor")
    private ThreadPoolTaskExecutor taskExecutor;

    public List<Long> recommendAgentIdsByUserCharacteristics(String username, Integer limit, boolean useCache) {
        limit = (limit == null || limit <= 0) ? AgentRecommendationConstants.DEFAULT_RECOMMENDATION_LIMIT : limit;

        // Handle anonymous user
        if (!StringUtils.hasText(username)) {
            return getAnonymousRecommendations(limit, useCache);
        }

        // Handle logged-in user
        log.info("开始为用户{}生成智能体推荐, limit={}", username, limit);
        long startTime = System.currentTimeMillis();

        // Try getting from the cache first
        if (useCache) {
            List<Long> cachedResult = getCachedRecommendations(username, limit);
            if (cachedResult != null) {
                return cachedResult;
            }
        }

        // Generate new recommendations
        List<Long> finalRecommendationIds = generateUserRecommendations(username, limit);

        // Cache the results
        cacheRecommendations(username, finalRecommendationIds);

        log.info("用户{}的智能体推荐生成完成，共{}个推荐，耗时：{}ms",
                username, finalRecommendationIds.size(), System.currentTimeMillis() - startTime);
        return finalRecommendationIds;
    }

    private List<Long> getAnonymousRecommendations(int limit, boolean useCache) {
        log.info("用户名为空，返回公开智能体随机推荐, limit={}", limit);
        String anonymousCacheKey = AgentRecommendationConstants.AGENT_RECOMMEND_IDS_KEY_PREFIX + "anonymous";
        Cache cache = cacheManager.getCache(AgentCacheConstant.AGENT_CACHE);

        // Try cache first
        List<Long> cachedAgentIds = getAgentIdsFromCache(cache, anonymousCacheKey);
        List<Long> resultIds;

        if (!CollectionUtils.isEmpty(cachedAgentIds) && useCache) {
            log.info("从缓存获取匿名用户推荐ID列表 ({} ids)", cachedAgentIds.size());
            resultIds = cachedAgentIds;
        } else {
            log.info("生成匿名用户推荐并缓存");
            List<AgentCacheVO> randomAgents = agentCacheService.findAllPublic().stream()
                    .filter(agent -> agent.getRecommendIdx() != null && agent.getRecommendIdx() > 0)
                    .collect(Collectors.toList());
            resultIds = randomAgents.stream()
                    .map(AgentCacheVO::getId)
                    .collect(Collectors.collectingAndThen(Collectors.toList(), list -> {
                        Collections.shuffle(list);
                        return list;
                    }));
            cacheAnonymousRecommendations(cache, resultIds);
        }

        return resultIds;
    }

    private void cacheAnonymousRecommendations(Cache cache, List<Long> agentIds) {
        if (cache != null && !CollectionUtils.isEmpty(agentIds)) {
            cache.put(AgentRecommendationConstants.AGENT_RECOMMEND_IDS_KEY_PREFIX + "anonymous", agentIds);
            log.info("已缓存{}个匿名用户推荐ID到缓存", agentIds.size());
        }
    }

    private List<Long> getCachedRecommendations(String username, int limit) {
        String cacheKey = AgentRecommendationConstants.AGENT_RECOMMEND_IDS_KEY_PREFIX + username;
        Cache recommendationCache = cacheManager.getCache(AgentCacheConstant.AGENT_CACHE);
        List<Long> cachedRecommendedAgentIds = getAgentIdsFromCache(recommendationCache, cacheKey);

        if (!CollectionUtils.isEmpty(cachedRecommendedAgentIds)) {
            log.info("从缓存获取用户 {} 的推荐ID列表 ({} ids)", username, cachedRecommendedAgentIds.size());

            // 只要缓存中有一定数量的有效推荐ID就使用缓存
            int minimumRequiredItems = Math.min(limit, 50); // 至少需要50条或请求的数量

            if (cachedRecommendedAgentIds.size() >= minimumRequiredItems) {
                int resultSize = Math.min(limit, cachedRecommendedAgentIds.size());
                List<Long> finalCachedIds = cachedRecommendedAgentIds.subList(0, resultSize);

                log.info("用户 {} 的智能体推荐从缓存获取完成，共{}个推荐", username, finalCachedIds.size());
                logRecommendedIds("从缓存获取的推荐IDs (最终)", finalCachedIds);
                return finalCachedIds;
            }
            log.info("缓存中用户 {} 的推荐ID不足 (有效 {} / 最低需求 {}), 将重新生成",
                    username, cachedRecommendedAgentIds.size(), minimumRequiredItems);
        }
        return null;
    }

    private List<Long> generateUserRecommendations(String username, int limit) {

        // 获取全部公开的智能体
        List<AgentCacheVO> agents = agentCacheService.findAllPublic().stream()
                .filter(agent -> agent.getRecommendIdx() != null && agent.getRecommendIdx() > 0)
                .collect(Collectors.toList());

        // 获取用户已使用过的智能体ID列表
        List<Long> usageAgentIds = agentUsageRecordService.findAgentIdsByUsername(username);
        if (!CollectionUtils.isEmpty(usageAgentIds)) {
            // 过滤出缓存中有效的智能体ID
            Set<Long> validAgentIdsInCache = agents.stream()
                    .map(AgentCacheVO::getId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());
            // 过滤使用记录中的无效ID
            List<Long> filteredUsageAgentIds = usageAgentIds.stream()
                    .filter(validAgentIdsInCache::contains)
                    .distinct()
                    .collect(Collectors.toList());
            usageAgentIds = filteredUsageAgentIds;
            // 从推荐列表中移除已使用过的智能体
            agents = agents.stream()
                    .filter(agent -> !filteredUsageAgentIds.contains(agent.getId()))
                    .collect(Collectors.toList());
        }
        // 过滤掉用户不感兴趣的智能体
        List<Long> dislikedAgentIds = agentInterestService.findUnmarkedByUsername(username);
        if (!CollectionUtils.isEmpty(dislikedAgentIds)) {
            agents = agents.stream()
                    .filter(agent -> !dislikedAgentIds.contains(agent.getId()))
                    .collect(Collectors.toList());
        }

        if (agents.isEmpty()) {
            log.info("没有公开待推荐智能体，返回空列表");
            return Collections.emptyList();
        }

        logRecommendedIds("待推荐的智能体", agents.stream()
                .map(AgentCacheVO::getId)
                .collect(Collectors.toList()));

        // 基于用户兴趣获取推荐智能体
        List<AgentCacheVO> interestAgents = getInterestBasedAgentsForUser(username, agents, usageAgentIds);

        // 如果基于兴趣的推荐数量已满足要求,直接返回
        if (interestAgents.size() >= limit) {
            return interestAgents.stream()
                    .map(AgentCacheVO::getId)
                    .limit(limit).toList();
        }
        log.info(" interestAgents agents (size: {}) IDs: {}",
                interestAgents.size(),
                interestAgents.stream()
                        .map(AgentCacheVO::getId)
                        .toList());

        // 获取剩余可推荐的智能体并随机打乱
        List<AgentCacheVO> remainingAgents = agents.stream()
                .filter(agent -> !interestAgents.contains(agent))
                .collect(Collectors.collectingAndThen(Collectors.toList(), list -> {
                    Collections.shuffle(list);
                    return list;
                }));
        log.info(" remainingAgents agents (size: {}) IDs: {}",
                remainingAgents.size(),
                remainingAgents.stream()
                        .map(AgentCacheVO::getId)
                        .toList());

        // 如果总数不足限制数量,则添加历史使用记录补充
        if (remainingAgents.size() + interestAgents.size() < limit) {
            List<Long> agentIds = Stream.concat(interestAgents.stream(), remainingAgents.stream()).map(AgentCacheVO::getId).toList();
            if (!usageAgentIds.isEmpty()) {
                agentIds = Stream.concat(agentIds.stream(), usageAgentIds.stream()).toList();
            }
            agentIds = agentIds.stream().distinct().limit(limit).toList();
            return agentIds;
        }

        // 合并基于兴趣和剩余的推荐并限制数量返回
        return Stream.concat(interestAgents.stream(), remainingAgents.stream()).map(AgentCacheVO::getId).limit(limit).toList();
    }

    private void cacheRecommendations(String username, List<Long> agentIds) {
        String cacheKey = AgentRecommendationConstants.AGENT_RECOMMEND_IDS_KEY_PREFIX + username;
        Cache cache = cacheManager.getCache(AgentCacheConstant.AGENT_CACHE);
        if (cache != null) {
            cache.put(cacheKey, agentIds);
        }
        log.info("已缓存用户 {} 的 {} 个推荐ID到缓存", username, agentIds.size());
    }

    // 用于重构的推荐逻辑的新帮助方法

    /**
     * 获取基于用户直接兴趣(点赞、收藏、评论)的智能体。
     * 按热度(DESC)和更新时间(DESC)排序。
     */
    private List<AgentCacheVO> getInterestBasedAgentsForUser(String username, List<AgentCacheVO> agentList, List<Long> agentUsageList) {
        // 获取用户偏好设置
        var userPreference = userPreferenceService.findByUsername(username);

        // 收集用户的所有交互智能体ID(使用、点赞、收藏、评论)
        Set<Long> interactedAgentIds = Stream.of(
                        agentUsageList,
                        agentLikeService.findUserLikesAgentIds(username),
                        agentFavoriteService.findUserFavoriteAgentIds(username),
                        agentCommentService.findAgentIdsCommentedByUser(username)
                )
                .filter(Objects::nonNull)
                .flatMap(Collection::stream)
                .collect(Collectors.toSet());

        // 如果用户没有任何交互记录,返回空列表
        if (interactedAgentIds.isEmpty()) {
            log.info("User {} has no interaction history", username);
            return Collections.emptyList();
        }

        log.info("User {} has interacted with {} agents", username, interactedAgentIds.size());

        // 获取用户交互过的智能体列表
        List<AgentCacheVO> interactedAgents = agentList.stream()
                .filter(agent -> interactedAgentIds.contains(agent.getId()))
                .toList();

        // 提取交互过的智能体类型ID
        Set<Long> interactedTypeIds = interactedAgents.stream()
                .map(AgentCacheVO::getTypeId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        // 获取相同类型的智能体ID
        List<Long> relatedTypeAgentIds = new ArrayList<>();
        if (!interactedTypeIds.isEmpty()) {
            relatedTypeAgentIds = agentList.stream()
                    .filter(agent -> agent.getTypeId() != null && interactedTypeIds.contains(agent.getTypeId()))
                    .map(AgentCacheVO::getId)
                    .toList();
        }

        // 添加用户偏好的智能体类型
        if (userPreference != null && !CollectionUtils.isEmpty(userPreference.getPreferredAgentTypes())) {
            Set<Long> combinedTypeIds = new HashSet<>(relatedTypeAgentIds);
            combinedTypeIds.addAll(userPreference.getPreferredAgentTypes());
            relatedTypeAgentIds = new ArrayList<>(combinedTypeIds);
        }

        // 获取相似标签的智能体ID
        List<Long> relatedTagAgentIds = Optional.of(agentTagRelationMapper.findTagIdsByAgentIds(new ArrayList<>(interactedAgentIds)))
                .filter(tagIds -> !tagIds.isEmpty())
                .map(agentTagRelationMapper::findAgentIdsByTagIds)
                .orElse(Collections.emptyList());

        // 合并相关类型和标签的智能体ID
        Set<Long> relatedAgentIds = Stream.concat(relatedTagAgentIds.stream(), relatedTypeAgentIds.stream())
                .collect(Collectors.toSet());

        if (relatedAgentIds.isEmpty()) {
            log.info("No related agents found for user {}", username);
            return Collections.emptyList();
        }

        // 获取相关智能体列表
        List<AgentCacheVO> relatedAgents = agentList.stream()
                .filter(agent -> relatedAgentIds.contains(agent.getId()))
                .toList();

        if (relatedAgents.isEmpty()) {
            log.info("No related agents available for user {}", username);
            return Collections.emptyList();
        }

        // 分离未交互和已交互的智能体
        List<AgentCacheVO> nonInteractedAgents = relatedAgents.stream()
                .filter(agent -> !interactedAgentIds.contains(agent.getId()))
                .collect(Collectors.toList());

        List<AgentCacheVO> previouslyInteractedAgents = relatedAgents.stream()
                .filter(agent -> interactedAgentIds.contains(agent.getId()))
                .collect(Collectors.collectingAndThen(Collectors.toList(), list -> {
                    Collections.shuffle(list);
                    return list;
                }));

        // 根据用户性别偏好过滤和排序
        if (userPreference != null && userPreference.getPreferredGender() != null
                && (userPreference.getPreferredGender() == 1 || userPreference.getPreferredGender() == 2)) {

            // 按偏好性别分组并随机打乱
            List<AgentCacheVO> preferredGenderAgents = nonInteractedAgents.stream()
                    .filter(a -> Objects.equals(a.getGender(), userPreference.getPreferredGender()))
                    .collect(Collectors.collectingAndThen(Collectors.toList(), list -> {
                        Collections.shuffle(list);
                        return list;
                    }));

            List<AgentCacheVO> otherGenderAgents = nonInteractedAgents.stream()
                    .filter(a -> !Objects.equals(a.getGender(), userPreference.getPreferredGender()))
                    .collect(Collectors.collectingAndThen(Collectors.toList(), list -> {
                        Collections.shuffle(list);
                        return list;
                    }));

            // 按优先级顺序合并: 偏好性别 -> 历史交互 -> 其他性别
            return Stream.of(preferredGenderAgents, previouslyInteractedAgents, otherGenderAgents)
                    .flatMap(Collection::stream)
                    .collect(Collectors.toList());
        }

        // 如果没有性别偏好,直接合并未交互和已交互列表
        Collections.shuffle(nonInteractedAgents);
        return Stream.concat(nonInteractedAgents.stream(), previouslyInteractedAgents.stream())
                .collect(Collectors.toList());
    }


    /**
     * 检查智能体是否符合推荐条件
     */
    private boolean isValidRecommendation(AgentEntity agent) {
        if (agent == null || agent.getId() == null) {
            // log.debug("智能体为空或ID为空，不符合推荐条件"); // Keep log minimal for valid cases
            return false;
        }

        boolean isValid = agent.getIsPublic() == AgentConstant.AGENT_PUBLIC &&
                agent.getStatus() == AgentStatus.PUBLISHED.getCode() &&
                agent.getShelfStatus() == 1 &&
                agent.getDeleted() == 0;

        if (!isValid && log.isDebugEnabled()) { // Log only if invalid and debug are on
            log.debug("智能体ID: {} 不符合基本推荐条件 (isPublic={}, status={}, shelfStatus={}, deleted={})",
                    agent.getId(), agent.getIsPublic(), agent.getStatus(), agent.getShelfStatus(), agent.getDeleted());
        }
        return isValid;
    }

    /**
     * 从缓存安全获取ID列表
     */
    private List<Long> getAgentIdsFromCache(Cache cache, String cacheKey) {
        if (cache == null || !StringUtils.hasText(cacheKey)) {
            return Collections.emptyList();
        }
        try {
            Cache.ValueWrapper valueWrapper = cache.get(cacheKey);
            if (valueWrapper != null) {
                Object cachedValue = valueWrapper.get();
                if (cachedValue instanceof List<?> rawList) {
                    // 风险检查列表元素的类型(Type erasure 会导致此操作有风险)
                    if (rawList.isEmpty() || rawList.get(0) instanceof Long) {
                        @SuppressWarnings("unchecked")
                        List<Long> result = (List<Long>) rawList;
                        log.debug("Cache hit for key: {}. Found {} IDs.", cacheKey, result.size());
                        return result;
                    } else {
                        log.warn("Cache key {} contains a List, but not of Longs. Found type: {}", cacheKey, rawList.get(0).getClass().getName());
                        cache.evict(cacheKey); // Evict bad cache entry
                        return Collections.emptyList();
                    }
                } else if (cachedValue != null) {
                    log.warn("Cache key {} contains non-List data: {}. Evicting.", cacheKey, cachedValue.getClass().getName());
                    cache.evict(cacheKey); // Evict bad cache entry
                    return Collections.emptyList();
                }
            }
        } catch (Exception e) {
            log.warn("从缓存 {} 获取ID列表失败: {}", cacheKey, e.getMessage());
        }
        log.debug("Cache miss or error for key: {}", cacheKey);
        return Collections.emptyList();
    }

    /**
     * 记录推荐的智能体ID
     */
    private void logRecommendedIds(String prefix, Collection<Long> ids) {
        if (ids == null || ids.isEmpty()) { // Handle null ids
            log.info("{}: (空或null)", prefix);
            return;
        }

        Set<Long> uniqueIds = new HashSet<>(ids);
        boolean hasDuplicates = uniqueIds.size() < ids.size();

        StringBuilder sb = new StringBuilder();
        sb.append(prefix).append(" (").append(ids.size()).append("个");

        if (hasDuplicates) {
            sb.append(", 包含重复! 去重后").append(uniqueIds.size()).append("个");
        }
        sb.append("): ");

        // 避免不必要的集合复制，直接使用原集合或最小化复制
        Collection<Long> displayIds;
        if (ids instanceof List) {
            displayIds = ids;
        } else {
            List<Long> sortedIds = new ArrayList<>(uniqueIds);
            Collections.sort(sortedIds);
            displayIds = sortedIds;
        }

        int displayCount = Math.min(displayIds.size(), 20); // Log more IDs if available
        int currentIndex = 0;
        for (Long id : displayIds) {
            if (currentIndex >= displayCount) break;
            sb.append(id);
            if (currentIndex < displayCount - 1) {
                sb.append(", ");
            }
            currentIndex++;
        }
        if (displayIds.size() > displayCount) {
            sb.append("... (共").append(displayIds.size()).append("个)");
        }

        if (hasDuplicates) {
            log.warn(sb.toString());
            // Detailed duplicate logging can be verbose, consider if needed frequently
        } else {
            log.info(sb.toString());
        }
    }

    @Override
    public List<AgentEntity> recommendAgentsByUserCharacteristics(String username, Integer limit) {
        List<Long> recommendedIds = recommendAgentIdsByUserCharacteristics(username, limit, false);
        List<AgentEntity> entities = agentService.queryByIdsKeepOrder(recommendedIds);
        fillAgentsWithSocialInfoEfficiently(entities, username);
        return entities;
    }

    @Override
    public PageInfo<AgentEntity> recommendAgentsPageByUserCharacteristics(String username, int pageNum, int pageSize, Long firstAgentId) {
        log.info("开始为用户{}生成分页智能体推荐，页码:{}, 每页数量:{}, 首推智能体ID:{}", username, pageNum, pageSize, firstAgentId);
        long startTime = System.currentTimeMillis();

        pageNum = Math.max(1, pageNum);
        pageSize = Math.max(1, Math.min(100, pageSize));

        String cacheKey = cacheHelper.generatePageCacheKey(username, pageNum, pageSize, firstAgentId);
        Cache pageCache = cacheManager.getCache(AgentCacheConstant.AGENT_PAGE_CACHE);
        if (pageNum != 1) {
            PageInfo<Long> cachedIdsResult = getPageInfoFromCache(pageCache, cacheKey);

            if (cachedIdsResult != null) {
                PageInfo<AgentEntity> pageInfo = new PageInfo<>();
                pageInfo.setTotal(cachedIdsResult.getTotal());
                pageInfo.setPageNum(pageNum);
                pageInfo.setPageSize(pageSize);
                List<AgentEntity> entities = agentService.queryByIdsKeepOrder(cachedIdsResult.getList());
                fillAgentsWithSocialInfoEfficiently(entities, username);
                pageInfo.setSize(cachedIdsResult.getSize());
                pageInfo.setList(entities);
                log.debug("分页缓存命中，查询键: {}, 耗时: {}ms", cacheKey, System.currentTimeMillis() - startTime);
                return pageInfo;
            }
        }
        // 调整下获取底层推荐列表的数量
        // 只需要足够支持当前页和少量后续页面的数据，而不是固定的大量数据
        int neededRecommendations; // 获取当前页需要的2倍数据，确保有足够数据
        // 但不超过最大限制
        neededRecommendations = PAGINATION_UNDERLYING_LIST_LIMIT;
        List<Long> allRecommendedIds = recommendAgentIdsByUserCharacteristics(username, neededRecommendations, pageNum != 1);

        List<Long> mutableAgents = new ArrayList<>(allRecommendedIds);

        // Handle firstAgentId: move it to the top if present, and pageNum is 1
        if (firstAgentId != null && firstAgentId > 0 && pageNum == 1) {
            reorderListWithFirstAgent(mutableAgents, firstAgentId, username); // Reuse existing method
        }

        PageInfo<Long> resultIdPage = cacheHelper.createPageFromList(mutableAgents, pageNum, pageSize);

        if (!CollectionUtils.isEmpty(resultIdPage.getList())) {
            logRecommendedIds("分页后的推荐IDs (page " + pageNum + ")", new ArrayList<>(resultIdPage.getList()));
        }

        if (pageCache != null && !CollectionUtils.isEmpty(resultIdPage.getList())) {
            final PageInfo<Long> pageIdsToCache = resultIdPage; // effectively final for lambda
            CompletableFuture.runAsync(() -> {
                        try {
                            pageCache.put(cacheKey, pageIdsToCache);
                            log.debug("异步缓存分页结果完成 for key {}", cacheKey);
                        } catch (Exception e) {
                            log.warn("缓存分页结果失败 for key {}: {}", cacheKey, e.getMessage());
                        }
                    }, taskExecutor)
                    .orTimeout(10, TimeUnit.SECONDS)
                    .exceptionally(ex -> {
                        log.warn("异步缓存分页结果超时或失败 for key {}: {}", cacheKey, ex.getMessage());
                        return null;
                    });
        }

        PageInfo<AgentEntity> pageInfo = new PageInfo<>();

        // 设置分页信息
        pageInfo.setTotal(resultIdPage.getTotal());
        pageInfo.setPageNum(pageNum);
        pageInfo.setPageSize(pageSize);
        List<AgentEntity> entities = agentService.queryByIdsKeepOrder(resultIdPage.getList());
        fillAgentsWithSocialInfoEfficiently(entities, username);
        pageInfo.setList(entities);
        pageInfo.setSize((int) resultIdPage.getTotal());
        log.info("查询智能体推荐分页总耗时: {}ms (生成/数据库查询)", System.currentTimeMillis() - startTime);
        return pageInfo;
    }

    /**
     * 从缓存安全获取分页信息
     * 处理类型转换，避免类型安全警告
     */
    @SuppressWarnings("unchecked")
    private PageInfo<Long> getPageInfoFromCache(Cache cache, String cacheKey) {
        if (cache == null || !StringUtils.hasText(cacheKey)) {
            return null;
        }
        try {
            Cache.ValueWrapper valueWrapper = cache.get(cacheKey);
            if (valueWrapper != null) {
                Object cachedValue = valueWrapper.get();
                if (cachedValue instanceof PageInfo<?> pi) {
                    // 如果PageInfo存储特定的类，则基本检查可以更加健壮
                    if (pi.getList().isEmpty() || pi.getList().get(0) instanceof Long) {
                        return (PageInfo<Long>) cachedValue;
                    } else {
                        log.warn("Cache key {} for PageInfo contains a List, but not of AgentEntity. Found type: {}. Evicting.", cacheKey, pi.getList().get(0).getClass().getName());
                        cache.evict(cacheKey);
                        return null;
                    }
                } else if (cachedValue != null) {
                    log.warn("Cache key {} for PageInfo contains non-PageInfo data: {}. Evicting.", cacheKey, cachedValue.getClass().getName());
                    cache.evict(cacheKey); // Evict bad cache entry
                    return null;
                }
            }
        } catch (Exception e) {
            log.warn("从缓存获取分页信息失败 {}: {}", cacheKey, e.getMessage());
        }
        return null;
    }

    /**
     * 将首推智能体放到列表开头
     */
    private void reorderListWithFirstAgent(List<Long> agentList, Long firstAgentId, String username) {
        if (firstAgentId == null || firstAgentId <= 0 || CollectionUtils.isEmpty(agentList)) {
            return;
        }

        log.info("处理首推智能体，ID: {}", firstAgentId);

        Optional<Long> firstAgentOpt = agentList.stream().filter(id -> id.equals(firstAgentId)).findFirst();

        if (firstAgentOpt.isPresent()) {
            Long firstAgent = firstAgentOpt.get();
            agentList.remove(firstAgent);
            agentList.add(0, firstAgent);
            log.info("首推智能体{}已存在于列表中，已移至首位", firstAgent);
        } else {
            try {
                AgentEntity agent = agentService.findById(firstAgentId, username); // Assuming findById can take username for context or is adapted
                if (isValidRecommendation(agent)) { // Check if it's a valid agent to recommend
                    agentList.add(0, agent.getId());
                    log.info("首推智能体不在列表中，已获取并添加至首位");
                } else {
                    log.warn("首推智能体不符合推荐条件或未找到，ID: {}", firstAgentId);
                }
            } catch (Exception e) {
                log.warn("获取首推智能体失败: {}", e.getMessage());
            }
        }
    }

    /**
     * 高效地填充智能体列表的社交信息
     */
    private void fillAgentsWithSocialInfoEfficiently(List<AgentEntity> list, String username) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        List<Long> agentIds = list.stream()
                .filter(a -> a != null && a.getId() != null)
                .map(AgentEntity::getId)
                .distinct()
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(agentIds)) {
            return;
        }

        try {
            CompletableFuture<Map<Long, List<AgentTagEntity>>> tagsFuture =
                    CompletableFuture.supplyAsync(() -> fetchAgentTagsInBatch(agentIds))
                            .orTimeout(5, TimeUnit.SECONDS)
                            .exceptionally(ex -> {
                                log.error("获取智能体标签失败: {}", ex.getMessage(), ex);
                                return Collections.emptyMap();
                            });

            CompletableFuture<Map<Long, AgentStatisticsEntity>> statisticsFuture =
                    CompletableFuture.supplyAsync(statisticsService::findAllGroupByAgentId);


            CompletableFuture<Map<Long, Integer>> userLikesFuture;
            if (StringUtils.hasText(username)) {
                userLikesFuture = CompletableFuture.supplyAsync(() -> agentLikeService.batchIsLiked(agentIds, username))
                        .orTimeout(5, TimeUnit.SECONDS)
                        .exceptionally(ex -> {
                            log.warn("获取用户点赞状态失败: {}", ex.getMessage(), ex);
                            return Collections.emptyMap();
                        });
            } else {
                userLikesFuture = CompletableFuture.completedFuture(Collections.emptyMap());
            }

            CompletableFuture.allOf(
                    tagsFuture, statisticsFuture, userLikesFuture
            ).get(10, TimeUnit.SECONDS); // Adjusted timeout for allOf

            Map<Long, List<AgentTagEntity>> agentTagsMap = tagsFuture.get();
            Map<Long, AgentTypeEntity> typeMap = agentTypeService.findTypesMap();

            Map<Long, Integer> userLikesMap = userLikesFuture.get();
            Map<Long, AgentStatisticsEntity> statisticsMap = statisticsFuture.get();
            list.forEach(agent -> {
                if (agent == null || agent.getId() == null) return;
                Long agentId = agent.getId();

                if (agent.getTypeId() != null) {
                    agent.setType(typeMap.getOrDefault(agent.getTypeId(), null));
                }
                AgentStatisticsEntity statisticsEntity = statisticsMap.getOrDefault(agentId, new AgentStatisticsEntity());
                agent.setTags(agentTagsMap.getOrDefault(agentId, Collections.emptyList()));
                agent.setIsLiked(userLikesMap.getOrDefault(agentId, 0));
                agent.setLikeCount(statisticsEntity.getLikeCount());
                agent.setCommentCount(statisticsEntity.getCommentCount());
                agent.setFavoriteCount(statisticsEntity.getFavoriteCount());
            });
        } catch (Exception e) {
            log.error("获取智能体异步数据出错: {}", e.getMessage(), e);
            list.forEach(agent -> {
                if (agent != null) {
                    if (agent.getTags() == null) agent.setTags(Collections.emptyList());
                    if (agent.getIsLiked() == null) agent.setIsLiked(0);
                    if (agent.getLikeCount() == null) agent.setLikeCount(0);
                    if (agent.getCommentCount() == null) agent.setCommentCount(0);
                    if (agent.getFavoriteCount() == null) agent.setFavoriteCount(0);
                }
            });
        }
    }

    /**
     * 从智能体中批量获取标签
     */
    private Map<Long, List<AgentTagEntity>> fetchAgentTagsInBatch(List<Long> agentIds) {
        if (CollectionUtils.isEmpty(agentIds)) {
            return Collections.emptyMap();
        }
        List<Long> validAgentIds = agentIds.stream().filter(Objects::nonNull).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(validAgentIds)) {
            return Collections.emptyMap();
        }

        List<AgentTagRelationEntity> allTagRelations = agentTagRelationMapper.findByAgentIds(validAgentIds);
        if (CollectionUtils.isEmpty(allTagRelations)) {
            return Collections.emptyMap();
        }

        Set<Long> tagIdsFromRelations = allTagRelations.stream()
                .map(AgentTagRelationEntity::getTagId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(tagIdsFromRelations)) {
            return Collections.emptyMap();
        }

        Map<Long, AgentTagEntity> tagsMap = agentTagService.findTagsMap();
        if (CollectionUtils.isEmpty(tagsMap)) {
            return Collections.emptyMap();
        }

        Map<Long, List<AgentTagEntity>> result = new HashMap<>();
        Map<Long, List<Long>> agentToTagIdsMap = allTagRelations.stream()
                .filter(r -> r.getAgentId() != null && r.getTagId() != null)
                .collect(Collectors.groupingBy(AgentTagRelationEntity::getAgentId,
                        Collectors.mapping(AgentTagRelationEntity::getTagId, Collectors.toList())));

        agentToTagIdsMap.forEach((agentId, tagIds) -> {
            List<AgentTagEntity> tagsForAgent = tagIds.stream()
                    .map(tagsMap::get)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(tagsForAgent)) {
                result.put(agentId, tagsForAgent);
            }
        });
        return result;
    }
}