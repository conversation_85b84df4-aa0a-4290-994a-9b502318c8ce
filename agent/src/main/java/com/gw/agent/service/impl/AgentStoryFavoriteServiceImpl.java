package com.gw.agent.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gw.agent.constant.AgentCacheConstant;
import com.gw.agent.entity.AgentCountEntity;
import com.gw.agent.entity.AgentStoryEntity;
import com.gw.agent.entity.AgentStoryFavoriteEntity;
import com.gw.agent.mapper.sql.AgentStoryFavoriteMapper;
import com.gw.agent.mapper.sql.AgentStoryMapper;
import com.gw.agent.service.AgentStoryFavoriteService;
import com.gw.agent.service.AgentStoryStatisticsService;
import com.gw.common.notify.constant.NotifyConstant;
import com.gw.common.notify.dto.InteractiveNotifySubmitDTO;
import com.gw.common.notify.service.NotifyProxyService;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 智能体剧情收藏服务实现
 */
@Service
@RequiredArgsConstructor
@Log4j2
public class AgentStoryFavoriteServiceImpl extends ServiceImpl<AgentStoryFavoriteMapper, AgentStoryFavoriteEntity> implements AgentStoryFavoriteService {
    private static final String FAVORITE_LOCK_PREFIX = "favorite:lock:";
    private static final String UNFAVORITE_LOCK_PREFIX = "unfavorite:lock:";
    private static final int LOCK_EXPIRE_TIME = 10; // 锁过期时间10秒
    private final NotifyProxyService notifyProxyService;
    private final AgentStoryMapper agentStoryMapper;
    private final String CacheCntValue = "agentStoryFavoriteCnt";
    private final String CacheIsFavoriteValue = "agentStoryIsFavorite";
    private final AgentStoryStatisticsService statisticsService;
    private final RedisTemplate<String, Object> redisTemplate;

    @Override
    @Caching(evict = {
            @CacheEvict(value = CacheCntValue, key = "'user:' + #username"),
            @CacheEvict(value = CacheCntValue, key = "'story:' + #storyId"),
            @CacheEvict(value = CacheIsFavoriteValue, key = "'favoriteStoryId:' + ':' + #username"),
            @CacheEvict(value = AgentCacheConstant.AGENT_STORY_CACHE, key = "'id:' + #storyId"),
            @CacheEvict(value = AgentCacheConstant.AGENT_STORY_PAGE_CACHE, allEntries = true),
            @CacheEvict(value = CacheIsFavoriteValue, key = "'is:' + #storyId + ':' + #username")
    })
    @Transactional(rollbackFor = Exception.class)
    public void addFavorite(Long storyId, String username) {
        String lockKey = FAVORITE_LOCK_PREFIX + storyId + ":" + username;

        // 尝试获取分布式锁
        Boolean lockAcquired = redisTemplate.opsForValue().setIfAbsent(lockKey, "1", LOCK_EXPIRE_TIME, TimeUnit.SECONDS);
        if (!Boolean.TRUE.equals(lockAcquired)) {
            log.warn("用户{}正在收藏剧情{}，请勿重复提交", username, storyId);
            throw new RuntimeException("操作过于频繁，请稍后再试");
        }

        try {
            Optional<AgentStoryEntity> story = agentStoryMapper.findById(storyId);
            if (story.isEmpty()) {
                log.error("记录智能体剧情{}收藏失败，智能体剧情不存在", storyId);
                return;
            }
            AgentStoryEntity entity = story.get();
            // 查询是否已存在收藏记录
            Optional<AgentStoryFavoriteEntity> existingFavorite = this.baseMapper.findByStoryIdAndUsername(storyId,
                    username);

            if (existingFavorite.isPresent()) {
                // 如果已存在但状态为取消收藏，则更新状态为已收藏
                AgentStoryFavoriteEntity favorite = existingFavorite.get();
                if (favorite.getStatus() == 0) {
                    favorite.setStatus(1);
                    this.baseMapper.updateById(favorite);
                    InteractiveNotifySubmitDTO interactiveNotifySubmitDTO = new InteractiveNotifySubmitDTO(
                            "{nickname}收藏了我创作的剧情" + "\"" + entity.getName() + "\"",
                            NotifyConstant.INTERACTIVE_AGENT_STORY_FAVORITE,
                            storyId,
                            entity.getCreator(), username);
                    notifyProxyService.insertInteractiveMessage(interactiveNotifySubmitDTO);
                    statisticsService.updateFavoriteCount(storyId, 1);
                } else {
                    log.warn("用户{}已经收藏了剧情{}", username, storyId);
                   return;
                }
            } else {
                // 创建新地收藏记录
                AgentStoryFavoriteEntity favorite = new AgentStoryFavoriteEntity();
                favorite.setStoryId(storyId);
                favorite.setUsername(username);
                favorite.setStatus(1);
                favorite.setCreateTime(LocalDateTime.now());
                this.baseMapper.insert(favorite);
                InteractiveNotifySubmitDTO interactiveNotifySubmitDTO = new InteractiveNotifySubmitDTO(
                        "{nickname}收藏了我创作的剧情" + "\"" + entity.getName() + "\"",
                        NotifyConstant.INTERACTIVE_AGENT_STORY_FAVORITE,
                        storyId,
                        entity.getCreator(), username);
                notifyProxyService.insertInteractiveMessage(interactiveNotifySubmitDTO);
                statisticsService.updateFavoriteCount(storyId, 1);
            }
            agentStoryMapper.updatePopularity(storyId, (int) (Math.random() * 41 + 10));
        } finally {
            // 释放锁
            redisTemplate.delete(lockKey);
        }
    }

    @Override
    @Caching(evict = {
            @CacheEvict(value = CacheCntValue, key = "'user:' + #username"),
            @CacheEvict(value = CacheCntValue, key = "'story:' + #storyId"),
            @CacheEvict(value = CacheIsFavoriteValue, key = "'favoriteStoryId:' + ':' + #username"),
            @CacheEvict(value = AgentCacheConstant.AGENT_STORY_CACHE, key = "'id:' + #storyId"),
            @CacheEvict(value = AgentCacheConstant.AGENT_STORY_PAGE_CACHE, allEntries = true),
            @CacheEvict(value = CacheIsFavoriteValue, key = "'is:' + #storyId + ':' + #username")
    })
    @Transactional(rollbackFor = Exception.class)
    public boolean removeFavorite(Long storyId, String username) {
        String lockKey = UNFAVORITE_LOCK_PREFIX + storyId + ":" + username;

        // 尝试获取分布式锁
        Boolean lockAcquired = redisTemplate.opsForValue().setIfAbsent(lockKey, "1", LOCK_EXPIRE_TIME, TimeUnit.SECONDS);
        if (!Boolean.TRUE.equals(lockAcquired)) {
            log.warn("用户{}正在取消收藏剧情{}，请勿重复提交", username, storyId);
            throw new RuntimeException("操作过于频繁，请稍后再试");
        }

        try {
            Optional<AgentStoryFavoriteEntity> existingFavorite = this.baseMapper.findByStoryIdAndUsername(storyId,
                    username);

            if (existingFavorite.isPresent()) {
                AgentStoryFavoriteEntity favorite = existingFavorite.get();
                if (favorite.getStatus() == 1) {
                    // 更新状态为取消收藏
                    favorite.setStatus(0);
                    statisticsService.updateFavoriteCount(storyId, -1);
                    return this.baseMapper.updateById(favorite) > 0;
                } else {
                    log.warn("用户{}尝试取消收藏未收藏的剧情{}", username, storyId);
                    throw new RuntimeException("您还未收藏该剧情");
                }
            }
            return false;
        } finally {
            // 释放锁
            redisTemplate.delete(lockKey);
        }
    }

    @Override
    @Cacheable(value = CacheCntValue, key = "'story:' + #storyId")
    public int countByStoryId(Long storyId) {
        return this.baseMapper.countByStoryId(storyId);
    }

    @Override
    @Cacheable(value = CacheCntValue, key = "'user:' + #username")
    public int countByUsername(String username) {
        return this.baseMapper.countByUsername(username);
    }

    @Override
    public List<AgentStoryFavoriteEntity> getUserFavorites(String username) {
        return this.baseMapper.findByUsername(username);
    }

    @Cacheable(value = CacheIsFavoriteValue, key = "'favoriteStoryId:' + ':' + #username")
    @Override
    public List<Long> findUserFavoriteStoryIds(String username) {
        return this.baseMapper.findUserFavoriteStoryIds(username);
    }

    @Override
    @Cacheable(value = CacheIsFavoriteValue, key = "'is:' + #storyId + ':' + #username")
    public Integer isFavorite(Long storyId, String username) {
        Optional<AgentStoryFavoriteEntity> favorite = this.baseMapper.findByStoryIdAndUsername(storyId, username);
        return favorite.map(AgentStoryFavoriteEntity::getStatus).orElse(0);
    }

    @Override
    public List<AgentStoryFavoriteEntity> getStoryFavorites(Long storyId) {
        return this.baseMapper.findByStoryId(storyId);
    }

    /**
     * 批量获取收藏数
     */
    @Override
    public Map<Long, Integer> batchGetFavoriteCounts(List<Long> storyIds) {
        if (CollectionUtils.isEmpty(storyIds)) {
            return Collections.emptyMap();
        }
        List<AgentCountEntity> entityList = this.baseMapper.batchCountByStoryIds(storyIds);
        return entityList.stream()
                .collect(Collectors.toMap(
                        AgentCountEntity::getAgentId,
                        AgentCountEntity::getCount,
                        (v1, v2) -> v1,
                        HashMap::new
                ));
    }
}