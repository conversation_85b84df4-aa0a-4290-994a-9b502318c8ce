package com.gw.agent.service;

import com.gw.agent.entity.AgentLikeEntity;

import java.util.List;
import java.util.Map;

/**
 * 智能体点赞服务接口
 */
public interface AgentLikeService {

    /**
     * 添加点赞
     *
     * @param agentId  智能体ID
     * @param username 用户名
     */
    void addLike(Long agentId, String username);

    /**
     * 取消点赞
     *
     * @param agentId  智能体ID
     * @param username 用户名
     * @return 是否成功
     */
    boolean removeLike(Long agentId, String username);

    /**
     * 查询用户点赞的智能体列表
     *
     * @param username 用户名
     * @return 点赞的智能体列表
     */
    List<AgentLikeEntity> getUserLikes(String username);

    List<Long> findUserLikesAgentIds(String username);

    int getUserLikeCnt(String username);

    /**
     * 查询智能体是否被用户点赞
     *
     * @param agentId  智能体ID
     * @param username 用户名
     * @return 是否已点赞：1-已点赞，0-未点赞
     */
    Integer isLiked(Long agentId, String username);

    /**
     * 查询智能体的点赞记录
     *
     * @param agentId 智能体ID
     * @return 点赞记录列表
     */
    List<AgentLikeEntity> getAgentLikes(Long agentId);

    /**
     * 获取智能体的点赞数
     *
     * @param agentId 智能体ID
     * @return 点赞数
     */
    int getLikeCount(Long agentId);

    /**
     * 批量获取智能体点赞数
     *
     * @param agentIds 智能体ID列表
     * @return 智能体ID到点赞数的映射
     */
    Map<Long, Integer> batchGetLikeCounts(List<Long> agentIds);

    /**
     * 批量查询智能体是否被用户点赞
     *
     * @param agentIds 智能体ID列表
     * @param username 用户名
     * @return 智能体ID到点赞状态的映射（1-已点赞，0-未点赞）
     */
    Map<Long, Integer> batchIsLiked(List<Long> agentIds, String username);

    /**
     * 获取用户标记为不感兴趣的智能体ID列表
     *
     * @param username 用户名
     * @return 不感兴趣的智能体ID列表
     */
    List<Long> findDislikedByUsername(String username);
}