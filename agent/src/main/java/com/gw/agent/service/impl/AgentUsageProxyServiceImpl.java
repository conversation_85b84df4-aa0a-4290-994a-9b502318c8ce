package com.gw.agent.service.impl;

import com.gw.agent.service.AgentService;
import com.gw.agent.service.AgentUsageRecordService;
import com.gw.common.agent.service.AgentUsageProxyService;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

/**
 * 智能体使用代理服务实现
 */
@Service
@RequiredArgsConstructor
@Log4j2
public class AgentUsageProxyServiceImpl implements AgentUsageProxyService {

    private final AgentService agentService;
    private final AgentUsageRecordService agentUsageRecordService;

    @Override
    public boolean recordAgentUsage(Long agentId, String username) {
        return agentService.recordAgentUsage(agentId, username);
    }

    @Override
    public int getAgentUserCount(Long agentId) {
        return agentUsageRecordService.getUserCount(agentId);
    }
}