package com.gw.agent.service;

import com.gw.agent.entity.AgentStoryFavoriteEntity;

import java.util.List;
import java.util.Map;

/**
 * 智能体剧情收藏服务接口
 */
public interface AgentStoryFavoriteService {

    /**
     * 添加收藏
     *
     * @param storyId  智能体剧情ID
     * @param username 用户名
     */
    void addFavorite(Long storyId, String username);

    /**
     * 取消收藏
     *
     * @param storyId  智能体剧情ID
     * @param username 用户名
     * @return 是否成功
     */
    boolean removeFavorite(Long storyId, String username);

    /**
     * 根据智能体剧情ID统计收藏数
     *
     * @param storyId 智能体剧情ID
     * @return 收藏数
     */
    int countByStoryId(Long storyId);

    /**
     * 根据用户名统计收藏数
     *
     * @param username 用户名
     * @return 收藏数
     */
    int countByUsername(String username);

    /**
     * 查询用户收藏的智能体剧情列表
     *
     * @param username 用户名
     * @return 收藏的智能体剧情列表
     */
    List<AgentStoryFavoriteEntity> getUserFavorites(String username);

    /**
     * 查询用户收藏的智能体剧情ID列表
     *
     * @param username 用户名
     * @return 收藏的智能体剧情ID列表
     */
    List<Long> findUserFavoriteStoryIds(String username);

    /**
     * 查询智能体剧情是否被用户收藏
     *
     * @param storyId  智能体剧情ID
     * @param username 用户名
     * @return 是否已收藏：1-已收藏，0-未收藏
     */
    Integer isFavorite(Long storyId, String username);

    /**
     * 查询智能体剧情的收藏记录
     *
     * @param storyId 智能体剧情ID
     * @return 收藏记录列表
     */
    List<AgentStoryFavoriteEntity> getStoryFavorites(Long storyId);

    /**
     * 批量获取收藏数
     *
     * @param storyIds 智能体剧情ID列表
     * @return 智能体剧情ID到收藏数的映射
     */
    Map<Long, Integer> batchGetFavoriteCounts(List<Long> storyIds);
}