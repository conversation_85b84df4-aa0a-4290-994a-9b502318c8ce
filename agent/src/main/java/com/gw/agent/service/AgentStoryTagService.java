package com.gw.agent.service;

import com.gw.agent.entity.AgentStoryTagEntity;

import java.util.List;
import java.util.Map;

/**
 * 智能体剧情标签服务接口
 */
public interface AgentStoryTagService {

    /**
     * 插入标签
     *
     * @param entity 标签实体
     */
    void insert(AgentStoryTagEntity entity);

    /**
     * 更新标签
     *
     * @param entity 标签实体
     */
    void update(AgentStoryTagEntity entity);

    /**
     * 删除标签
     *
     * @param entity 标签实体
     */
    void delete(AgentStoryTagEntity entity);

    /**
     * 根据ID查找标签
     *
     * @param id 标签ID
     * @return 标签实体
     */
    AgentStoryTagEntity findById(Long id);

    /**
     * 查找所有标签
     *
     * @return 标签列表
     */
    List<AgentStoryTagEntity> findAll();

    /**
     * 检查标签名称在指定分类下是否存在
     *
     * @param name     标签名称
     * @param category 标签分类
     * @return 是否存在
     */
    boolean isNameExistByCategory(String name, Integer category);

    /**
     * 获取标签映射
     *
     * @return 标签ID到标签实体的映射
     */
    Map<Long, AgentStoryTagEntity> findTagsMap();

    /**
     * 获取热门标签
     *
     * @param limit 限制数量
     * @return 热门标签列表
     */
    List<AgentStoryTagEntity> findHotTags(Integer limit);

    /**
     * 增加标签使用次数
     *
     * @param tagId     标签ID
     * @param increment 增加数量
     */
    void incrementUseCount(Long tagId, Integer increment);
}