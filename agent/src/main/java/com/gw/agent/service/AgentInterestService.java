package com.gw.agent.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gw.agent.entity.AgentInterestEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 智能体兴趣服务接口
 */
public interface AgentInterestService extends IService<AgentInterestEntity> {

    /**
     * 判断用户是否对智能体感兴趣
     *
     * @param agentId  智能体ID
     * @param username 用户名
     * @return 兴趣级别：0-无兴趣，1-轻度兴趣，2-中度兴趣，3-高度兴趣
     */
    int getInterestLevel(Long agentId, String username);

    /**
     * 标记用户对智能体的兴趣
     *
     * @param agentId       智能体ID
     * @param username      用户名
     * @param interestLevel 兴趣级别
     * @param source        来源
     * @return 是否成功
     */
    boolean markInterest(Long agentId, String username, Integer interestLevel, Integer source);

    /**
     * 取消用户对智能体的兴趣标记
     *
     * @param agentId  智能体ID
     * @param username 用户名
     * @return 是否成功
     */
    boolean unmarkInterest(Long agentId, String username);

    /**
     * 获取智能体的兴趣数量
     *
     * @param agentId 智能体ID
     * @return 兴趣数量
     */
    int getInterestCount(Long agentId);

    /**
     * 获取用户的兴趣智能体列表
     *
     * @param username 用户名
     * @return 兴趣智能体ID列表
     */
    List<Long> getInterestedAgentIds(String username);

    /**
     * 获取智能体的兴趣用户列表
     *
     * @param agentId 智能体ID
     * @return 兴趣用户名列表
     */
    List<String> getInterestedUsernames(Long agentId);

    List<Long> findUnmarkedByUsername(@Param("username") String username);
}