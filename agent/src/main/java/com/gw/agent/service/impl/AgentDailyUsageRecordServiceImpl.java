package com.gw.agent.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gw.agent.entity.AgentDailyUsageRecordEntity;
import com.gw.agent.mapper.sql.AgentDailyUsageRecordMapper;
import com.gw.agent.service.AgentDailyUsageRecordService;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.List;

/**
 * 用户每日使用智能体记录服务实现类
 */
@Log4j2
@Service
public class AgentDailyUsageRecordServiceImpl extends ServiceImpl<AgentDailyUsageRecordMapper, AgentDailyUsageRecordEntity> implements AgentDailyUsageRecordService {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AgentDailyUsageRecordEntity recordDailyUsage(Long agentId, String username) {
        LocalDate today = LocalDate.now();

        // 查询当日是否已有记录
        AgentDailyUsageRecordEntity record = this.findByAgentIdAndUsernameAndDate(agentId, username, today);

        if (record == null) {
            // 当日首次使用，创建新记录
            record = new AgentDailyUsageRecordEntity();
            record.setAgentId(agentId);
            record.setUsername(username);
            record.setUseDate(today);
            record.setUseCount(1);
            record.setStatus(1);
            this.save(record);
        } else {
            // 已有记录，更新使用次数
            record.setUseCount(record.getUseCount() + 1);
            this.updateById(record);
        }

        return record;
    }

    @Override
    public AgentDailyUsageRecordEntity findByAgentIdAndUsernameAndDate(Long agentId, String username, LocalDate date) {
        LambdaQueryWrapper<AgentDailyUsageRecordEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AgentDailyUsageRecordEntity::getAgentId, agentId)
                .eq(AgentDailyUsageRecordEntity::getUsername, username)
                .eq(AgentDailyUsageRecordEntity::getUseDate, date);

        return this.getOne(queryWrapper);
    }

    @Override
    public List<AgentDailyUsageRecordEntity> findByAgentIdAndDateRange(Long agentId, LocalDate startDate, LocalDate endDate) {
        LambdaQueryWrapper<AgentDailyUsageRecordEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AgentDailyUsageRecordEntity::getAgentId, agentId)
                .ge(AgentDailyUsageRecordEntity::getUseDate, startDate)
                .le(AgentDailyUsageRecordEntity::getUseDate, endDate)
                .orderByAsc(AgentDailyUsageRecordEntity::getUseDate);

        return this.list(queryWrapper);
    }

    @Override
    public List<AgentDailyUsageRecordEntity> findByUsernameAndDateRange(String username, LocalDate startDate, LocalDate endDate) {
        LambdaQueryWrapper<AgentDailyUsageRecordEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AgentDailyUsageRecordEntity::getUsername, username)
                .ge(AgentDailyUsageRecordEntity::getUseDate, startDate)
                .le(AgentDailyUsageRecordEntity::getUseDate, endDate)
                .orderByAsc(AgentDailyUsageRecordEntity::getUseDate);

        return this.list(queryWrapper);
    }

    @Override
    public int countDailyUsersByAgentId(Long agentId, LocalDate date) {
        LambdaQueryWrapper<AgentDailyUsageRecordEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AgentDailyUsageRecordEntity::getAgentId, agentId)
                .eq(AgentDailyUsageRecordEntity::getUseDate, date)
                .select(AgentDailyUsageRecordEntity::getUsername);

        // 统计去重后的用户数
        return Math.toIntExact(this.baseMapper.selectCount(queryWrapper));
    }

    @Override
    public int countDailyAgentsByUsername(String username, LocalDate date) {
        LambdaQueryWrapper<AgentDailyUsageRecordEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AgentDailyUsageRecordEntity::getUsername, username)
                .eq(AgentDailyUsageRecordEntity::getUseDate, date)
                .select(AgentDailyUsageRecordEntity::getAgentId);

        // 统计去重后的智能体数
        return Math.toIntExact(this.baseMapper.selectCount(queryWrapper));
    }
} 