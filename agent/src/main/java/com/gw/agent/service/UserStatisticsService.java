package com.gw.agent.service;

import com.gw.agent.vo.DailyStatisticsVO;
import com.gw.agent.vo.MonthlyStatisticsVO;

import java.util.List;

/**
 * 用户统计服务接口
 */
public interface UserStatisticsService {

    /**
     * 获取过去指定个月的每月用户统计数据
     *
     * @param months 月数
     * @return 月度统计数据列表
     */
    List<MonthlyStatisticsVO> getMonthlyStatistics(int months);

    /**
     * 获取过去指定天数的每日用户统计数据
     *
     * @param days 天数
     * @return 日度统计数据列表
     */
    List<DailyStatisticsVO> getDailyStatistics(int days);

    /**
     * 获取指定月份内每天的用户统计数据
     *
     * @param months 月数
     * @return 日度统计数据列表
     */
    List<DailyStatisticsVO> getMonthlyDailyStatistics(int months);
} 