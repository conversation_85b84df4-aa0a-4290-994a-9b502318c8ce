package com.gw.agent.service;

import com.gw.agent.entity.AgentStoryUsageRecordEntity;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 智能体剧情使用记录服务接口
 */
public interface AgentStoryUsageRecordService {

    /**
     * 记录用户使用智能体剧情
     * 如果是首次使用，则创建新记录，否则更新使用次数
     *
     * @param storyId  智能体剧情ID
     * @param username 用户名
     * @return 是否是首次使用
     */
    boolean recordUsage(Long storyId, String username);

    /**
     * 查询智能体剧情的使用记录
     *
     * @param storyId  智能体剧情ID
     * @param username 用户名
     * @return 使用记录
     */
    AgentStoryUsageRecordEntity findByStoryIdAndUsername(Long storyId, String username);

    /**
     * 获取智能体剧情的使用人数
     *
     * @param storyId 智能体剧情ID
     * @return 使用人数
     */
    int getUserCount(Long storyId);

    /**
     * 统计用户使用的智能体剧情数量
     *
     * @param username 用户名
     * @return 使用的智能体剧情数量
     */
    int countByUsername(String username);

    /**
     * 查询用户使用过的所有智能体剧情ID列表
     *
     * @param username 用户名
     * @return 智能体剧情ID列表
     */
    List<Long> findStoryIdsByUsername(String username);

    /**
     * 统计指定时间范围内的活跃用户数
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 活跃用户数
     */
    int countTodayActiveUsers(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 查询智能体剧情的使用记录列表
     *
     * @param storyId 智能体剧情ID
     * @return 使用记录列表
     */
    List<AgentStoryUsageRecordEntity> findByStoryId(Long storyId);

    /**
     * 查询用户的使用记录列表
     *
     * @param username 用户名
     * @return 使用记录列表
     */
    List<AgentStoryUsageRecordEntity> findByUsername(String username);
}