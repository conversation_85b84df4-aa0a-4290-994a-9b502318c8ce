package com.gw.agent.service;

import com.gw.agent.entity.AgentStoryDailyUsageRecordEntity;

import java.time.LocalDate;
import java.util.List;

/**
 * 智能体剧情每日使用记录服务接口
 */
public interface AgentStoryDailyUsageRecordService {

    /**
     * 记录用户当日使用智能体剧情
     * 如果当日首次使用，则创建新记录，否则更新使用次数
     *
     * @param storyId  智能体剧情ID
     * @param username 用户名
     * @return 更新后的记录
     */
    AgentStoryDailyUsageRecordEntity recordDailyUsage(Long storyId, String username);

    /**
     * 查询用户在指定日期使用智能体剧情的记录
     *
     * @param storyId  智能体剧情ID
     * @param username 用户名
     * @param date     日期
     * @return 使用记录
     */
    AgentStoryDailyUsageRecordEntity findByStoryIdAndUsernameAndDate(Long storyId, String username, LocalDate date);

    /**
     * 获取指定日期范围内智能体剧情的使用记录
     *
     * @param storyId   智能体剧情ID
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 使用记录列表
     */
    List<AgentStoryDailyUsageRecordEntity> findByStoryIdAndDateRange(Long storyId, LocalDate startDate, LocalDate endDate);

    /**
     * 获取用户在指定日期范围内的使用记录
     *
     * @param username  用户名
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 使用记录列表
     */
    List<AgentStoryDailyUsageRecordEntity> findByUsernameAndDateRange(String username, LocalDate startDate, LocalDate endDate);

    /**
     * 统计智能体剧情在指定日期的使用人数
     *
     * @param storyId 智能体剧情ID
     * @param date    日期
     * @return 使用人数
     */
    int countDailyUsersByStoryId(Long storyId, LocalDate date);

    /**
     * 统计用户在指定日期使用的智能体剧情数量
     *
     * @param username 用户名
     * @param date     日期
     * @return 使用的智能体剧情数量
     */
    int countDailyStoriesByUsername(String username, LocalDate date);
}