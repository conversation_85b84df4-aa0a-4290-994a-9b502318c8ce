package com.gw.agent.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.gw.agent.dto.AgentStoryQueryDTO;
import com.gw.agent.dto.MyCommentStoryQueryDTO;
import com.gw.agent.dto.MyFavoriteStoryQueryDTO;
import com.gw.agent.dto.MyLikeStoryQueryDTO;
import com.gw.agent.entity.*;
import com.gw.agent.exception.SecurityCheckException;
import com.gw.agent.mapper.sql.*;
import com.gw.agent.service.*;
import com.gw.common.agent.constant.StoryConstant;
import com.gw.common.dto.PageOrderField;
import com.gw.common.exception.EntityNotFoundException;
import com.gw.common.notify.constant.NotifyConstant;
import com.gw.common.notify.dto.SystemNotifySubmitDTO;
import com.gw.common.notify.service.NotifyProxyService;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Log4j2
public class AgentStoryServiceImpl extends ServiceImpl<AgentStoryMapper, AgentStoryEntity> implements AgentStoryService {
    private final AgentStorySceneMapper sceneMapper;
    private final AgentStoryTypeMapper storyTypeMapper;
    private final SceneAgentRelationMapper sceneAgentRelationMapper;
    private final SecurityCheckService securityCheckService;
    private final AgentStoryStatisticsService statisticsService;
    private final StoryCurrentUseSceneMapper storyCurrentUseSceneMapper;
    private final AgentStoryFavoriteService favoriteService;
    private final AgentStoryLikeService likeService;
    private final NotifyProxyService notifyProxyService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insertStory(AgentStoryEntity story) {
        log.info("开始创建剧情: {}", story.getName());
        // 设置审核中状态
        story.setStatus(StoryConstant.STORY_CHECK_STATUS);
        story.setId(null);
        this.baseMapper.insert(story);
        if (story.getScenes() != null) {
            story.getScenes().forEach(scene -> {
                scene.setStoryId(story.getId());
                scene.setId(null);
                sceneMapper.insert(scene);
                if (scene.getAgentRelations() != null) {
                    scene.getAgentRelations().forEach(relation -> {
                        relation.setSceneId(scene.getId());
                        relation.setStoryId(story.getId());
                        relation.setId(null);
                        sceneAgentRelationMapper.insert(relation);
                    });
                }
            });
        }
        // 执行安全检查
        performStorySecurityCheck(story);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateStory(AgentStoryEntity story) {
        log.info("开始更新剧情: {}", story.getName());
        // 设置审核中状态
        story.setStatus(StoryConstant.STORY_CHECK_STATUS);
        this.baseMapper.updateById(story);

        List<Long> oldSceneIds = sceneMapper.selectSceneIdsByStoryId(story.getId());
        if (story.getScenes() == null || story.getScenes().isEmpty()) {
            sceneMapper.deleteByStoryId(story.getId(), story.getUpdateTime(), story.getUpdater());
            return;
        }

        List<Long> newSceneIds = story.getScenes().stream()
                .filter(scene -> scene.getId() != null && scene.getId() > 0)
                .map(AgentStorySceneEntity::getId)
                .toList();

        List<Long> sceneIdsToDelete = oldSceneIds.stream()
                .filter(id -> !newSceneIds.contains(id))
                .toList();
        if (!sceneIdsToDelete.isEmpty()) {
            sceneMapper.deleteBySceneIds(sceneIdsToDelete, story.getUpdateTime(), story.getUpdater());
        }

        story.getScenes().forEach(scene -> {
            scene.setStoryId(story.getId());
            if (scene.getId() == null || scene.getId() == 0) {
                scene.setId(null);
                sceneMapper.insert(scene);
            } else {
                sceneMapper.updateById(scene);
                sceneAgentRelationMapper.deleteBySceneId(scene.getId());
            }
            if (scene.getAgentRelations() != null) {
                scene.getAgentRelations().forEach(relation -> {
                    relation.setSceneId(scene.getId());
                    relation.setStoryId(story.getId());
                    relation.setId(null);
                    sceneAgentRelationMapper.insert(relation);
                });
            }
        });
        // 执行安全检查
        performStorySecurityCheck(story);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteStory(AgentStoryEntity story) {
        log.info("开始删除剧情: {}, id: {}", story.getName(), story.getId());

        try {
            // 删除场景关联的智能体关系
            List<Long> sceneIds = sceneMapper.selectSceneIdsByStoryId(story.getId());
            if (!sceneIds.isEmpty()) {
                for (Long sceneId : sceneIds) {
                    sceneAgentRelationMapper.deleteBySceneId(sceneId);
                }
            }

            // 删除剧情关联的场景
            sceneMapper.deleteByStoryId(story.getId(), story.getUpdateTime(), story.getUpdater());

            // 删除剧情本身
            this.baseMapper.deleteById(story.getId());

            log.info("删除剧情成功: {}, id: {}", story.getName(), story.getId());
        } catch (Exception e) {
            log.error("删除剧情失败: {}, id: {}, 错误: {}", story.getName(), story.getId(), e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public AgentStoryEntity findStoryById(Long id) {
        return this.baseMapper.findById(id).orElseThrow(() -> new EntityNotFoundException("群聊不存在: " + id));
    }

    @Override
    public AgentStoryEntity findStoryDetailById(Long id) {
        log.info("开始查询剧情详情, id: {}", id);

        // 查询剧情基本信息
        AgentStoryEntity story = this.baseMapper.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("剧情不存在: " + id));

        try {
            // 填充剧情类型信息
            if (story.getStoryTypeId() != null) {
                AgentStoryTypeEntity storyType = storyTypeMapper.selectById(story.getStoryTypeId());
                story.setStoryType(storyType);
            }

            // 查询并填充场景信息
            List<AgentStorySceneEntity> scenes = sceneMapper.findByStoryId(id);
            if (scenes != null && !scenes.isEmpty()) {
                // 为每个场景填充智能体关系
                for (AgentStorySceneEntity scene : scenes) {
                    List<SceneAgentRelationEntity> agentRelations = sceneAgentRelationMapper.findBySceneId(scene.getId());
                    scene.setAgentRelations(agentRelations);
                }
                scenes.sort(Comparator.comparingInt(AgentStorySceneEntity::getSceneOrder));

                story.setScenes(scenes);
                story.setSceneCount(scenes.size());
            } else {
                story.setScenes(new ArrayList<>());
                story.setSceneCount(0);
            }
            AgentStoryStatisticsEntity statistics = statisticsService.findByStoryId(story.getId());
            if (statistics != null) {
                story.setLikeCount(statistics.getLikeCount());
                story.setFavoriteCount(statistics.getFavoriteCount());
                story.setCommentCount(statistics.getCommentCount());
            }else{
                story.setLikeCount(0);
                story.setFavoriteCount(0);
                story.setCommentCount(0);
            }
            log.info("查询剧情详情成功, id: {}, 场景数量: {}", id, story.getSceneCount());
            return story;

        } catch (Exception e) {
            log.error("查询剧情详情失败, id: {}, 错误: {}", id, e.getMessage(), e);
            throw new RuntimeException("查询剧情详情失败: " + e.getMessage(), e);
        }
    }

    @Override
    public List<Long> findStoryIdsByByName(String name) {
        return this.baseMapper.findStoryIdsByByName(name);
    }

    @Override
    public AgentStorySceneEntity findByBySceneId(Long sceneId) {
        return sceneMapper.findById(sceneId).orElseThrow(() -> new EntityNotFoundException("群聊场景不存在: " + sceneId));
    }

    @Override
    public SceneAgentRelationEntity findBySceneIdAndAgentId(Long sceneId, Long agentId) {
        return sceneAgentRelationMapper.findBySceneIdAndAgentId(sceneId, agentId).orElse(null);
    }

    @Override
    public PageInfo<AgentStoryEntity> page(int pageNum, int pageSize, AgentStoryQueryDTO query, PageOrderField orderFields) {
        long startTime = System.currentTimeMillis();
        log.info("开始执行剧情分页查询, pageNum: {}, pageSize: {}, orderFields: {}", pageNum, pageSize, orderFields);

        // 规范化参数
        pageNum = Math.max(1, pageNum);
        pageSize = Math.max(1, Math.min(100, pageSize)); // 设置最大页大小限制，避免大查询

        // 处理排序字段
        if (orderFields != null && orderFields.getKey() != null) {
            if (orderFields.getKey().equalsIgnoreCase("updatetime")) {
                orderFields.setKey("update_time");
            } else if (orderFields.getKey().equalsIgnoreCase("createtime")) {
                orderFields.setKey("create_time");
            }
        }

        try {
            // 使用PageHelper进行分页查询
            PageHelper.startPage(pageNum, pageSize, true);
            List<AgentStoryEntity> list = this.baseMapper.page(query, orderFields);

            log.debug("剧情分页查询完成，获取到{}条记录，耗时: {}ms", list.size(), System.currentTimeMillis() - startTime);

            // 如果有数据，填充相关信息
            if (!CollectionUtils.isEmpty(list)) {
                long enhanceStart = System.currentTimeMillis();
                fillStoriesWithAdditionalInfo(list);
                log.debug("剧情附加信息填充完成，耗时: {}ms", System.currentTimeMillis() - enhanceStart);
            }

            PageInfo<AgentStoryEntity> result = new PageInfo<>(list);
            log.info("剧情分页查询总耗时: {}ms", System.currentTimeMillis() - startTime);
            return result;

        } catch (Exception e) {
            log.error("执行剧情分页查询出错: {}, 耗时: {}ms", e.getMessage(), System.currentTimeMillis() - startTime, e);

            // 出错时返回空结果
            PageInfo<AgentStoryEntity> emptyResult = new PageInfo<>(new ArrayList<>());
            emptyResult.setPageNum(pageNum);
            emptyResult.setPageSize(pageSize);
            emptyResult.setTotal(0);
            emptyResult.setPages(0);

            log.info("返回空结果，总耗时: {}ms", System.currentTimeMillis() - startTime);
            return emptyResult;
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void insertScene(AgentStorySceneEntity entity) throws Exception {
        log.info("开始创建场景: {}", entity.getSceneName());

        // 验证剧情是否存在
        if (entity.getStoryId() == null) {
            throw new IllegalArgumentException("剧情ID不能为空");
        }

        this.baseMapper.findById(entity.getStoryId())
                .orElseThrow(() -> new EntityNotFoundException("剧情不存在: " + entity.getStoryId()));
        entity.setId(null);
        // 插入场景
        sceneMapper.insert(entity);

        // 插入场景关联的智能体关系
        if (entity.getAgentRelations() != null && !entity.getAgentRelations().isEmpty()) {
            entity.getAgentRelations().forEach(relation -> {
                relation.setSceneId(entity.getId());
                relation.setStoryId(entity.getStoryId());
                relation.setId(null);
                sceneAgentRelationMapper.insert(relation);
            });
        }

        // 执行场景安全检查
        performSceneSecurityCheck(entity);

        log.info("创建场景成功: {}, id: {}", entity.getSceneName(), entity.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateScene(AgentStorySceneEntity entity) throws Exception {
        log.info("开始更新场景: {}, id: {}", entity.getSceneName(), entity.getId());

        // 验证场景是否存在
        if (entity.getId() == null) {
            throw new IllegalArgumentException("场景ID不能为空");
        }

        // 更新场景基本信息
        sceneMapper.updateById(entity);

        // 删除原有的智能体关系
        sceneAgentRelationMapper.deleteBySceneId(entity.getId());

        // 插入新的智能体关系
        if (entity.getAgentRelations() != null && !entity.getAgentRelations().isEmpty()) {
            entity.getAgentRelations().forEach(relation -> {
                relation.setSceneId(entity.getId());
                relation.setStoryId(entity.getStoryId());
                relation.setId(null);
                sceneAgentRelationMapper.insert(relation);
            });
        }

        // 执行场景安全检查
        performSceneSecurityCheck(entity);

        log.info("更新场景成功: {}, id: {}", entity.getSceneName(), entity.getId());
    }

    /**
     * 执行场景安全检查（同步）
     * 检查场景中的文本和图篇内容是否违规
     *
     * @param scene 场景实体
     * @throws Exception 检查异常
     */
    private void performSceneSecurityCheck(AgentStorySceneEntity scene) throws Exception {
        log.info("开始执行场景安全检查, sceneId: {}, sceneName: {}", scene.getId(), scene.getSceneName());

        String scenePrefix = "场景[" + scene.getSceneName() + "]";
        Long storyId = scene.getStoryId();

        try {
            // 检查场景名称
            if (scene.getSceneName() != null) {
                securityCheckService.checkTextWithTencentCOS(scenePrefix + "名称", scene.getSceneName());
            }

            // 检查场景简介
            if (scene.getSceneDescription() != null) {
                securityCheckService.checkTextWithTencentCOS(scenePrefix + "简介", scene.getSceneDescription());
            }

            // 检查场景开场白
            if (scene.getOpeningText() != null) {
                securityCheckService.checkTextWithTencentCOS(scenePrefix + "开场白", scene.getOpeningText());
            }

            // 检查场景脚本内容
            if (scene.getSceneScript() != null) {
                securityCheckService.checkTextWithTencentCOS(scenePrefix + "脚本", scene.getSceneScript());
            }

            // 检查场景背景图片
            if (scene.getBgUrl() != null) {
                securityCheckService.checkImageWithTencentCOS(scenePrefix + "背景图片", scene.getBgUrl(), storyId);
            }

            log.info("场景安全检查通过, sceneId: {}, sceneName: {}", scene.getId(), scene.getSceneName());

        } catch (Exception e) {
            log.error("场景安全检查失败, sceneId: {}, sceneName: {}, 错误: {}", scene.getId(), scene.getSceneName(), e.getMessage(), e);
            throw e;
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteScene(AgentStorySceneEntity entity) {
        log.info("开始删除场景: {}, id: {}", entity.getSceneName(), entity.getId());

        try {
            // 验证场景是否存在
            if (entity.getId() == null) {
                throw new IllegalArgumentException("场景ID不能为空");
            }
            // 删除场景关联的智能体关系
            sceneAgentRelationMapper.deleteBySceneId(entity.getId());

            // 删除场景本身
            sceneMapper.deleteById(entity.getId());

            log.info("删除场景成功: {}, id: {}", entity.getSceneName(), entity.getId());
        } catch (Exception e) {
            log.error("删除场景失败: {}, id: {}, 错误: {}", entity.getSceneName(), entity.getId(), e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public AgentStorySceneEntity findSceneDetailById(Long id) {
        log.info("开始查询场景详情, id: {}", id);

        try {
            // 查询场景基本信息
            AgentStorySceneEntity scene = sceneMapper.findById(id)
                    .orElseThrow(() -> new EntityNotFoundException("场景不存在: " + id));

            // 填充智能体关系信息
            List<SceneAgentRelationEntity> agentRelations = sceneAgentRelationMapper.findBySceneId(id);
            scene.setAgentRelations(agentRelations);

            log.info("查询场景详情成功, id: {}, 智能体关系数量: {}", id,
                    agentRelations != null ? agentRelations.size() : 0);
            return scene;

        } catch (Exception e) {
            log.error("查询场景详情失败, id: {}, 错误: {}", id, e.getMessage(), e);
            throw new RuntimeException("查询场景详情失败: " + e.getMessage(), e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void modifyShelfStatus(Long storyId, Integer status, String shelfReason, String username, LocalDateTime updateTime) {
        this.baseMapper.modifyShelfStatus(storyId, status, shelfReason, username, updateTime);
        AgentStoryEntity story = this.baseMapper.selectById(storyId);
        if(!story.getCreator().equals( username) && status == 2){
            notifyProxyService.insertSystemMessage(new SystemNotifySubmitDTO("秘境消息", "你创建的" + story.getName() + "被下架！" + shelfReason,
                    NotifyConstant.LEVEL_NORMAL, story.getId(), story.getCreator()));
        }else if(!story.getCreator().equals( username) && status == 1){
            this.baseMapper.updateStatus(storyId,StoryConstant.STORY_STATUS_PUBLISHED);
            notifyProxyService.insertSystemMessage(new SystemNotifySubmitDTO("秘境消息", "你创建的" + story.getName() + "被上架！" + shelfReason,
                    NotifyConstant.LEVEL_NORMAL, story.getId(), story.getCreator()));
        }
    }

    @Override
    public void modifyShelfStatusOffByAgentId(Long agentId,  String shelfReason, String username) {
        List<SceneAgentRelationEntity> relationEntities = sceneAgentRelationMapper.findByAgentIdDistinctByStoryId(agentId);
        if(relationEntities.isEmpty()){
            return;
        }
        List<Long> storyIds = relationEntities.stream().map(SceneAgentRelationEntity::getStoryId).toList();
        List<AgentStoryEntity> stories = this.baseMapper.selectBatchIdsByShelfStatus(storyIds);
        storyIds = stories.stream().map(AgentStoryEntity::getId).toList();
        this.baseMapper.modifyShelfStatusByIds(storyIds, 2, shelfReason, username, LocalDateTime.now());
        for(AgentStoryEntity entity : stories) {
            notifyProxyService.insertSystemMessage(new SystemNotifySubmitDTO("秘境消息",
                    "你创建的" + entity.getName() + "被下架！因为里面的角色状态发生了变化: " + shelfReason,
                    NotifyConstant.LEVEL_NORMAL, entity.getId(), entity.getCreator()));
        }
    }

    @Override
    public List<AgentStoryEntity> findAllStoryBase() {
        return this.baseMapper.findAll();
    }

    @Override
    public List<AgentStoryEntity> findAllByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        return this.baseMapper.selectBatchIds(ids);
    }

    @Override
    public Integer cntByCreator(String username) {
        return this.baseMapper.countByCreator(username);
    }

    @Override
    public void recLastUseStorySceneUse(Long storyId, Long sceneId, String username) {
        var entity = storyCurrentUseSceneMapper.findFirstByStoryIdAndUsername(storyId, username);
        if (entity == null) {
            entity = new StoryCurrentUseSceneEntity();
            entity.setStoryId(storyId);
            entity.setSceneId(sceneId);
            entity.setUsername(username);
            entity.setUpdateTime(LocalDateTime.now());
            this.storyCurrentUseSceneMapper.insert(entity);
        } else {
            entity.setSceneId(sceneId);
            entity.setUpdateTime(LocalDateTime.now());
            this.storyCurrentUseSceneMapper.updateById(entity);
        }
    }

    @Override
    public StoryCurrentUseSceneEntity findFirstByStoryIdAndUsername(Long storyId, String username) {
        return storyCurrentUseSceneMapper.findFirstByStoryIdAndUsername(storyId, username);
    }

    @Override
    public List<AgentStoryEntity> findByIdsAndUsername(List<Long> ids, String username) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }

        // 获取剧情基本信息
        List<AgentStoryEntity> stories = this.baseMapper.selectBatchIds(ids);
        if (CollectionUtils.isEmpty(stories)) {
            return Collections.emptyList();
        }

        // 填充用户相关信息
        fillStoriesWithUserInfo(stories, username);

        return stories;
    }

    /**
     * 填充剧情的用户相关信息（收藏状态、点赞状态等）
     *
     * @param stories  剧情列表
     * @param username 用户名
     */
    private void fillStoriesWithUserInfo(List<AgentStoryEntity> stories, String username) {
        if (CollectionUtils.isEmpty(stories) || !StringUtils.hasText(username)) {
            return;
        }

        try {
            List<Long> storyIds = stories.stream()
                    .map(AgentStoryEntity::getId)
                    .collect(Collectors.toList());

            // 批量获取收藏状态
            Map<Long, Integer> favoriteStatusMap = getBatchFavoriteStatus(storyIds, username);

            // 批量获取点赞状态
            Map<Long, Integer> likeStatusMap = likeService.batchIsLiked(storyIds, username);

            // 批量获取统计信息
            Map<Long, AgentStoryStatisticsEntity> statisticsMap = getBatchStatistics(storyIds);

            // 填充信息到每个剧情
            for (AgentStoryEntity story : stories) {
                if (story != null && story.getId() != null) {
                    // 设置收藏状态
                    story.setIsFavorite(favoriteStatusMap.getOrDefault(story.getId(), 0));

                    // 设置点赞状态
                    story.setIsLiked(likeStatusMap.getOrDefault(story.getId(), 0));

                    // 设置统计信息
                    AgentStoryStatisticsEntity statistics = statisticsMap.get(story.getId());
                    if (statistics != null) {
                        story.setLikeCount(statistics.getLikeCount());
                        story.setFavoriteCount(statistics.getFavoriteCount());
                        story.setCommentCount(statistics.getCommentCount());
                    } else {
                        story.setLikeCount(0);
                        story.setFavoriteCount(0);
                        story.setCommentCount(0);
                    }
                }
            }

            log.debug("成功填充{}个剧情的用户相关信息，用户: {}", stories.size(), username);
        } catch (Exception e) {
            log.error("填充剧情用户相关信息时出错，用户: {}, 错误: {}", username, e.getMessage(), e);
            // 不抛出异常，避免影响主要的查询流程
        }
    }

    /**
     * 批量获取收藏状态
     *
     * @param storyIds 剧情ID列表
     * @param username 用户名
     * @return 剧情ID到收藏状态的映射
     */
    private Map<Long, Integer> getBatchFavoriteStatus(List<Long> storyIds, String username) {
        if (CollectionUtils.isEmpty(storyIds) || !StringUtils.hasText(username)) {
            return storyIds.stream().collect(Collectors.toMap(id -> id, id -> 0));
        }

        try {
            // 获取用户收藏的所有剧情ID
            List<Long> favoriteStoryIds = favoriteService.findUserFavoriteStoryIds(username);

            // 创建结果Map，默认所有剧情都未收藏
            Map<Long, Integer> result = storyIds.stream()
                    .collect(Collectors.toMap(storyId -> storyId, storyId -> 0));

            // 更新已收藏的剧情状态
            for (Long favoriteStoryId : favoriteStoryIds) {
                if (result.containsKey(favoriteStoryId)) {
                    result.put(favoriteStoryId, 1);
                }
            }

            return result;
        } catch (Exception e) {
            log.error("批量获取收藏状态失败，用户: {}, 错误: {}", username, e.getMessage(), e);
            // 降级处理：返回默认值
            return storyIds.stream().collect(Collectors.toMap(id -> id, id -> 0));
        }
    }

    /**
     * 批量获取统计信息
     *
     * @param storyIds 剧情ID列表
     * @return 剧情ID到统计信息的映射
     */
    private Map<Long, AgentStoryStatisticsEntity> getBatchStatistics(List<Long> storyIds) {
        if (CollectionUtils.isEmpty(storyIds)) {
            return Collections.emptyMap();
        }

        try {
            // 获取所有统计信息
            Map<Long, AgentStoryStatisticsEntity> allStatistics = statisticsService.findAllGroupByStoryId();

            // 过滤出需要的统计信息
            return storyIds.stream()
                    .collect(Collectors.toMap(
                            storyId -> storyId,
                            allStatistics::get,
                            (v1, v2) -> v1,
                            HashMap::new
                    ));
        } catch (Exception e) {
            log.error("批量获取统计信息失败，错误: {}", e.getMessage(), e);
            return Collections.emptyMap();
        }
    }

    /**
     * 异步执行剧情安全检查
     * 参考AgentServiceImpl的安全检查实现
     * 使用配置的taskExecutor线程池
     *
     * @param story 剧情实体
     */
    @Async("taskExecutor")
    public void performStorySecurityCheck(AgentStoryEntity story) {
        try {
            log.info("开始执行剧情安全检查, id: {}, name: {}", story.getId(), story.getName());

            // 检查剧情标题
            if (story.getName() != null) {
                securityCheckService.checkTextWithTencentCOS("秘境名称", story.getName());
            }

            // 检查剧情简介
            if (story.getDescription() != null) {
                securityCheckService.checkTextWithTencentCOS("秘境全局设定", story.getDescription());
            }

            // 检查我的身份
            if (story.getMyName() != null) {
                securityCheckService.checkTextWithTencentCOS("用户角色名", story.getMyName());
            }
            if (story.getMyIdentity() != null) {
                securityCheckService.checkTextWithTencentCOS("用户角色设定", story.getMyIdentity());
            }

            // 检查剧情封面图片
            if (story.getBgUrl() != null) {
                securityCheckService.checkImageWithTencentCOS("秘境封面", story.getBgUrl(), story.getId());
            }

            // 检查场景内容
            if (story.getScenes() != null && !story.getScenes().isEmpty()) {
                checkScenesContent(story);
            }

            // 所有检查通过，更新状态为已发布
            log.info("剧情安全检查全部通过, id: {}", story.getId());
            updateStoryStatus(story.getId(), StoryConstant.STORY_STATUS_PUBLISHED);

        }catch (SecurityCheckException e) {
            log.error("剧情安全检查失败, id: {},type{} 错误: {}", story.getId(),e.getCheckType(),e.getMessage(), e);
            String userFriendlyMessage = e.getUserFriendlyMessage();
            updateStoryStatus(story.getId(), StoryConstant.STORY_CHECK_STATUS_FAIL);

            this.baseMapper.updateSecurityCheckResultById(story.getId(),userFriendlyMessage);
        }
        catch (Exception e) {
            log.error("剧情安全检查失败, id: {}, 错误: {}", story.getId(), e.getMessage(), e);
            updateStoryStatus(story.getId(), StoryConstant.STORY_CHECK_STATUS_FAIL);
            this.baseMapper.updateSecurityCheckResultById(story.getId(),e.getMessage());

        }
    }

    /**
     * 检查场景内容安全性
     *
     * @param story 剧情实体
     * @throws Exception 检查异常
     */
    private void checkScenesContent(AgentStoryEntity story) throws Exception {
        for (AgentStorySceneEntity scene : story.getScenes()) {
            String scenePrefix = "章节[" + scene.getSceneName() + "]";

            // 检查场景名称
            if (scene.getSceneName() != null) {
                securityCheckService.checkTextWithTencentCOS(scenePrefix + "名称", scene.getSceneName());
            }

            // 检查场景简介
            if (scene.getSceneDescription() != null) {
                securityCheckService.checkTextWithTencentCOS(scenePrefix + "情节设定", scene.getSceneDescription());
            }

            // 检查场景开场白
            if (scene.getOpeningText() != null) {
                securityCheckService.checkTextWithTencentCOS(scenePrefix + "开场白", scene.getOpeningText());
            }

            // 检查场景脚本内容
            if (scene.getSceneScript() != null) {
                securityCheckService.checkTextWithTencentCOS(scenePrefix + "脚本", scene.getSceneScript());
            }

            // 检查场景背景图片
            if (scene.getBgUrl() != null) {
                securityCheckService.checkImageWithTencentCOS(scenePrefix + "封面", scene.getBgUrl(), story.getId());
            }
        }
    }

    /**
     * 更新剧情状态
     *
     * @param storyId 剧情ID
     * @param status  状态
     */
    private void updateStoryStatus(Long storyId, Integer status) {
        try {
            this.baseMapper.updateStatus(storyId, status);
            this.baseMapper.updateSecurityCheckResultById(storyId,"");
            log.info("更新剧情状态成功, id: {}, status: {}", storyId, status);
        } catch (Exception e) {
            log.error("更新剧情状态失败, id: {}, status: {}, 错误: {}", storyId, status, e.getMessage(), e);
        }
    }

    /**
     * 填充剧情列表的附加信息
     * 批量查询相关数据，避免循环查询数据库
     *
     * @param stories 剧情列表
     */
    private void fillStoriesWithAdditionalInfo(List<AgentStoryEntity> stories) {
        if (CollectionUtils.isEmpty(stories)) {
            return;
        }

        try {
            // 收集所有剧情ID和类型ID
            List<Long> storyIds = stories.stream()
                    .filter(story -> story != null && story.getId() != null)
                    .map(AgentStoryEntity::getId)
                    .toList();

            List<Long> typeIds = stories.stream()
                    .filter(story -> story != null && story.getStoryTypeId() != null)
                    .map(AgentStoryEntity::getStoryTypeId)
                    .distinct()
                    .collect(Collectors.toList());

            // 批量查询剧情类型
            Map<Long, AgentStoryTypeEntity> typeMap = Map.of();
            if (!CollectionUtils.isEmpty(typeIds)) {
                List<AgentStoryTypeEntity> storyTypes = storyTypeMapper.selectBatchIds(typeIds);
                typeMap = storyTypes.stream()
                        .collect(Collectors.toMap(AgentStoryTypeEntity::getId, type -> type));
            }

            // 批量查询场景信息
            Map<Long, List<AgentStorySceneEntity>> sceneMap = Map.of();
            Map<Long, List<SceneAgentRelationEntity>> relationsMap = Map.of();
            final Map<Long, AgentStoryStatisticsEntity> statisticsEntityMap = statisticsService.findAllGroupByStoryId();
            Map<Long, Integer> sceneCountMap = Map.of();
            if (!CollectionUtils.isEmpty(storyIds)) {
                // 批量查询所有场景
                List<AgentStorySceneEntity> allScenes = sceneMapper.findByStoryIds(storyIds);
                List<Long> sceneIds = allScenes.stream()
                        .map(AgentStorySceneEntity::getId)
                        .toList();
                List<SceneAgentRelationEntity> relations = sceneAgentRelationMapper.findBySceneIds(sceneIds);
                // 按剧情ID分组场景
                sceneMap = allScenes.stream()
                        .collect(Collectors.groupingBy(AgentStorySceneEntity::getStoryId));
                relationsMap = relations.stream()
                        .collect(Collectors.groupingBy(SceneAgentRelationEntity::getSceneId));
                // 统计每个剧情的场景数量
                sceneCountMap = sceneMap.entrySet().stream()
                        .collect(Collectors.toMap(
                                Map.Entry::getKey,
                                entry -> entry.getValue().size()
                        ));
            }

            // 为每个剧情填充信息
            for (AgentStoryEntity story : stories) {
                if (story != null && story.getId() != null) {
                    // 填充剧情类型
                    if (story.getStoryTypeId() != null) {
                        AgentStoryTypeEntity storyType = typeMap.get(story.getStoryTypeId());
                        story.setStoryType(storyType);
                    }

                    // 填充场景列表
                    List<AgentStorySceneEntity> scenes = sceneMap.get(story.getId());

                    if (scenes != null) {
                        for (AgentStorySceneEntity scene : scenes) {
                            scene.setAgentRelations(relationsMap.get(scene.getId()));
                        }
                        scenes.sort(Comparator.comparingInt(AgentStorySceneEntity::getSceneOrder));

                        story.setScenes(scenes);

                        story.setSceneCount(scenes.size());
                    } else {
                        story.setScenes(new ArrayList<>());
                        story.setSceneCount(0);
                    }

                    // 如果场景数量映射中有数据，使用映射中的数量（防止重复计算）
                    Integer sceneCount = sceneCountMap.get(story.getId());
                    if (sceneCount != null) {
                        story.setSceneCount(sceneCount);
                    }
                    AgentStoryStatisticsEntity statisticsEntity = statisticsEntityMap.get(story.getId());
                    if (statisticsEntity != null) {
                        story.setLikeCount(statisticsEntity.getLikeCount());
                        story.setCommentCount(statisticsEntity.getCommentCount());
                        story.setFavoriteCount(statisticsEntity.getFavoriteCount());
                    }
                }
            }

            log.debug("成功填充{}个剧情的附加信息，包含{}个剧情类型和{}个场景",
                    stories.size(), typeMap.size(), sceneMap.values().stream().mapToInt(List::size).sum());
        } catch (Exception e) {
            log.error("填充剧情附加信息时出错: {}", e.getMessage(), e);
            // 不抛出异常，避免影响主要的查询流程
        }
    }

    /**
     * 分页查询用户收藏的智能体剧情
     *
     * @param pageNum  页码
     * @param pageSize 每页大小
     * @param query    查询条件
     * @return 分页结果
     */
    @Override
    public PageInfo<AgentStoryEntity> pageFavoriteByUsername(int pageNum, int pageSize, MyFavoriteStoryQueryDTO query) {
        PageHelper.startPage(pageNum, pageSize);
        List<AgentStoryEntity> list = this.baseMapper.pageFavoriteByUsername(query);

        if (!CollectionUtils.isEmpty(list)) {
            // 填充剧情的附加信息
            fillStoriesWithAdditionalInfo(list);

            // 填充用户相关信息（收藏状态、点赞状态等）
            fillStoriesWithUserInfo(list, query.getUsername());
        }

        return new PageInfo<>(list);
    }

    /**
     * 分页查询用户点赞的智能体剧情
     *
     * @param pageNum  页码
     * @param pageSize 每页大小
     * @param query    查询条件
     * @return 分页结果
     */
    @Override
    public PageInfo<AgentStoryEntity> pageLikedByUsername(int pageNum, int pageSize, MyLikeStoryQueryDTO query) {
        PageHelper.startPage(pageNum, pageSize);
        List<AgentStoryEntity> list = this.baseMapper.pageLikedByUsername(query);

        if (!CollectionUtils.isEmpty(list)) {
            // 填充剧情的附加信息
            fillStoriesWithAdditionalInfo(list);

            // 填充用户相关信息（收藏状态、点赞状态等）
            fillStoriesWithUserInfo(list, query.getUsername());
        }

        return new PageInfo<>(list);
    }

    /**
     * 分页查询用户评论的智能体剧情
     *
     * @param pageNum  页码
     * @param pageSize 每页大小
     * @param query    查询条件
     * @return 分页结果
     */
    @Override
    public PageInfo<AgentStoryEntity> pageCommentedByUsername(int pageNum, int pageSize, MyCommentStoryQueryDTO query) {
        PageHelper.startPage(pageNum, pageSize);
        List<AgentStoryEntity> list = this.baseMapper.pageCommentedByUsername(query);

        if (!CollectionUtils.isEmpty(list)) {
            // 填充剧情的附加信息
            fillStoriesWithAdditionalInfo(list);

            // 填充用户相关信息（收藏状态、点赞状态等）
            fillStoriesWithUserInfo(list, query.getUsername());
        }

        return new PageInfo<>(list);
    }

    @Override
    public List<Long> findStoryIdsByNameLike(String storyName) {
        if (storyName == null || storyName.trim().isEmpty()) {
            return Collections.emptyList();
        }
        return this.baseMapper.findStoryIdsByNameLike(storyName.trim());
    }
}
