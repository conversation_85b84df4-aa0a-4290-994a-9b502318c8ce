package com.gw.agent.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gw.agent.entity.AgentStoryStatisticsEntity;
import com.gw.agent.mapper.sql.AgentStoryStatisticsMapper;
import com.gw.agent.service.AgentStoryStatisticsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Log4j2
public class AgentStoryStatisticsServiceImpl extends ServiceImpl<AgentStoryStatisticsMapper, AgentStoryStatisticsEntity> implements AgentStoryStatisticsService {
    @Override
    public void insert(AgentStoryStatisticsEntity entity) {
        this.baseMapper.insert(entity);
    }

    @Override
    public void updateFavoriteCount(Long storyId, Integer increment) {
        AgentStoryStatisticsEntity entity = this.baseMapper.findFirstByStoryId(storyId);
        if (entity != null) {
            entity.setFavoriteCount(entity.getFavoriteCount() + increment);
            if (entity.getFavoriteCount() < 0) {
                entity.setFavoriteCount(0);
            }
            this.baseMapper.updateFavoriteCount(entity.getId(), entity.getFavoriteCount());
        } else {
            entity = new AgentStoryStatisticsEntity(storyId);
            if (increment > 0) {
                entity.setFavoriteCount(increment);
            }
            this.baseMapper.insert(entity);
        }
    }

    @Override
    public void updateLikeCount(Long storyId, Integer increment) {
        AgentStoryStatisticsEntity entity = this.baseMapper.findFirstByStoryId(storyId);
        if (entity != null) {
            entity.setLikeCount(entity.getLikeCount() + increment);
            if (entity.getLikeCount() < 0) {
                entity.setLikeCount(0);
            }
            this.baseMapper.updateLikeCount(entity.getId(), entity.getLikeCount());
        } else {
            entity = new AgentStoryStatisticsEntity(storyId);
            if (increment > 0) {
                entity.setLikeCount(increment);
            }
            this.baseMapper.insert(entity);
        }
    }

    @Override
    public void updateCommentCount(Long storyId, Integer increment) {
        AgentStoryStatisticsEntity entity = this.baseMapper.findFirstByStoryId(storyId);
        if (entity != null) {
            entity.setCommentCount(entity.getCommentCount() + increment);
            if (entity.getCommentCount() < 0) {
                entity.setCommentCount(0);
            }
            this.baseMapper.updateCommentCount(entity.getId(), entity.getCommentCount());
        } else {
            entity = new AgentStoryStatisticsEntity(storyId);
            if (increment > 0) {
                entity.setCommentCount(increment);
            }
            this.baseMapper.insert(entity);
        }
    }

    @Override
    public AgentStoryStatisticsEntity findByStoryId(Long id) {
        return this.baseMapper.findFirstByStoryId(id);
    }

    @Override
    public Map<Long, AgentStoryStatisticsEntity> findAllGroupByStoryId() {
        return this.baseMapper.findAll().stream().collect(Collectors.toMap(
            AgentStoryStatisticsEntity::getStoryId,
            Function.identity(),
            // Merge function: when duplicate keys exist, combine the statistics
            (existing, replacement) -> {
                log.warn("Found duplicate storyId: {}, combining statistics. Existing: {}, Replacement: {}",
                    existing.getStoryId(), existing, replacement);

                // Create a new entity with combined statistics
                AgentStoryStatisticsEntity merged = new AgentStoryStatisticsEntity();
                merged.setId(Math.max(existing.getId(), replacement.getId())); // Use the higher ID
                merged.setStoryId(existing.getStoryId());
                merged.setFavoriteCount(existing.getFavoriteCount() + replacement.getFavoriteCount());
                merged.setCommentCount(existing.getCommentCount() + replacement.getCommentCount());
                merged.setLikeCount(existing.getLikeCount() + replacement.getLikeCount());

                return merged;
            }
        ));
    }

    @Override
    public int cleanupDuplicateRecords() {
        log.info("Starting cleanup of duplicate AgentStoryStatistics records");

        List<AgentStoryStatisticsEntity> allRecords = this.baseMapper.findAll();
        Map<Long, List<AgentStoryStatisticsEntity>> groupedByStoryId = allRecords.stream()
            .collect(Collectors.groupingBy(AgentStoryStatisticsEntity::getStoryId));

        int cleanedUpCount = 0;

        for (Map.Entry<Long, List<AgentStoryStatisticsEntity>> entry : groupedByStoryId.entrySet()) {
            List<AgentStoryStatisticsEntity> duplicates = entry.getValue();
            if (duplicates.size() > 1) {
                log.info("Found {} duplicate records for storyId: {}", duplicates.size(), entry.getKey());

                // Sort by ID descending to keep the latest record
                duplicates.sort((a, b) -> Long.compare(b.getId(), a.getId()));

                // Combine all statistics into the latest record
                AgentStoryStatisticsEntity latest = duplicates.get(0);
                int totalFavorites = duplicates.stream().mapToInt(AgentStoryStatisticsEntity::getFavoriteCount).sum();
                int totalComments = duplicates.stream().mapToInt(AgentStoryStatisticsEntity::getCommentCount).sum();
                int totalLikes = duplicates.stream().mapToInt(AgentStoryStatisticsEntity::getLikeCount).sum();

                // Update the latest record with combined statistics
                this.baseMapper.updateFavoriteCount(latest.getId(), totalFavorites);
                this.baseMapper.updateLikeCount(latest.getId(), totalLikes);
                this.baseMapper.updateCommentCount(latest.getId(), totalComments);

                // Delete the older duplicate records
                for (int i = 1; i < duplicates.size(); i++) {
                    this.baseMapper.deleteById(duplicates.get(i).getId());
                    cleanedUpCount++;
                    log.info("Deleted duplicate record with id: {} for storyId: {}",
                        duplicates.get(i).getId(), entry.getKey());
                }

                log.info("Consolidated statistics for storyId: {} - Favorites: {}, Comments: {}, Likes: {}",
                    entry.getKey(), totalFavorites, totalComments, totalLikes);
            }
        }

        log.info("Cleanup completed. Removed {} duplicate records", cleanedUpCount);
        return cleanedUpCount;
    }

}
