package com.gw.agent.controller;

import com.gw.agent.dto.AgentStoryInterestDTO;
import com.gw.agent.service.AgentStoryInterestService;
import com.gw.common.dto.ItemIdDTO;
import com.gw.common.dto.ResponseResult;
import com.gw.common.user.context.UserContextUtil;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * 智能体剧情兴趣控制器
 */
@RestController
@RequestMapping("/api/v1/agent/story/interest")
@RequiredArgsConstructor
@Log4j2
@Tag(name = "智能体剧情兴趣管理", description = "智能体剧情兴趣相关API")
public class AgentStoryInterestController {

    private final AgentStoryInterestService agentStoryInterestService;

    /**
     * 标记对智能体剧情的兴趣
     */
    @Operation(summary = "标记对智能体剧情的感兴趣", description = "标记当前用户对指定智能体剧情的兴趣")
    @PostMapping("/mark")
    public ResponseResult<?> markInterest(@RequestBody @Valid AgentStoryInterestDTO req) {
        String username = UserContextUtil.getCurrentUsername();
        boolean result = agentStoryInterestService.markInterest(req.getStoryId(), username, req.getInterestLevel(), req.getSource());
        return result ?
                ResponseResult.success(null) :
                ResponseResult.failure("标记兴趣失败");
    }

    /**
     * 取消对智能体剧情的兴趣标记
     */
    @Operation(summary = "对智能体剧情不感兴趣", description = "对智能体剧情不感兴趣")
    @PostMapping("/unmark")
    public ResponseResult<?> unmarkInterest(@RequestBody @Valid ItemIdDTO req) {
        String username = UserContextUtil.getCurrentUsername();
        boolean result = agentStoryInterestService.unmarkInterest(req.getId(), username);
        return result ?
                ResponseResult.success(null) :
                ResponseResult.failure("取消兴趣标记失败");
    }

    /**
     * 获取用户对智能体剧情的兴趣级别
     */
    @Operation(summary = "获取兴趣级别", description = "获取当前用户对指定智能体剧情的兴趣级别")
    @PostMapping("/level")
    @Hidden
    public ResponseResult<Map<String, Object>> getInterestLevel(@RequestBody @Valid ItemIdDTO req) {
        String username = UserContextUtil.getCurrentUsername();
        int level = agentStoryInterestService.getInterestLevel(req.getId(), username);

        Map<String, Object> result = new HashMap<>();
        result.put("storyId", req.getId());
        result.put("interestLevel", level);

        return ResponseResult.success(result);
    }
}