package com.gw.agent.controller;

import com.github.pagehelper.PageInfo;
import com.gw.agent.config.CacheProperties;
import com.gw.agent.constant.AgentFileConstants;
import com.gw.agent.dto.*;
import com.gw.agent.entity.AgentEntity;
import com.gw.agent.entity.AgentTagEntity;
import com.gw.agent.entity.AgentTypeEntity;
import com.gw.agent.mapper.ModelMapperConvert;
import com.gw.agent.service.*;
import com.gw.agent.vo.AgentDetailVO;
import com.gw.agent.vo.DownloadFileVO;
import com.gw.agent.vo.VoiceTimbreVo;
import com.gw.common.agent.constant.AgentCommonCacheConstant;
import com.gw.common.agent.constant.AgentConstant;
import com.gw.common.agent.constant.AgentStatus;
import com.gw.common.agent.dto.AgentQueryByIdsDTO;
import com.gw.common.agent.dto.UserInteractionAgentQuery;
import com.gw.common.agent.service.AgentProxyService;
import com.gw.common.agent.vo.AgentBaseVO;
import com.gw.common.agent.vo.UserInteractionAgentVO;
import com.gw.common.dto.*;
import com.gw.common.entity.BaseEntity;
import com.gw.common.exception.BusinessException;
import com.gw.common.membership.constant.MemberBenefitConstant;
import com.gw.common.membership.service.MembershipProxyService;
import com.gw.common.user.constant.UserCommonCacheConstant;
import com.gw.common.user.context.UserContextUtil;
import com.gw.common.user.service.UserProxyService;
import com.gw.common.user.vo.UserBaseContentVo;
import com.gw.common.util.DownloadUtil;
import com.gw.common.util.ImageUtils;
import com.gw.common.util.UploadFileUtil;
import com.gw.common.vo.PageBaseContentVo;
import com.gw.common.vo.PaginationVo;
import com.gw.common.vo.UploadFileVo;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.modelmapper.ModelMapper;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ForkJoinPool;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

import static com.gw.common.exception.BusinessExceptionCode.BENEFIT_NOT_ALLOWED_CODE;
import static com.gw.common.exception.BusinessExceptionCode.FAIL_CODE;

/**
 * 智能体控制器
 */
@RestController
@RequestMapping("/api/v1/agent")
@RequiredArgsConstructor
@Tag(name = "智能体管理", description = "智能体相关API")
@Log4j2
public class AgentController {
    // 定义缓存更新的最小间隔时间（10秒）
    private static final long CACHE_UPDATE_INTERVAL = 10 * 1000;
    private final AgentUsageRecordService agentUsageRecordService;
    private final AgentService agentService;
    private final AgentRecommendationService recommendationService;
    private final AgentRemoteService agentRemoteService;
    private final AgentTagService agentTagService;
    private final AgentFavoriteService agentFavoriteService;
    private final AgentTypeService agentTypeService;
    private final AgentProxyService agentProxyService;
    private final CacheProperties cacheProperties;
    private final AgentLikeService agentLikeService;
    private final AgentCommentService agentCommentService;
    private final AgentSearchRankService agentSearchRankService;

    // 添加成员变量，用于跟踪最后一次更新缓存的时间
    private final AtomicLong lastCacheUpdateTime = new AtomicLong(0);
    // 正在执行标志
    private final AtomicBoolean isUpdating = new AtomicBoolean(false);
    private final MembershipProxyService membershipProxyService;
    private final UserProxyService userProxyService;
    @Qualifier("taskExecutor")
    private final ThreadPoolTaskExecutor taskExecutor;
    private final AgentDraftService draftService;
    private final AgentStoryService agentStoryService;
    /**
     * 获取所有平台信息
     */
    @Operation(summary = "获取所有平台", description = "获取所有支持的平台信息")
    @GetMapping("/platforms")
    public ResponseResult<List<PlatformDTO>> getAllPlatforms() {
        List<PlatformDTO> platforms = new ArrayList<>();

        //todo: 从常量中读取平台信息（未来可改为从配置文件读取）
        platforms.add(new PlatformDTO(AgentConstant.COZE_PLATFORM, "扣子平台"));
        platforms.add(new PlatformDTO(AgentConstant.HUO_SHAN_PLATFORM, "火山平台"));

        return ResponseResult.success(platforms);
    }

    /**
     * 填充智能体信息
     */
    private void fillAgent(AgentSubmitDTO req, AgentEntity entity) {
        if (req == null || entity == null) {
            throw new IllegalArgumentException("Request or entity cannot be null");
        }
        req.setName(req.getName().trim());
        String username = UserContextUtil.getCurrentUsername();
        ModelMapper modelMapper = ModelMapperConvert.getAgentSubmitModelMapper();

        // 基本字段映射
        modelMapper.map(req, entity);

        // 设置类型
        setAgentType(req, entity);

        // 设置标签
        setAgentTags(req, entity);
        if (req.getBgUrl() == null || req.getBgUrl().isEmpty()) {
            entity.setBgUrl(req.getAvatarUrl());
        }


        // 设置更新者和更新时间
        entity.setUpdater(username);
        entity.setUpdateTime(LocalDateTime.now());
    }

    /**
     * 设置智能体类型
     */
    private void setAgentType(AgentSubmitDTO req, AgentEntity entity) {
        if (req.getTypeId() != null) {
            AgentTypeEntity type = agentTypeService.findById(req.getTypeId());
            entity.setType(type);
            entity.setShelfStatus(AgentConstant.SHELF_ON_STATUS);
            entity.setTypeId(req.getTypeId());
        } else {
            entity.setTypeId((long) AgentConstant.AGENT_TYPE_CHAT);
        }
    }

    /**
     * 设置智能体标签
     */
    private void setAgentTags(AgentSubmitDTO req, AgentEntity entity) {
        Map<Long, AgentTagEntity> tagMap = agentTagService.findTagsMap();
        entity.setTags(new ArrayList<>());

        if (req.getTagIds() != null) {
            req.getTagIds().stream()
                    .filter(Objects::nonNull)
                    .forEach(tagId -> {
                        AgentTagEntity tag = tagMap.get(tagId);
                        if (tag != null) {
                            entity.getTags().add(tag);
                        }
                    });
        }
    }

    /**
     * 处理背景图像
     */
    private void processBackgroundImage(AgentEntity entity) {
        if (entity.getBgUrl() != null) {
            try {
                // 添加时间戳到文件名
                String timestamp = String.valueOf(System.currentTimeMillis());
                String filename = AgentFileConstants.UPLOAD_PATH + entity.getId() + "_" + timestamp + ".jpg";
                ImageUtils.compressImage(entity.getBgUrl(), 1.0f, filename);
                entity.setBgUrl(filename);
            } catch (Exception e) {
                log.error("压缩背景图失败", e);
            }
        }
    }

    /**
     * 基础的实体转VO映射
     */
    private AgentDetailVO mapEntityToVo(AgentEntity entity) {
        if (entity == null) {
            return null;
        }
        ModelMapper modelMapper = ModelMapperConvert.getAgentModelMapper();
        return modelMapper.map(entity, AgentDetailVO.class);
    }

    /**
     * 完整的实体转VO映射，包含额外信息
     */
    private AgentDetailVO convertToVO(AgentEntity entity) {
        if (entity == null) {
            return null;
        }
        AgentDetailVO vo = mapEntityToVo(entity);
        vo.setUserCount(agentUsageRecordService.getUserCount(entity.getId()));
        return vo;
    }

    /**
     * 完整的实体转VO映射，包含额外信息和收藏状态
     */
    private AgentDetailVO convertToVOWithFavoriteAndLike(AgentEntity entity, String username) {
        if (entity == null) {
            return null;
        }
        AgentDetailVO vo = convertToVO(entity);
        if (username != null) {
            vo.setIsFavorite(agentFavoriteService.isFavorite(entity.getId(), username));
            vo.setIsLike(agentLikeService.isLiked(entity.getId(), username));
        }
        return vo;
    }

    /**
     * 验证用户权限
     */
    private void validateUserPermission(AgentEntity entity, String username) {
        if (!entity.getCreator().equals(username)) {
            throw new BusinessException(FAIL_CODE.getCode(), "无权限操作");
        }
    }

    /**
     * 验证权限并获取实体
     */
    private AgentEntity validateAndGetEntity(Long id, String username) {
        AgentEntity entity = agentService.findById(id);
        validateUserPermission(entity, username);
        return entity;
    }

    /**
     * 准备实体进行更新
     */
    private AgentEntity prepareEntityForUpdate(Long id, String username, boolean validatePermission) {
        AgentEntity entity = agentService.findById(id);
        if (validatePermission) {
            validateUserPermission(entity, username);
        }
        entity.setUpdater(username);
        entity.setUpdateTime(LocalDateTime.now());
        return entity;
    }

    @Operation(summary = "更新推荐指数", description = "更新推荐指数")
    @PostMapping("admin/recommend-idx/update")
    public ResponseResult<?> updateRecommendIdx(@RequestBody @Valid AgentUpdateRecommendIdxDTO req) {

        AgentEntity entity = agentService.findById(req.getId());

        agentService.updateRecommendIdxById(req.getId(), req.getRecommendIdx());
        return ResponseResult.success(null);
    }

    @Operation(summary = "后台创建智能体", description = "创建一个新的智能体")
    @PostMapping("admin")
    public ResponseResult<?> adminCreateAgent(@RequestBody @Valid AgentSubmitDTO req) {
        String username = UserContextUtil.getCurrentUsername();
        req.setPlatform(2);
        return handleAgentCreation(req, username, true, null);
    }

    /**
     * 创建智能体
     */
    @Operation(summary = "创建智能体", description = "创建一个新的智能体")
    @PostMapping("")
    public ResponseResult<?> createAgent(@RequestBody @Valid AgentSubmitDTO req) {
        String username = UserContextUtil.getCurrentUsername();

        // 检查会员权限
        var allow = membershipProxyService.checkBenefitCanUse(username, MemberBenefitConstant.CREAT_AGENT_CODE);
        if (!allow) {
            return ResponseResult.failure(BENEFIT_NOT_ALLOWED_CODE, "今日免费创建角色额度已用完");
        }

        UserBaseContentVo user = userProxyService.findByUsername(
            cacheProperties.getCacheName(UserCommonCacheConstant.USER_BASE_MAP_CACHE_KEY), username);
        req.setPlatform(2);
        return handleAgentCreation(req, username, false, user);
    }

    /**
     * 处理智能体创建的通用逻辑
     */
    private ResponseResult<?> handleAgentCreation(AgentSubmitDTO req, String username, boolean isAdmin, UserBaseContentVo user) {
        AgentEntity entity = new AgentEntity();
        fillAgent(req, entity);
        entity.setCreator(username);
        entity.setRecommendIdx(isAdmin ? 1 : 0);

        if (user != null) {
            entity.setWxOpenId(user.getWxOpenId());
        }

        agentService.insert(entity);
        processBackgroundImage(entity);
        agentService.updateBgUrl(entity.getId(), entity.getBgUrl());

        // 记录会员权益使用（仅非管理员创建时）
        if (!isAdmin) {
            membershipProxyService.asyncRecordBenefitUsage(username, MemberBenefitConstant.CREAT_AGENT_CODE, 1);
        }

        // 删除草稿
        if (req.getDraftId() != null) {
            draftService.deleteDraft(req.getDraftId());
        }

        return ResponseResult.success(null);
    }

    /**
     * 更新智能体
     */
    @Operation(summary = "后台更新智能体", description = "更新智能体信息")
    @PostMapping("admin/update")
    public ResponseResult<?> updateAgent(@RequestBody @Valid AgentUpdateDTO req) {
        String username = UserContextUtil.getCurrentUsername();
        req.setPlatform(2);
        AgentEntity entity = prepareEntityForUpdate(req.getId(), username, false);
        return handleAgentUpdate(entity, req, username);
    }

    /**
     * 处理智能体更新的公共逻辑
     *
     * @param entity 要更新的实体
     * @param req    更新请求
     * @return 响应结果
     */
    private ResponseResult<?> handleAgentUpdate(AgentEntity entity, AgentUpdateDTO req,String username) {
        // 保存更新前的状态（原始实体）
        AgentEntity originalEntity = new AgentEntity();
        try {
            BeanUtils.copyProperties(entity, originalEntity);
        } catch (Exception e) {
            log.error("复制实体对象失败", e);
        }

        // 使用请求数据更新实体
        fillAgent(req, entity);
        log.info("bg url: {}", entity.getBgUrl());
        processBackgroundImage(entity);
        entity.setBgThumbnailUrl(null);
        entity.setShelfStatus(AgentConstant.SHELF_ON_STATUS);
        // 检查是否只有bgUrl、avatarUrl或isPublic属性发生变化，其他字段保持不变
        boolean onlySimpleFieldsChanged = isOnlySimpleFieldsChanged(originalEntity, entity);

        // 根据变更内容选择更新方法
        if (onlySimpleFieldsChanged) {
            agentService.updateNotPublic(entity);
        } else {
            agentService.update(entity);
        }
        if(originalEntity.getIsPublic() == AgentConstant.AGENT_PUBLIC && entity.getIsPublic() == AgentConstant.AGENT_PRIVATE){
            agentStoryService.modifyShelfStatusOffByAgentId(entity.getId(), "智能体" + entity.getName() +"取消公开", username);
        }
        return ResponseResult.success(null);
    }

    /**
     * 检查是否仅bgUrl、avatarUrl或isPublic字段发生变化，其他字段保持不变
     *
     * @param originalEntity 原始实体
     * @param updatedEntity  更新后的实体
     * @return 是否仅简单字段变化
     */
    private boolean isOnlySimpleFieldsChanged(AgentEntity originalEntity, AgentEntity updatedEntity) {
        // 检查三个简单字段是否有变化
        boolean bgUrlChanged = !Objects.equals(originalEntity.getBgUrl(), updatedEntity.getBgUrl());
        boolean avatarUrlChanged = !Objects.equals(originalEntity.getAvatarUrl(), updatedEntity.getAvatarUrl());
        boolean isPublicChanged = !Objects.equals(originalEntity.getIsPublic(), updatedEntity.getIsPublic());

        // 至少有一个简单字段发生变化
        boolean hasSimpleFieldsChanged = bgUrlChanged || avatarUrlChanged || isPublicChanged;

        // 没有简单字段变化，则不是"仅简单字段变化"
        if (!hasSimpleFieldsChanged) {
            return false;
        }

        // 检查其他字段是否保持不变
        if (!Objects.equals(originalEntity.getName(), updatedEntity.getName())) return false;
        if (!Objects.equals(originalEntity.getIdentity(), updatedEntity.getIdentity())) return false;
        if (!Objects.equals(originalEntity.getTypeId(), updatedEntity.getTypeId())) return false;
        if (!Objects.equals(originalEntity.getGender(), updatedEntity.getGender())) return false;
        if (!Objects.equals(originalEntity.getIntroduction(), updatedEntity.getIntroduction())) return false;
        if (!Objects.equals(originalEntity.getProfile(), updatedEntity.getProfile())) return false;

        // 比较标签列表
        List<Long> originalTagIds = originalEntity.getTags() == null ? null :
                originalEntity.getTags().stream().map(BaseEntity::getId).toList();
        List<Long> updatedTagIds = updatedEntity.getTags() == null ? null :
                updatedEntity.getTags().stream().map(BaseEntity::getId).toList();
        if (!Objects.equals(originalTagIds, updatedTagIds)) return false;

        if (updatedEntity.getBgThumbnailUrl() != null && originalEntity.getBgThumbnailUrl() != null &&
                !originalEntity.getBgThumbnailUrl().equals(updatedEntity.getBgThumbnailUrl())) {
            return true;
        }
        if (updatedEntity.getAvatarUrl() != null && originalEntity.getAvatarUrl() != null &&
                !originalEntity.getAvatarUrl().equals(updatedEntity.getAvatarUrl())) {
            return true;
        }
        return updatedEntity.getIsPublic() != null && originalEntity.getIsPublic() != null && updatedEntity.getIsPublic().equals(originalEntity.getIsPublic());
    }

    @Operation(summary = "更新智能体头像", description = "更新智能体头像")
    @PostMapping("update_avatar")
    public ResponseResult<?> updateAvatarUrl(@RequestBody @Valid AgentUpdateAvatarDTO req) {
        String username = UserContextUtil.getCurrentUsername();
        validateAndGetEntity(req.getId(), username);

        if (StringUtils.isBlank(req.getAvatarUrl())) {
            return ResponseResult.failure(FAIL_CODE.getCode(), "头像URL不能为空");
        }

        agentService.updateAvatarUrl(req.getId(), req.getAvatarUrl());
        return ResponseResult.success(null);
    }

    /**
     * 更新智能体背景
     */
    @Operation(summary = "更新智能体背景", description = "更新智能体背景")
    @PostMapping("update_bg")
    public ResponseResult<?> updateBgUrl(@RequestBody @Valid AgentUpdateBgDTO req) {
        return handleBgUrlUpdate(req, true);
    }

    /**
     * 更新智能体背景
     */
    @Operation(summary = "更新我的智能体背景", description = "更新智能体背景")
    @PostMapping("/my/update_bg")
    public ResponseResult<?> updateMyBgUrl(@RequestBody @Valid AgentUpdateBgDTO req) {
        return handleBgUrlUpdate(req, false);
    }

    /**
     * 处理背景URL更新的通用逻辑
     */
    private ResponseResult<?> handleBgUrlUpdate(AgentUpdateBgDTO req, boolean processImage) {
        String username = UserContextUtil.getCurrentUsername();
        AgentEntity entity = validateAndGetEntity(req.getId(), username);

        if (StringUtils.isBlank(req.getBgUrl())) {
            return ResponseResult.failure(FAIL_CODE.getCode(), "背景URL不能为空");
        }

        if (processImage) {
            entity.setBgUrl(req.getBgUrl());
            processBackgroundImage(entity);
        }

        agentService.updateBgUrl(entity.getId(), req.getBgUrl());
        return ResponseResult.success(null);
    }

    /**
     * 内部获取智能体详情
     */
    @Operation(summary = "内部获取智能体详情", description = "内部获取智能体详细信息")
    @PostMapping("internal/get")

    public ResponseResult<AgentBaseVO> getAgentDetailFromInternal(@RequestBody @Valid ItemIdDTO req) {
        AgentEntity entity = agentService.findById(req.getId());
        if (entity == null) {
            return ResponseResult.failure(FAIL_CODE.getCode(), "智能体不存在");
        }
        ModelMapper mapper = ModelMapperConvert.getAgentModelMapper();

        // 使用优化后的异步方法代替直接的异步调用
        tryFillAgentBaseMapAsync();

        return ResponseResult.success(mapper.map(entity, AgentBaseVO.class));
    }

    /**
     * 尝试异步更新缓存，带有冷却时间检查
     */
    private void tryFillAgentBaseMapAsync() {
        long currentTime = System.currentTimeMillis();
        long lastUpdate = lastCacheUpdateTime.get();

        // 如果距离上次更新时间不足指定间隔，则跳过
        if (currentTime - lastUpdate < CACHE_UPDATE_INTERVAL) {
            log.info("距离上次更新时间不足指定间隔，跳过本次更新");
            return;
        }

        // 如果已经有任务在执行，则跳过
        if (!isUpdating.compareAndSet(false, true)) {
            log.info("缓存更新任务已经在执行，跳过本次更新");
            return;
        }

        // 使用自定义线程池执行异步任务
        CompletableFuture.runAsync(() -> {
                    try {
                        isUpdating.set(true);
                        fillAgentBaseMap();
                        // 更新最后更新时间
                        lastCacheUpdateTime.set(System.currentTimeMillis());
                    } catch (Exception e) {
                        log.error("缓存智能体信息失败", e);
                    } finally {
                        // 释放执行标志
                        isUpdating.set(false);
                    }
                }, taskExecutor != null ? taskExecutor : ForkJoinPool.commonPool())
                .orTimeout(30, java.util.concurrent.TimeUnit.SECONDS)
                .exceptionally(ex -> {
                    log.error("异步缓存智能体信息超时或失败: {}", ex.getMessage());
                    isUpdating.set(false); // 确保标志被重置
                    return null;
                });
    }

    /**
     * 填充智能体基础信息缓存
     */
    private void fillAgentBaseMap() {
        List<AgentEntity> entities = agentService.findAll();
        if (entities == null || entities.isEmpty()) {
            return;
        }

        ModelMapper modelMapper = ModelMapperConvert.getAgentBaseModelMapper();
        Map<Long, AgentBaseVO> maps = entities.stream()
                .collect(HashMap::new,
                        (map, entity) -> map.put(entity.getId(), modelMapper.map(entity, AgentBaseVO.class)),
                        HashMap::putAll
                );

        String cacheKey = cacheProperties.getCacheName(AgentCommonCacheConstant.AGENT_BASE_MAP_CACHE_KEY);
        agentProxyService.putAgentMapToCache(cacheKey, maps,
                cacheProperties.getCacheExpiration(AgentCommonCacheConstant.AGENT_BASE_KEY));
    }

    /**
     * 内部获取智能体详情根据IDS
     */
    @Operation(summary = "内部获取智能体详情根据IDS", description = "内部获取智能体详情根据IDS")
    @PostMapping("internal/get_by_ids")
    @Hidden
    public ResponseResult<List<AgentBaseVO>> getAgentsByIds(@RequestBody @Valid AgentQueryByIdsDTO req) {
        List<AgentEntity> entities = agentService.findAllByIds(req.getAgentIds());
        if (entities == null || entities.isEmpty()) {
            return ResponseResult.success(Collections.emptyList());
        }

        ModelMapper modelMapper = ModelMapperConvert.getAgentBaseModelMapper();
        List<AgentBaseVO> vos = entities.stream()
                .map(entity -> modelMapper.map(entity, AgentBaseVO.class))
                .toList();

        // 使用优化后的异步方法代替直接的异步调用
        tryFillAgentBaseMapAsync();

        return ResponseResult.success(vos);
    }

    @Operation(summary = "后台获取智能体详情", description = "后台获取智能体详细信息")
    @PostMapping("admin/get")
    public ResponseResult<AgentDetailVO> adminGetAgentDetail(@RequestBody @Valid ItemIdDTO req) {
        String username = UserContextUtil.getCurrentUsername();
        AgentEntity entity = agentService.findById(req.getId(), username);

        if (entity == null) {
            return ResponseResult.failure(FAIL_CODE.getCode(), "智能体不存在");
        }


        agentService.updateThumbnailNotExist(entity);

        return ResponseResult.success(convertToVOWithFavoriteAndLike(entity, username));
    }

    /**
     * 获取智能体详情
     */
    @Operation(summary = "获取智能体详情", description = "获取智能体详细信息")
    @PostMapping("get")
    public ResponseResult<AgentDetailVO> getAgentDetail(@RequestBody @Valid ItemIdDTO req) {
        String username = UserContextUtil.getCurrentUsername();
        AgentEntity entity = agentService.findById(req.getId(), username);

        if (entity == null) {
            return ResponseResult.failure(FAIL_CODE.getCode(), "智能体不存在");
        }

        if (entity.getIsPublic() == AgentConstant.AGENT_PRIVATE && !entity.getCreator().equals(username)) {
            return ResponseResult.failure(FAIL_CODE.getCode(), "非公开的智能体，无权限查看");
        }

        agentService.updateThumbnailNotExist(entity);

        return ResponseResult.success(convertToVOWithFavoriteAndLike(entity, username));
    }

    /**
     * 获取智能体表详情（不对外）
     */
    @Hidden
    @Operation(summary = "获取智能体表详情（不对外）", description = "获取智能体详细信息")
    @PostMapping("get_entity")
    public ResponseResult<AgentEntity> getAgentDetailEntity(@RequestBody @Valid ItemIdDTO req) {
        AgentEntity entity = agentService.findById(req.getId());
        return ResponseResult.success(entity);
    }

    /**
     * 修改上下架的状态
     */
    @Operation(summary = "修改上下架的状态", description = "修改上下架的状态")
    @PostMapping("admin/modify/shelf_status")
    public ResponseResult<?> modifyShelfStatus(@RequestBody @Valid AgentShelfStatusDTO req) {
        String username = UserContextUtil.getCurrentUsername();
        AgentEntity entity = agentService.findById(req.getAgentId());
        entity.setShelfStatus(req.getStatus());
        entity.setUpdater(username);
        entity.setUpdateTime(LocalDateTime.now());
        entity.setShelfReason(req.getShelfReason());
        if(entity.getShelfStatus() == AgentConstant.SHELF_ON_STATUS){
            entity.setStatus(AgentStatus.PUBLISHED.getCode());
        }
        agentService.updateNotPublic(entity);
        if(req.getStatus().equals(2)){
            agentStoryService.modifyShelfStatusOffByAgentId(req.getAgentId(), entity.getName() + "被下架", username);
        }
        return ResponseResult.success(null);
    }

    /**
     * 删除智能体
     */
    @Operation(summary = "设置智能体为专属", description = "设置智能体为专属")
    @PostMapping("set_exclusive")
    public ResponseResult<?> setExclusiveAgent(@RequestBody @Valid ItemIdDTO req) {
        String username = UserContextUtil.getCurrentUsername();
        if (agentService.existByExclusive()) {
            throw new BusinessException("系统已经存在一个专属智能体，如果需要设置，需要删除旧的");
        }
        AgentEntity entity = agentService.findById(req.getId());
        entity.setIsPublic(AgentConstant.AGENT_EXCLUSIVE);
        validateUserPermission(entity, username);

        entity.setUpdater(username);
        entity.setUpdateTime(LocalDateTime.now());
        agentService.updateNotPublic(entity);

        return ResponseResult.success(null);
    }

    @Operation(summary = "获得专属智能体", description = "获得专属智能体")
    @GetMapping("exclusive")
    public ResponseResult<AgentDetailVO> getExclusiveAgent() {
        String username = UserContextUtil.getCurrentUsername();
        AgentEntity entity = agentService.findByExclusive();

        if (entity == null) {
            return ResponseResult.failure(FAIL_CODE.getCode(), "专属智能体不存在");
        }

        if (entity.getIsPublic() == AgentConstant.AGENT_PRIVATE && !entity.getCreator().equals(username)) {
            return ResponseResult.failure(FAIL_CODE.getCode(), "非公开的智能体，无权限查看");
        }

        agentService.updateThumbnailNotExist(entity);

        return ResponseResult.success(convertToVOWithFavoriteAndLike(entity, username));
    }

    @Operation(summary = "获得专属智能体ID", description = "获得专属智能体ID")
    @GetMapping("internal/exclusive_id")
    public ResponseResult<Long> getExclusiveAgentId() {
        return ResponseResult.success(agentService.findFirstIdByExclusive());
    }

    /**
     * 删除智能体
     */
    @Operation(summary = "删除智能体", description = "删除指定的智能体")
    @PostMapping("delete")
    public ResponseResult<?> deleteAgent(@RequestBody @Valid ItemIdDTO req) {
        return handleAgentDeletion(req.getId(), false);
    }

    @Operation(summary = "删除我智能体", description = "删除指定的智能体")
    @PostMapping("/my/delete")
    public ResponseResult<?> deleteMyAgent(@RequestBody @Valid ItemIdDTO req) {
        return handleAgentDeletion(req.getId(), true);
    }

    /**
     * 处理智能体删除的通用逻辑
     */
    private ResponseResult<?> handleAgentDeletion(Long agentId, boolean validatePermission) {
        String username = UserContextUtil.getCurrentUsername();
        AgentEntity entity = prepareEntityForUpdate(agentId, username, validatePermission);
        agentService.delete(entity);
        agentStoryService.modifyShelfStatusOffByAgentId(entity.getId(), entity.getName() + "被删除", username);
        return ResponseResult.success(null);
    }

    /**
     * 获取当前用户的智能体列表
     */
    @Operation(summary = "获取当前用户的智能体列表", description = "获取当前登录用户的所有智能体")
    @GetMapping("/my")
    public ResponseResult<List<AgentDetailVO>> getMyAgents() {
        String username = UserContextUtil.getCurrentUsername();
        List<AgentEntity> entities = agentService.findAllByCreator(username);

        if (entities.isEmpty()) {
            return ResponseResult.success(Collections.emptyList());
        }

        List<AgentDetailVO> vos = entities.stream()
                .map(entity -> convertToVOWithFavoriteAndLike(entity, username))
                .toList();

        return ResponseResult.success(vos);
    }

    /**
     * 获取当前用户的智能体推荐
     */
    @Operation(summary = "获取当前用户的智能体推荐", description = "获取当前用户的智能体推荐")
    @GetMapping("/my_recommend")
    public ResponseResult<List<AgentDetailVO>> getMyRecommendAgents() {
        String username = UserContextUtil.getCurrentUsername();
        List<AgentEntity> entities = recommendationService.recommendAgentsByUserCharacteristics(username, 1000);

        if (entities.isEmpty()) {
            return ResponseResult.success(Collections.emptyList());
        }

        List<AgentDetailVO> vos = entities.stream()
                .map(entity -> convertToVOWithFavoriteAndLike(entity, username))
                .toList();

        return ResponseResult.success(vos);
    }

    /**
     * 分页获取当前用户的智能体
     */
    @Operation(summary = "分页获取当前用户的智能体", description = "分页获取当前登录用户的智能体")
    @PostMapping("/my/page")
    public ResponseResult<PageBaseContentVo<AgentDetailVO>> pageMyAgents(
            @RequestBody @Valid PageBaseRequest<AgentQueryDTO> params) {
        String username = UserContextUtil.getCurrentUsername();
        log.info("{}当前用户的智能体", username);
        AgentQueryDTO filter = params.getFilter();
        filter.setUsername(username);
        params.setFilter(filter);

        return getPageResponse(params);
    }

    /**
     * 分页获取智能体
     */
    @Operation(summary = "后台分页获取智能体", description = "后台分页获取智能体")
    @PostMapping("/admin/page")
    public ResponseResult<PageBaseContentVo<AgentDetailVO>> adminPageAgents(
            @RequestBody @Valid PageBaseRequest<AgentQueryDTO> params) {
        String username = UserContextUtil.getCurrentUsername();
        log.info("{} 后台page 智能体", username);
        if (params.getFilter() == null) {
            params.setFilter(new AgentQueryDTO());
        }
        params.getFilter().setStartDateTime(params.getFilter().getStartDate());
        params.getFilter().setEndDateTime(params.getFilter().getEndDate());
        return getPageResponse(params);
    }

    @Operation(summary = "分页获取智能体", description = "分页获取智能体")
    @PostMapping("/app/page")
    public ResponseResult<PageBaseContentVo<AgentDetailVO>> appPageAgents(
            @RequestBody @Valid PageBaseRequest<AgentQueryDTO> params) {
        return pageAgents(params);
    }

    /**
     * 分页获取智能体
     */
    @Operation(summary = "分页获取智能体", description = "分页获取智能体")
    @PostMapping("/page")
    public ResponseResult<PageBaseContentVo<AgentDetailVO>> pageAgents(
            @RequestBody @Valid PageBaseRequest<AgentQueryDTO> params) {
        String username = UserContextUtil.getCurrentUsername();
        if (params.getFilter() == null) {
            params.setFilter(new AgentQueryDTO());
        }
        params.getFilter().setUsername(username);
        params.getFilter().setMyPublicUserName(username);

        params.getFilter().setStatus(AgentConstant.AGENT_PUBLISH_SUCCESS);
        params.getFilter().setShelfStatus(AgentConstant.SHELF_ON_STATUS);
        log.info("{}page 智能体", username);
        return getPageResponse(params);
    }

    /**
     * 分页获取公开的智能体
     */
    @Operation(summary = "分页获取公开的智能体", description = "分页获取公开智能体")
    @PostMapping("/public/page")
    public ResponseResult<PageBaseContentVo<AgentDetailVO>> pagePublicAgents(
            @RequestBody @Valid PageBaseRequest<AgentQueryDTO> params) {
        params.getFilter().setIsPublic(AgentConstant.AGENT_PUBLIC);
        params.getFilter().setStatus(AgentConstant.AGENT_PUBLISH_SUCCESS);
        params.getFilter().setShelfStatus(AgentConstant.SHELF_ON_STATUS);
        log.info("public page 智能体");

        return getPageResponse(params);
    }

    /**
     * 通用分页响应处理方法
     */
    private ResponseResult<PageBaseContentVo<AgentDetailVO>> getPageResponse(
            PageBaseRequest<AgentQueryDTO> params) {
        String username = UserContextUtil.getCurrentUsername();
        log.info("用户 {} page 智能体", username != null ? username : "未登录用户");


        // 获取排序字段
        PageOrderField orderFields = Optional.ofNullable(params.getOption())
                .map(PageBaseOptions::getOrderFields)
                .orElse(new PageOrderField());

        // 获取过滤条件
        AgentQueryDTO filter = params.getFilter();
        if (filter.getKeyword() != null && !filter.getKeyword().trim().isEmpty()) {
            if (filter.getTagIds() == null || filter.getTagIds().isEmpty()) {
                List<AgentTagEntity> tagEntities = agentTagService.findAll();
                filter.setTagSearchIds(tagEntities.stream()
                        .filter(tag -> tag.getName() != null && tag.getName().contains(filter.getKeyword()))
                        .map(AgentTagEntity::getId)
                        .toList());
            }
            if (filter.getTypeId() == null || filter.getTypeId() == 0) {
                List<AgentTypeEntity> typeEntities = agentTypeService.findAll();
                List<AgentTypeEntity> typeFilter = typeEntities.stream()
                        .filter(type -> type.getName() != null && type.getName().contains(filter.getKeyword()))
                        .toList();
                filter.setTypeSearchId(typeFilter.stream()
                        .map(AgentTypeEntity::getId)
                        .findFirst()
                        .orElse(0L));
            }
        }
        // 查询数据
        PageInfo<AgentEntity> page = agentService.page(params.getCurrent(), params.getPageSize(), filter, orderFields);
        List<AgentEntity> entities = page.getList();

        // 空结果处理
        if (entities.isEmpty()) {
            if (filter.getKeyword() != null && !filter.getKeyword().trim().isEmpty()) {
                agentSearchRankService.recordSearch(username, filter.getKeyword(), false);
            }
            PaginationVo pagination = new PaginationVo(page.getTotal(), page.getPageNum(), page.getPageSize());
            return ResponseResult.success(new PageBaseContentVo<>(Collections.emptyList(), pagination));
        }

        if (filter.getKeyword() != null && !filter.getKeyword().trim().isEmpty()) {
            agentSearchRankService.recordSearch(username, filter.getKeyword(), true);
        }
        // 转换为VO
        List<AgentDetailVO> vos = entities.stream()
                .peek(agentService::updateThumbnailNotExist)
                .map(entity -> convertToVOWithFavoriteAndLike(entity, username))
                .toList();

        // 创建分页信息
        PaginationVo pagination = new PaginationVo(
                page.getTotal(),
                page.getPageNum(),
                page.getPageSize());

        return ResponseResult.success(new PageBaseContentVo<>(vos, pagination));
    }

    /**
     * 列举支持的音色
     */
    @Operation(summary = "列举支持的音色", description = "获取当前支持的音色")
    @GetMapping("/list_voice")
    public ResponseResult<List<VoiceTimbreVo>> listVoice() {
        return ResponseResult.success(agentRemoteService.findVoiceTimbreList());
    }

    @Operation(summary = "下载文件", description = "下载文件")
    @PostMapping("link/download")
    public ResponseResult<DownloadFileVO> downloadLinkFile(@RequestBody @Valid DownloadFileDTO params) {

        String downloadUrl = DownloadUtil.downloadFile(params.getUrl(), AgentFileConstants.DOWNLOAD_PATH);
        return ResponseResult.success(new DownloadFileVO(downloadUrl));
    }

    /**
     * 上传图片
     */
    @Operation(summary = "上传图片", responses = {
            @ApiResponse(responseCode = "200", description = "成功上传图片")
    })
    @PostMapping(value = "file/upload", consumes = "multipart/form-data")
    public ResponseResult<UploadFileVo> fileUpload(@RequestParam("file") MultipartFile file) {
        // 校验文件
        if (file == null || file.isEmpty()) {
            return ResponseResult.failure(FAIL_CODE.getCode(), "文件不能为空");
        }

        // 校验文件类型
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null || !AgentFileConstants.ALLOWED_EXTENSIONS.contains(
                originalFilename.substring(originalFilename.lastIndexOf(".") + 1).toLowerCase())) {
            return ResponseResult.failure(FAIL_CODE.getCode(), "不支持的文件类型");
        }

        // 校验文件大小
        if (file.getSize() > AgentFileConstants.UPLOAD_MAX_SIZE) {
            return ResponseResult.failure(FAIL_CODE.getCode(), "文件大小超过限制");
        }

        // 处理文件上传
        String filePath = UploadFileUtil.handleFileUpload(file,
                AgentFileConstants.ALLOWED_EXTENSIONS,
                AgentFileConstants.UPLOAD_MAX_SIZE,
                null,
                AgentFileConstants.UPLOAD_PATH);

        return ResponseResult.success(new UploadFileVo(filePath));
    }

    /**
     * 分页获取推荐智能体
     */
    private ResponseResult<PageBaseContentVo<AgentDetailVO>> pageRecommendAgents(String username, int pageNum, int pageSize, Long firstAgentId) {
        PageInfo<AgentEntity> page = recommendationService.recommendAgentsPageByUserCharacteristics(username, pageNum, pageSize, firstAgentId);
        return buildPaginationResponse(page, username);
    }

    /**
     * 分页获取当前用户推荐的智能体
     */
    @Operation(summary = "分页获取当前用户推荐的智能体", description = "分页获取当前登录用户的智能体")
    @PostMapping("recommend/my/page")
    public ResponseResult<PageBaseContentVo<AgentDetailVO>> pageMyRecommend(
            @RequestBody @Valid PageBaseRequest<RecommendAgentQueryDTO> params) {
        String username = UserContextUtil.getCurrentUsername();
        Long firstAgentId = Optional.ofNullable(params.getFilter())
                .map(RecommendAgentQueryDTO::getFirstAgentId)
                .orElse(0L);
        return pageRecommendAgents(username, params.getCurrent(), params.getPageSize(), firstAgentId);
    }
    @Operation(summary = "分页获取用户收藏的智能体列表", description = "分页获取当前用户收藏的所有智能体")
    @PostMapping("/favorites/my/page")
    public ResponseResult<PageBaseContentVo<AgentDetailVO>> pageMyFavorites(
            @RequestBody @Valid PageBaseRequest<MyFavoriteAgentQueryDTO> params) {
        String username = UserContextUtil.getCurrentUsername();
        log.info("{}分页获取收藏的智能体", username);
        return handleUserInteractionPagination(params, UserInteractionType.FAVORITE);
    }
    @Operation(summary = "分页获取用户评论过的智能体列表", description = "分页获取当前用户评论过的所有智能体")
    @PostMapping("/comments/my/page")
    public ResponseResult<PageBaseContentVo<AgentDetailVO>> pageMyComments(
            @RequestBody @Valid PageBaseRequest<MyCommentAgentQueryDTO> params) {
        String username = UserContextUtil.getCurrentUsername();
        log.info("{}分页获取评论过的智能体", username);
        return handleUserInteractionPagination(params, UserInteractionType.COMMENT);
    }
    @Operation(summary = "分页获取用户点赞的智能体列表", description = "分页获取当前用户点赞的所有智能体")
    @PostMapping("/likes/my/page")
    public ResponseResult<PageBaseContentVo<AgentDetailVO>> pageMyLikes(
            @RequestBody @Valid PageBaseRequest<MyLikeAgentQueryDTO> params) {
        String username = UserContextUtil.getCurrentUsername();
        log.info("{}分页获取点赞的智能体", username);
        return handleUserInteractionPagination(params, UserInteractionType.LIKE);
    }
    /**
     * 分页获取系统推荐的智能体
     */
    @Operation(summary = "分页获取系统推荐的智能体", description = "分页获取系统推荐的智能体")
    @PostMapping("recommend/system/page")
    public ResponseResult<PageBaseContentVo<AgentDetailVO>> pageSystemRecommend(
            @RequestBody @Valid PageBaseRequest<RecommendAgentQueryDTO> params) {
        Long firstAgentId = Optional.ofNullable(params.getFilter())
                .map(RecommendAgentQueryDTO::getFirstAgentId)
                .orElse(0L);
        return pageRecommendAgents(null, params.getCurrent(), params.getPageSize(), firstAgentId);
    }

    /**
     * 获得用户和智能体的互动统计
     */
    @Operation(summary = "获得用户和智能体的互动统计", description = "获得用户和智能体的互动统计")
    @PostMapping("internal/user_interaction")
    public ResponseResult<UserInteractionAgentVO> userInteraction(@RequestBody UserInteractionAgentQuery params) {
        // 获取各种统计数据
        int creatCnt = agentService.cntByCreator(params.getUsername());
        int likeCnt = agentLikeService.getUserLikeCnt(params.getUsername());
        int commentCnt = agentCommentService.countByUsernameAndDistinctAgent(params.getUsername());
        int favoriteCnt = agentFavoriteService.countByUsername(params.getUsername());

        // 构建结果对象
        UserInteractionAgentVO vo = new UserInteractionAgentVO();
        vo.setCreatCnt(creatCnt);
        vo.setLikeCnt(likeCnt);
        vo.setCommentCnt(commentCnt);
        vo.setFavoriteCnt(favoriteCnt);
        log.info("{}获得用户和智能体的互动统计{}", params.getUsername(), vo);
        return ResponseResult.success(vo);
    }

    /**
     * 用户维度API集：分页获取当前用户创建的智能体
     */
    @Operation(summary = "分页获取当前用户创造的智能体", description = "分页获取当前用户创造的智能体")
    @PostMapping("create/my/creat")
    public ResponseResult<PageBaseContentVo<AgentDetailVO>> pageMyCreat(
            @RequestBody @Valid PageBaseRequest<MyCreateAgentQueryDTO> params) {
        String username = UserContextUtil.getCurrentUsername();

        // 创建查询条件
        AgentQueryDTO filter = new AgentQueryDTO();
        filter.setUsername(username);

        // 添加可能的额外搜索条件
        MyCreateAgentQueryDTO queryDTO = params.getFilter();
        if (queryDTO != null && queryDTO.getKeyword() != null && !queryDTO.getKeyword().trim().isEmpty()) {
            filter.setKeyword(queryDTO.getKeyword());
        }
        if (queryDTO != null) {
            filter.setIsPublic(queryDTO.getIsPublic());
        }
        // 获取排序字段
        PageOrderField orderFields = Optional.ofNullable(params.getOption())
                .map(PageBaseOptions::getOrderFields)
                .orElse(new PageOrderField());

        // 查询数据
        PageInfo<AgentEntity> page = agentService.page(params.getCurrent(), params.getPageSize(), filter, orderFields);
        List<AgentEntity> entities = page.getList();

        if (entities.isEmpty()) {
            PaginationVo pagination = new PaginationVo(page.getTotal(), page.getPageNum(), page.getPageSize());
            return ResponseResult.success(new PageBaseContentVo<>(Collections.emptyList(), pagination));
        }

        // 转换为VO
        List<AgentDetailVO> vos = entities.stream()
                .peek(agentService::updateThumbnailNotExist)
                .map(entity -> convertToVOWithFavoriteAndLike(entity, username))
                .collect(Collectors.toList());

        PaginationVo pagination = new PaginationVo(page.getTotal(), page.getPageNum(), page.getPageSize());
        return ResponseResult.success(new PageBaseContentVo<>(vos, pagination));
    }

    /**
     * 用户维度API集：分页获取当前用户点赞的智能体
     */
    @Operation(summary = "分页获取当前用户点赞的智能体", description = "分页获取当前用户点赞的智能体")
    @PostMapping("create/my/like")
    public ResponseResult<PageBaseContentVo<AgentDetailVO>> pageMyLike(
            @RequestBody @Valid PageBaseRequest<MyLikeAgentQueryDTO> params) {
        return handleUserInteractionPagination(params, UserInteractionType.LIKE);
    }

    /**
     * 用户维度API集：分页获取当前用户收藏的智能体
     */
    @Operation(summary = "分页获取当前用户收藏的智能体", description = "分页获取当前用户收藏的智能体")
    @PostMapping("create/my/favorite")
    public ResponseResult<PageBaseContentVo<AgentDetailVO>> pageMyFavorite(
            @RequestBody @Valid PageBaseRequest<MyFavoriteAgentQueryDTO> params) {
        return handleUserInteractionPagination(params, UserInteractionType.FAVORITE);
    }

    /**
     * 用户维度API集：分页获取当前用户评论过的智能体
     */
    @Operation(summary = "分页获取当前用户评论过的智能体", description = "分页获取当前用户评论过的智能体")
    @PostMapping("create/my/comment")
    public ResponseResult<PageBaseContentVo<AgentDetailVO>> pageMyComment(
            @RequestBody @Valid PageBaseRequest<MyCommentAgentQueryDTO> params) {
        return handleUserInteractionPagination(params, UserInteractionType.COMMENT);
    }

    /**
     * 用户交互类型枚举
     */
    private enum UserInteractionType {
        LIKE, FAVORITE, COMMENT
    }

    /**
     * 处理用户交互分页的通用方法
     */
    private <T> ResponseResult<PageBaseContentVo<AgentDetailVO>> handleUserInteractionPagination(
            PageBaseRequest<T> params, UserInteractionType type) {
        String username = UserContextUtil.getCurrentUsername();

        PageInfo<AgentEntity> page = switch (type) {
            case LIKE -> {
                MyLikeAgentQueryDTO query = new MyLikeAgentQueryDTO();
                query.setUsername(username);
                yield agentService.pageLikedByUsername(params.getCurrent(), params.getPageSize(), query);
            }
            case FAVORITE -> {
                MyFavoriteAgentQueryDTO query = new MyFavoriteAgentQueryDTO();
                query.setUsername(username);
                yield agentService.pageFavoriteByUsername(params.getCurrent(), params.getPageSize(), query);
            }
            case COMMENT -> {
                MyCommentAgentQueryDTO query = new MyCommentAgentQueryDTO();
                query.setUsername(username);
                yield agentService.pageCommentByUsername(params.getCurrent(), params.getPageSize(), query);
            }
        };

        return buildPaginationResponse(page, username);
    }

    /**
     * 处理用户交互类分页结果
     */
    private ResponseResult<PageBaseContentVo<AgentDetailVO>> processUserInteractionPageResult(
            PageInfo<AgentEntity> page, String username) {
        return buildPaginationResponse(page, username);
    }

    /**
     * 构建分页响应的通用方法
     */
    private ResponseResult<PageBaseContentVo<AgentDetailVO>> buildPaginationResponse(
            PageInfo<AgentEntity> page, String username) {
        List<AgentEntity> entities = page.getList();

        if (entities == null || entities.isEmpty()) {
            PaginationVo pagination = new PaginationVo(page.getTotal(), page.getPageNum(), page.getPageSize());
            return ResponseResult.success(new PageBaseContentVo<>(Collections.emptyList(), pagination));
        }

        // 转换为VO
        List<AgentDetailVO> vos = processEntitiesForPagination(entities, username);
        PaginationVo pagination = new PaginationVo(page.getTotal(), page.getPageNum(), page.getPageSize());
        return ResponseResult.success(new PageBaseContentVo<>(vos, pagination));
    }

    /**
     * 处理实体列表转换为VO的通用方法
     */
    private List<AgentDetailVO> processEntitiesForPagination(List<AgentEntity> entities, String username) {
        return entities.stream()
                .peek(agentService::updateThumbnailNotExist)
                .map(entity -> convertToVOWithFavoriteAndLike(entity, username))
                .collect(Collectors.toList());
    }

    @Operation(summary = "更新我的智能体", description = "更新我的智能体信息")
    @PostMapping("update")
    public ResponseResult<?> updateMyAgent(@RequestBody @Valid AgentUpdateDTO req) {
        String username = UserContextUtil.getCurrentUsername();
        req.setPlatform(2);
        AgentEntity entity = prepareEntityForUpdate(req.getId(), username, true);
        return handleAgentUpdate(entity, req,username);
    }

}