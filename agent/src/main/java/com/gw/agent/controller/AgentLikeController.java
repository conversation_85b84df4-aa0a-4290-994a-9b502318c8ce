package com.gw.agent.controller;

import com.gw.agent.dto.AgentLikeDTO;
import com.gw.agent.entity.AgentEntity;
import com.gw.agent.entity.AgentLikeEntity;
import com.gw.agent.mapper.ModelMapperConvert;
import com.gw.agent.service.AgentLikeService;
import com.gw.agent.service.AgentService;
import com.gw.agent.vo.AgentDetailVO;
import com.gw.common.dto.ItemIdDTO;
import com.gw.common.dto.ResponseResult;
import com.gw.common.user.context.UserContextUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.modelmapper.ModelMapper;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 智能体点赞控制器
 */
@RestController
@RequestMapping("/api/v1/agent/likes")
@RequiredArgsConstructor
@Tag(name = "智能体点赞", description = "智能体点赞相关API")
@Log4j2
public class AgentLikeController {

    private final AgentLikeService agentLikeService;
    private final AgentService agentService;

    /**
     * 将实体转换为VO对象
     */
    private AgentDetailVO convertToVO(AgentEntity entity) {
        ModelMapper modelMapper = ModelMapperConvert.getAgentModelMapper();
        AgentDetailVO vo = new AgentDetailVO();
        modelMapper.map(entity, vo);
        return vo;
    }

    /**
     * 添加点赞
     */
    @Operation(summary = "添加点赞", description = "点赞一个智能体")
    @PostMapping("/add")
    public ResponseResult<?> addLike(@RequestBody @Valid AgentLikeDTO req) {
        String username = UserContextUtil.getCurrentUsername();
        AgentEntity agent = agentService.findById(req.getAgentId());

        agentLikeService.addLike(agent.getId(), username);
        return ResponseResult.success(null);
    }

    /**
     * 取消点赞
     */
    @Operation(summary = "取消点赞", description = "取消点赞一个智能体")
    @PostMapping("/remove")
    public ResponseResult<?> removeLike(@RequestBody @Valid AgentLikeDTO req) {
        String username = UserContextUtil.getCurrentUsername();
        AgentEntity agent = agentService.findById(req.getAgentId());

        boolean result = agentLikeService.removeLike(agent.getId(), username);
        return ResponseResult.success(result);
    }

    /**
     * 获取用户点赞的智能体列表
     */
    @Operation(summary = "获取用户点赞的智能体列表", description = "获取当前用户点赞的所有智能体")
    @GetMapping("/my")
    public ResponseResult<List<AgentDetailVO>> getMyLikes() {
        String username = UserContextUtil.getCurrentUsername();


        List<AgentLikeEntity> likes = agentLikeService.getUserLikes(username);
        List<Long> agentIds = likes.stream().map(AgentLikeEntity::getAgentId).collect(Collectors.toList());

        List<AgentEntity> agents = agentService.findByIdsAndUsername(agentIds, username);
        List<AgentDetailVO> result = agents.stream().map(this::convertToVO).collect(Collectors.toList());

        return ResponseResult.success(result);
    }

    /**
     * 检查智能体是否被点赞
     */
    @Operation(summary = "检查智能体是否被点赞", description = "检查指定智能体是否被当前用户点赞")
    @PostMapping("/check")
    public ResponseResult<Boolean> checkLike(@RequestBody @Valid AgentLikeDTO req) {
        String username = UserContextUtil.getCurrentUsername();

        Integer isLiked = agentLikeService.isLiked(req.getAgentId(), username);
        return ResponseResult.success(isLiked == 1);
    }

    /**
     * 获取智能体点赞数
     */
    @Operation(summary = "获取智能体点赞数", description = "获取指定智能体的点赞数")
    @PostMapping("/count")
    public ResponseResult<Integer> getLikeCount(@RequestBody @Valid ItemIdDTO req) {
        int count = agentLikeService.getLikeCount(req.getId());
        return ResponseResult.success(count);
    }


}