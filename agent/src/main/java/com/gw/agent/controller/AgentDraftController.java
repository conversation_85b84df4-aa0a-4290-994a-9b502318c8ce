package com.gw.agent.controller;

import com.github.pagehelper.PageInfo;
import com.gw.agent.dto.AgentDraftQueryDTO;
import com.gw.agent.dto.AgentDraftSubmitDTO;
import com.gw.agent.entity.AgentDraftEntity;
import com.gw.agent.service.AgentDraftService;
import com.gw.agent.vo.AgentDraftVO;
import com.gw.common.dto.ItemIdDTO;
import com.gw.common.dto.PageBaseRequest;
import com.gw.common.dto.ResponseResult;
import com.gw.common.user.context.UserContextUtil;
import com.gw.common.vo.PageBaseContentVo;
import com.gw.common.vo.PaginationVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequiredArgsConstructor
@RequestMapping("/api/v1/agent/draft")
@Tag(name = "智能体草稿箱", description = "智能体草稿箱相关接口")
@Log4j2
public class AgentDraftController {

    private final AgentDraftService draftService;


    /**
     * 搜索智能体
     */
    @Operation(summary = "提交草稿箱", description = "提交草稿箱")
    @PostMapping("")
    public ResponseResult<AgentDraftVO> submit(@RequestBody @Valid AgentDraftSubmitDTO request) {
        String username = UserContextUtil.getCurrentUsername();
        var entity = AgentDraftSubmitDTO.toEntity(request);
        if (entity.getId() == 0) {
            entity.setId(null);
        }
        entity.setUsername(username);
        entity = draftService.submitDraft(entity);
        return ResponseResult.success(new AgentDraftVO(entity));
    }

    @Operation(summary = "删除草稿箱", description = "删除草稿箱")
    @PostMapping("/delete")
    public ResponseResult<?> delete(@RequestBody @Valid ItemIdDTO req) {
        String username = UserContextUtil.getCurrentUsername();
        var entity = draftService.findById(req.getId());
        if (!entity.getUsername().equals(username)) {
            return ResponseResult.failure("不属于我的草稿箱");
        }
        draftService.deleteDraft(req.getId());
        return ResponseResult.success(null);
    }

    @Operation(summary = "清空草稿箱", description = "清空草稿箱")
    @PostMapping("/delete_all")
    public ResponseResult<?> deleteAll() {
        String username = UserContextUtil.getCurrentUsername();
        draftService.deleteAllByUsername(username);
        return ResponseResult.success(null);
    }
    @Operation(summary = "根据ID获取详情", description = "根据ID获取详情")
    @PostMapping("/my/get")
    public ResponseResult<AgentDraftVO> getMyDraft(@RequestBody @Valid ItemIdDTO req) {
        String username = UserContextUtil.getCurrentUsername();
        var entity = draftService.findById(req.getId());
        if (!entity.getUsername().equals(username)) {
            return ResponseResult.failure("不属于我的草稿箱");
        }
        return ResponseResult.success(new AgentDraftVO(entity));
    }

    @PostMapping("/my/page")
    @Operation(summary = "page我的草稿箱", description = "page我的草稿箱")
    public ResponseResult<PageBaseContentVo<AgentDraftVO>> pageMy(@RequestBody @Valid PageBaseRequest<AgentDraftQueryDTO> params) {

        String username = UserContextUtil.getCurrentUsername();
        PageInfo<AgentDraftEntity> page = draftService.page(params.getCurrent(), params.getPageSize(), username);
        List<AgentDraftEntity> entities = page.getList();

        List<AgentDraftVO> vos = entities.stream()
                .map(entity -> new AgentDraftVO(entity))
                .toList();
        PaginationVo pagination = new PaginationVo(
                page.getTotal(),
                page.getPageNum(),
                page.getPageSize());
        return ResponseResult.success(new PageBaseContentVo<>(vos, pagination));
    }
} 