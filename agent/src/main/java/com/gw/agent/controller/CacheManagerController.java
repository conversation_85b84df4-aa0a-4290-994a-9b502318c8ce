package com.gw.agent.controller;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.stats.CacheStats;
import com.gw.agent.config.CacheProperties;
import com.gw.common.dto.ResponseResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.cache.CacheManager;
import org.springframework.cache.caffeine.CaffeineCache;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.Duration;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;

/**
 * 缓存管理控制器
 */
@RestController
@RequestMapping("/api/v1/agent/cache")
@RequiredArgsConstructor
@Log4j2
@Tag(name = "缓存管理", description = "缓存管理相关API")
public class CacheManagerController {

    private final CacheManager cacheManager;

    private final RedisTemplate<String, Object> redisTemplate;
    private final CacheProperties cacheProperties;

    /**
     * 获取缓存信息统计
     *
     * @return 缓存信息统计
     */
    @GetMapping("/info")
    @Operation(summary = "获取缓存信息", description = "获取系统中所有缓存的大小和过期时间")
    public ResponseResult<Map<String, Object>> getCacheInfo() {
        Map<String, Object> result = new HashMap<>();
        Map<String, Object> cacheDetails = new HashMap<>();
        long totalSize = 0;

        // 获取所有缓存名称
        Collection<String> cacheNames = cacheManager.getCacheNames();

        // 遍历缓存名称
        for (String cacheName : cacheNames) {
            org.springframework.cache.Cache springCache = cacheManager.getCache(cacheName);
            if (springCache instanceof CaffeineCache caffeineCache) {
                Cache<Object, Object> nativeCache = caffeineCache.getNativeCache();

                // 获取缓存统计信息
                CacheStats stats = nativeCache.stats();

                // 获取条目数量
                long entryCount = nativeCache.estimatedSize();
                totalSize += entryCount;

                Map<String, Object> cacheInfo = new HashMap<>();
                cacheInfo.put("entryCount", entryCount);
                cacheInfo.put("hitCount", stats.hitCount());
                cacheInfo.put("missCount", stats.missCount());
                cacheInfo.put("hitRate", String.format("%.2f%%", stats.hitRate() * 100));

                // 获取过期时间
                Duration expiration = cacheProperties.getCacheExpiration(cacheName);
                cacheInfo.put("expiration", expiration.toSeconds() + "秒");

                cacheDetails.put(cacheName, cacheInfo);
            }
        }

        result.put("cacheDetails", cacheDetails);
        result.put("totalSize", formatSize(totalSize));

        return ResponseResult.success(result);
    }

    /**
     * 格式化大小，根据不同的单位显示（B, KB, MB, GB）
     *
     * @param size 大小（字节）
     * @return 格式化后的大小字符串
     */
    private String formatSize(long size) {
        if (size < 1024) {
            return size + " B";
        } else if (size < 1024 * 1024) {
            return String.format("%.2f KB", size / 1024.0);
        } else if (size < 1024 * 1024 * 1024) {
            return String.format("%.2f MB", size / (1024.0 * 1024));
        } else {
            return String.format("%.2f GB", size / (1024.0 * 1024 * 1024));
        }
    }
} 