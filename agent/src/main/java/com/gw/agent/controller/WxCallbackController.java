package com.gw.agent.controller;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.gw.agent.config.WxMaConfiguration;
import com.gw.agent.constant.WxConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Arrays;

/**
 * 微信回调控制器
 * 处理微信安全检测等回调推送
 */
@Tag(name = "微信回调接口", description = "处理微信小程序回调推送")
@RestController
@RequestMapping("/api/v1/agent/wx/callback")
@RequiredArgsConstructor
@Log4j2
public class WxCallbackController {

    private final WxMaConfiguration wxMaConfiguration;

    /**
     * 接收微信安全检测回调推送（GET请求，用于URL验证）
     * 微信服务器在配置URL时会发送GET请求验证服务器的有效性
     * <p>
     * 验证流程：
     * 1. 将Token、timestamp、nonce三个参数进行字典序排序
     * 2. 将三个参数字符串拼接成一个字符串进行SHA1加密
     * 3. 开发者获得加密后的字符串可与signature对比，标识该请求来源于微信
     * 4. 若验证成功，请原样返回echostr参数内容
     * 文档：<a href="https://developers.weixin.qq.com/miniprogram/dev/OpenApiDoc/sec-center/sec-check/mediaCheckAsync.html">...</a>
     */
    @Operation(summary = "微信回调URL验证", description = "验证微信回调URL的有效性，验证成功后返回echostr")
    @GetMapping("/security-check")
    public ResponseEntity<String> handleSecurityCheckCallbackGet(
            @Parameter(description = "微信加密签名", required = true)
            @RequestParam String signature,
            @Parameter(description = "时间戳", required = true)
            @RequestParam String timestamp,
            @Parameter(description = "随机数", required = true)
            @RequestParam String nonce,
            @Parameter(description = "随机字符串", required = true)
            @RequestParam String echostr
    ) {

        log.info("接收到微信安全检测回调推送, signature: {}, timestamp: {}, nonce: {}, echostr: {}",
                signature, timestamp, nonce, echostr);

        // 验证签名
        if (!verifySignature(signature, timestamp, nonce)) {
            log.error("微信回调签名验证失败, signature: {}, timestamp: {}, nonce: {}",
                    signature, timestamp, nonce);
            return ResponseEntity.ok(echostr);
        }

        log.info("微信回调签名验证成功，返回echostr: {}", echostr);
        return ResponseEntity.ok(echostr);
    }

    /**
     * 验证微信签名
     * 实现微信官方的签名验证算法：
     * 1. 将Token、timestamp、nonce三个参数进行字典序排序
     * 2. 将三个参数字符串拼接成一个字符串
     * 3. 对拼接后的字符串进行SHA1加密
     * 4. 将加密结果与微信传递的signature进行比较
     *
     * @param signature 微信传递的签名
     * @param timestamp 时间戳
     * @param nonce     随机数
     * @return 验证结果，true表示验证通过，false表示验证失败
     */
    private boolean verifySignature(String signature, String timestamp, String nonce) {
        try {
            String token = wxMaConfiguration.getCallbackToken();

            // 将token、timestamp、nonce三个参数进行字典序排序
            String[] params = {token, timestamp, nonce};
            Arrays.sort(params);

            // 拼接字符串
            String concatenated = String.join("", params);
            log.debug("拼接后的字符串: {}", concatenated);

            // 计算SHA1签名
            String calculatedSignature = sha1(concatenated);
            log.debug("计算得到的签名: {}, 微信传递的签名: {}", calculatedSignature, signature);

            // 比较签名
            return calculatedSignature.equals(signature);

        } catch (Exception e) {
            log.error("验证微信签名时发生异常: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * SHA1加密
     * 使用标准的SHA1算法对输入字符串进行加密
     *
     * @param input 待加密字符串
     * @return SHA1加密结果（小写十六进制字符串）
     * @throws NoSuchAlgorithmException 如果SHA1算法不可用
     */
    private String sha1(String input) throws NoSuchAlgorithmException {
        MessageDigest md = MessageDigest.getInstance(WxConstants.SHA1_ALGORITHM);
        byte[] bytes = md.digest(input.getBytes());

        StringBuilder sb = new StringBuilder();
        for (byte b : bytes) {
            sb.append(String.format(WxConstants.HEX_FORMAT, b));
        }
        return sb.toString();
    }

    /**
     * 接收微信安全检测回调推送（POST请求，实际的回调数据）
     */
    @Operation(summary = "微信安全检测回调", description = "接收微信安全检测的回调结果")
    @PostMapping("/security-check")
    public ResponseEntity<String> handleSecurityCheckCallback(
            @Parameter(description = "回调数据", required = true)
            @RequestBody String requestBody) {

        log.info("接收到微信安全检测回调推送, requestBody: {}", requestBody);

        try {
            // 解析回调数据
            JSONObject callbackData = JSON.parseObject(requestBody);

            // 验证回调数据格式
            if (!isValidSecurityCheckCallback(callbackData)) {
                log.error("无效的安全检测回调数据格式: {}", requestBody);
                return ResponseEntity.badRequest().body("fail");
            }

        } catch (Exception e) {
            log.error("处理微信安全检测回调推送异常: {}", e.getMessage(), e);
        }
        return ResponseEntity.ok("success");
    }

    /**
     * 验证安全检测回调数据格式
     *
     * @param callbackData 回调数据JSON对象
     * @return 验证结果
     */
    private boolean isValidSecurityCheckCallback(JSONObject callbackData) {
        if (callbackData == null) {
            return false;
        }

        // 检查必要字段
        String msgType = callbackData.getString("MsgType");
        String event = callbackData.getString("Event");
        String traceId = callbackData.getString("trace_id");

        return "event".equals(msgType) &&
                "wxa_media_check".equals(event) &&
                traceId != null && !traceId.trim().isEmpty();
    }
}