package com.gw.agent.controller;

import com.gw.agent.dto.AgentCommentLikeDTO;
import com.gw.agent.entity.AgentCommentLikeEntity;
import com.gw.agent.service.AgentCommentLikeService;
import com.gw.agent.service.AgentStatisticsService;
import com.gw.common.dto.ItemIdDTO;
import com.gw.common.dto.ResponseResult;
import com.gw.common.user.context.UserContextUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 智能体评论点赞控制器
 */
@RestController
@RequestMapping("/api/v1/agent/comment/like")
@RequiredArgsConstructor
@Tag(name = "智能体评论点赞", description = "智能体评论点赞相关接口")
public class AgentCommentLikeController {
    
    private final AgentCommentLikeService commentLikeService;
    private final AgentStatisticsService statisticsService;
    /**
     * 点赞/取消点赞评论
     */
    @Operation(summary = "点赞/取消点赞评论", description = "对评论进行点赞或取消点赞操作")
    @PostMapping("/toggle")
    public ResponseResult<Boolean> toggleLike(@RequestBody @Valid AgentCommentLikeDTO req) {
        String username = UserContextUtil.getCurrentUsername();
        boolean result = commentLikeService.toggleLike(req.getCommentId(), username);
        return ResponseResult.success(result);
    }

    /**
     * 点赞评论
     */
    @Operation(summary = "点赞评论", description = "对评论进行点赞")
    @PostMapping("/add")
    public ResponseResult<Void> addLike(@RequestBody @Valid ItemIdDTO req) {
        String username = UserContextUtil.getCurrentUsername();
        commentLikeService.addLike(req.getId(), username);
        return ResponseResult.success(null);
    }

    /**
     * 取消点赞评论
     */
    @Operation(summary = "取消点赞评论", description = "取消对评论的点赞")
    @PostMapping("/remove")
    public ResponseResult<Void> removeLike(@RequestBody @Valid ItemIdDTO req) {
        String username = UserContextUtil.getCurrentUsername();
        commentLikeService.removeLike(req.getId(), username);
        return ResponseResult.success(null);
    }

    /**
     * 获取评论点赞数
     */
    @Operation(summary = "获取评论点赞数", description = "获取指定评论的点赞数量")
    @PostMapping("/count")
    public ResponseResult<Integer> getLikeCount(@RequestBody @Valid ItemIdDTO req) {
        var statistics = statisticsService.findByAgentId(req.getId());
        int count = statistics == null ? 0 : statistics.getLikeCount();
        return ResponseResult.success(count);
    }

    /**
     * 批量获取评论点赞数
     */
    @Operation(summary = "批量获取评论点赞数", description = "批量获取多个评论的点赞数量")
    @PostMapping("/batch/count")
    public ResponseResult<Map<Long, Integer>> batchGetLikeCount(@RequestBody @Valid List<Long> commentIds) {
        Map<Long, Integer> result = commentLikeService.batchGetLikeCount(commentIds);
        return ResponseResult.success(result);
    }

    /**
     * 检查用户是否已点赞评论
     */
    @Operation(summary = "检查用户是否已点赞评论", description = "检查当前用户是否已对指定评论点赞")
    @PostMapping("/status")
    public ResponseResult<Boolean> checkLikeStatus(@RequestBody @Valid ItemIdDTO req) {
        String username = UserContextUtil.getCurrentUsername();
        int status = commentLikeService.isLiked(req.getId(), username);
        return ResponseResult.success(status == 1);
    }

    /**
     * 批量检查用户点赞状态
     */
    @Operation(summary = "批量检查用户点赞状态", description = "批量检查当前用户对多个评论的点赞状态")
    @PostMapping("/batch/status")
    public ResponseResult<Map<Long, Boolean>> batchCheckLikeStatus(@RequestBody @Valid List<Long> commentIds) {
        String username = UserContextUtil.getCurrentUsername();
        Map<Long, Integer> statusMap = commentLikeService.batchIsLiked(commentIds, username);
        Map<Long, Boolean> result = statusMap.entrySet().stream()
                .collect(java.util.stream.Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> entry.getValue() == 1
                ));
        return ResponseResult.success(result);
    }

    /**
     * 获取用户点赞的评论列表
     */
    @Operation(summary = "获取用户点赞的评论列表", description = "获取当前用户点赞的所有评论")
    @GetMapping("/my")
    public ResponseResult<List<AgentCommentLikeEntity>> getUserLikedComments() {
        String username = UserContextUtil.getCurrentUsername();
        List<AgentCommentLikeEntity> result = commentLikeService.getUserLikedComments(username);
        return ResponseResult.success(result);
    }
}
