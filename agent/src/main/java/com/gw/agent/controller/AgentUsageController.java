package com.gw.agent.controller;

import com.gw.agent.service.AgentService;
import com.gw.agent.service.AgentUsageRecordService;
import com.gw.common.dto.ResponseResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.extern.log4j.Log4j2;
import org.springframework.web.bind.annotation.*;

/**
 * 智能体使用记录控制器
 */
@RestController
@RequestMapping("/api/v1/agent/usage")
@RequiredArgsConstructor
@Tag(name = "智能体使用统计", description = "智能体使用记录相关API")
@Log4j2
public class AgentUsageController {

    private final AgentService agentService;
    private final AgentUsageRecordService agentUsageRecordService;

    /**
     * 记录用户使用智能体
     */
    @Operation(summary = "记录用户使用智能体", description = "记录用户使用智能体，如果是首次使用则增加智能体热度值")
    @PostMapping("/record")
    public boolean recordAgentUsage(@RequestParam Long agentId, @RequestParam String username) {
        try {
            return agentService.recordAgentUsage(agentId, username); // 确保返回纯boolean值
        } catch (Exception e) {
            log.error("记录智能体使用失败", e);
            return false;
        }
    }

    /**
     * 获取智能体的使用人数
     */
    @Operation(summary = "获取智能体使用人数", description = "获取使用该智能体的用户数量")
    @GetMapping("/user-count")
    public int getUserCount(@RequestParam Long agentId) {
        return agentUsageRecordService.getUserCount(agentId);
    }

    /**
     * 获取智能体的使用信息
     */
    @Operation(summary = "获取智能体使用信息", description = "获取智能体的使用统计信息")
    @GetMapping("/info")
    public ResponseResult<AgentUsageInfoVO> getAgentUsageInfo(@RequestParam Long agentId) {
        int userCount = agentUsageRecordService.getUserCount(agentId);
        AgentUsageInfoVO vo = new AgentUsageInfoVO();
        vo.setAgentId(agentId);
        vo.setUserCount(userCount);
        return ResponseResult.success(vo);
    }

    /**
     * 智能体使用信息VO
     */
    @Setter
    @Getter
    public static class AgentUsageInfoVO {
        private Long agentId;
        private Integer userCount;

    }
}