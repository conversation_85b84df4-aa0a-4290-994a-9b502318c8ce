package com.gw.agent.controller;

import com.github.pagehelper.PageInfo;
import com.gw.agent.dto.StoryDraftQueryDTO;
import com.gw.agent.dto.StoryDraftSubmitDTO;
import com.gw.agent.entity.StoryDraftEntity;
import com.gw.agent.service.StoryDraftService;
import com.gw.agent.vo.StoryDraftVO;
import com.gw.common.dto.ItemIdDTO;
import com.gw.common.dto.PageBaseRequest;
import com.gw.common.dto.ResponseResult;
import com.gw.common.user.context.UserContextUtil;
import com.gw.common.vo.PageBaseContentVo;
import com.gw.common.vo.PaginationVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequiredArgsConstructor
@RequestMapping("/api/v1/agent/story/draft")
@Tag(name = "群聊草稿箱", description = "群聊草稿箱相关接口")
@Log4j2
public class StoryDraftController {

    private final StoryDraftService draftService;


    /**
     * 搜索智能体
     */
    @Operation(summary = "提交草稿箱", description = "提交草稿箱")
    @PostMapping("")
    public ResponseResult<StoryDraftVO> submit(@RequestBody @Valid StoryDraftSubmitDTO request) {
        String username = UserContextUtil.getCurrentUsername();
        var entity = StoryDraftSubmitDTO.toEntity(request);
        if (entity.getId() == 0) {
            entity.setId(null);
        }
        entity.setUsername(username);
        entity = draftService.submitDraft(entity);
        return ResponseResult.success(new StoryDraftVO(entity));
    }

    @Operation(summary = "删除草稿箱", description = "删除草稿箱")
    @PostMapping("/delete")
    public ResponseResult<?> delete(@RequestBody @Valid ItemIdDTO req) {
        String username = UserContextUtil.getCurrentUsername();
        var entity = draftService.findById(req.getId());
        if (!entity.getUsername().equals(username)) {
            return ResponseResult.failure("不属于我的草稿箱");
        }
        draftService.deleteDraft(req.getId());
        return ResponseResult.success(null);
    }

    @Operation(summary = "清空草稿箱", description = "清空草稿箱")
    @PostMapping("/delete_all")
    public ResponseResult<?> deleteAll() {
        String username = UserContextUtil.getCurrentUsername();
        draftService.deleteAllByUsername(username);
        return ResponseResult.success(null);
    }

    @Operation(summary = "根据ID获取详情", description = "根据ID获取详情")
    @PostMapping("/my/get")
    public ResponseResult<StoryDraftVO> getMyDraft(@RequestBody @Valid ItemIdDTO req) {
        String username = UserContextUtil.getCurrentUsername();
        var entity = draftService.findById(req.getId());
        if (!entity.getUsername().equals(username)) {
            return ResponseResult.failure("不属于我的草稿箱");
        }
        return ResponseResult.success(new StoryDraftVO(entity));
    }
    @PostMapping("/my/page")
    @Operation(summary = "page我的草稿箱", description = "page我的草稿箱")
    public ResponseResult<PageBaseContentVo<StoryDraftVO>> pageMy(@RequestBody @Valid PageBaseRequest<StoryDraftQueryDTO> params) {

        String username = UserContextUtil.getCurrentUsername();
        PageInfo<StoryDraftEntity> page = draftService.page(params.getCurrent(), params.getPageSize(), username);
        List<StoryDraftEntity> entities = page.getList();

        List<StoryDraftVO> vos = entities.stream()
                .map(entity -> new StoryDraftVO(entity))
                .toList();
        PaginationVo pagination = new PaginationVo(
                page.getTotal(),
                page.getPageNum(),
                page.getPageSize());
        return ResponseResult.success(new PageBaseContentVo<>(vos, pagination));
    }
} 