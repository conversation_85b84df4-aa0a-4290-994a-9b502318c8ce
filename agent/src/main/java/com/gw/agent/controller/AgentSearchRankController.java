package com.gw.agent.controller;

import com.gw.agent.dto.AgentSearchRankDTO;
import com.gw.agent.dto.SearchAgentRequestDTO;
import com.gw.agent.entity.AgentSearchEntity;
import com.gw.agent.entity.AgentSearchRankEntity;
import com.gw.agent.mapper.ModelMapperConvert;
import com.gw.agent.service.AgentSearchRankService;
import com.gw.agent.service.AgentSearchService;
import com.gw.agent.vo.AgentSearchRankVO;
import com.gw.agent.vo.AgentSearchResponseVO;
import com.gw.common.agent.constant.AgentConstant;
import com.gw.common.dto.ItemIdDTO;
import com.gw.common.dto.ResponseResult;
import com.gw.common.user.context.UserContextUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.modelmapper.ModelMapper;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

/**
 * 智能体热搜榜控制器
 * <p>
 * 注意：当前实体类模型正在开发中，存在一些编译问题需要解决。
 * 主要修改：
 * 1. AgentSearchRankEntity 不再继承 BaseEntity，只保留 id 和更新时间字段
 * 2. 数据库表结构已经简化，移除了create_time和deleted字段
 * 3. 用户标识从userId改为username
 * 4. 新增了对AgentType、AgentTag、AgentName的搜索支持
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/v1/agent/search-rank")
@Tag(name = "智能体热搜", description = "智能体热搜相关接口")
@Log4j2
public class AgentSearchRankController {

    private final AgentSearchService agentSearchService;
    private final AgentSearchRankService agentSearchRankService;

    /**
     * 搜索智能体
     */
    @Operation(summary = "智能体搜索", description = "模糊搜索智能体，匹配名称、类型和标签")
    @PostMapping("/search")
    public ResponseResult<List<AgentSearchResponseVO>> searchAgents(@RequestBody @Valid SearchAgentRequestDTO request) {
        if (request.getKeyword() == null || request.getKeyword().trim().isEmpty()) {
            return ResponseResult.success(new ArrayList<>());
        }
        List<AgentSearchEntity> result = agentSearchService.searchAgents(
                request.getKeyword(),
                request.getLimit() * 2, AgentConstant.AGENT_PUBLISH_SUCCESS, AgentConstant.AGENT_PUBLIC);
        if (result == null || result.isEmpty()) {
            return ResponseResult.success(new ArrayList<>());
        }

        ModelMapper modelMapper = ModelMapperConvert.getBaseModelMapper();
        List<AgentSearchResponseVO> vos = result.stream()
                .map(entity -> modelMapper.map(entity, AgentSearchResponseVO.class))
                .filter(vo -> vo.getKeyword() != null)
                .distinct()
                .limit(request.getLimit())
                .toList();
        return ResponseResult.success(vos);
    }

    @Operation(summary = "根据ID删除搜索项", description = "根据ID删除搜索项")
    @PostMapping("/delete")
    public ResponseResult<?> delete(@RequestBody @Valid ItemIdDTO req) {
        String username = UserContextUtil.getCurrentUsername();
        AgentSearchRankEntity entity = agentSearchRankService.findById(req.getId());
        if (entity != null && entity.getUsername() != null && entity.getUsername().equals(username)) {
            agentSearchRankService.deleteById(req.getId());
        } else {
            log.error("不属于我的搜索项");
            return ResponseResult.failure("不属于我的搜索项");
        }
        return ResponseResult.success(null);
    }

    @Operation(summary = "删除我的所有搜索项", description = "删除我的所有搜索项")
    @PostMapping("/delete_all_my")
    public ResponseResult<?> deleteAllMy() {
        String username = UserContextUtil.getCurrentUsername();
        agentSearchRankService.deleteByUsername(username);
        return ResponseResult.success(null);
    }

    /**
     * 获取用户搜索排行榜
     */
    @PostMapping("/personal/rank")
    @Operation(summary = "获取用户搜索排行榜", description = "获取当前用户的搜索排行榜数据")
    public ResponseResult<List<AgentSearchRankVO>> getUserSearchRank(@RequestBody @Valid AgentSearchRankDTO params) {
        String username = UserContextUtil.getCurrentUsername();
        List<AgentSearchRankEntity> rankEntities = agentSearchRankService.getUserSearchRank(username, params.getLimit());
        ModelMapper modelMapper = ModelMapperConvert.getBaseModelMapper();
        List<AgentSearchRankVO> vos = rankEntities.stream()
                .map(entity -> modelMapper.map(entity, AgentSearchRankVO.class))
                .toList();
        return ResponseResult.success(vos);
    }

    @PostMapping("/global/rank")
    @Operation(summary = "获取全局搜索排行榜", description = "获取全局搜索排行榜数据")
    public ResponseResult<List<AgentSearchRankVO>> getGlobalSearchRank(@RequestBody @Valid AgentSearchRankDTO params) {

        List<AgentSearchRankEntity> rankEntities = agentSearchRankService.getGlobalSearchRank(params.getLimit());
        ModelMapper modelMapper = ModelMapperConvert.getBaseModelMapper();
        List<AgentSearchRankVO> vos = rankEntities.stream()
                .map(entity -> modelMapper.map(entity, AgentSearchRankVO.class))
                .toList();
        return ResponseResult.success(vos);
    }
//    /**
//     * 查询个人热搜关键词列表
//     */
//    @GetMapping("/personal/keywords")
//    @Operation(summary = "查询个人热搜关键词列表", description = "查询当前用户的热搜关键词列表")
//    public ResponseResult<List<String>> getPersonalHotKeywords(@RequestParam(defaultValue = "10") Integer limit) {
//        // 注意：实际项目中需要从UserContextUtil获取username，但目前有编译问题，暂时硬编码
//        String username = UserContextUtil.getCurrentUsername();
//        List<String> result = agentSearchRankService.getPersonalHotKeywords(username, limit);
//        return ResponseResult.success(null);
//    }
//
//    /**
//     * 查询全局热搜关键词列表
//     */
//    @GetMapping("/global/keywords")
//    @Operation(summary = "查询全局热搜关键词列表", description = "查询全平台的热搜关键词列表")
//    public ResponseResult<List<String>> getGlobalHotKeywords(@RequestParam(defaultValue = "10") Integer limit) {
//        List<String> result = agentSearchRankService.getGlobalHotKeywords(limit);
//        return ResponseResult.success(null);
//    }
} 