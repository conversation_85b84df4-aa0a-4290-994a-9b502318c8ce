package com.gw.agent.constant;

import java.util.Arrays;
import java.util.List;

/**
 * 智能体文件相关常量
 */
public class AgentFileConstants {

    /**
     * 文件上传路径
     */
    public static final String UPLOAD_PATH = "upload/file/agent/material/";
    public static final String DOWNLOAD_PATH = "upload/file/agent/download/";
    public static final String UPLOAD_STORY_PATH = "upload/file/agent/story/";
    public static final String UPLOAD_STORY_COMMENT_PATH = "upload/file/agent/comment/story/";
    public static final String UPLOAD_AGENT_COMMENT_PATH = "upload/file/agent/comment/agent/";
    /**
     * 上传文件最大大小 (100MB)
     */
    public static final int UPLOAD_MAX_SIZE = 100 * 1024 * 1024;

    /**
     * 允许上传的文件扩展名
     */
    public static final List<String> ALLOWED_EXTENSIONS = Arrays.asList("png", "jpg", "jpeg");


}
