package com.gw.agent.constant;

/**
 * 智能体推荐服务常量
 */
public final class AgentRecommendationConstants {
    /**
     * 推荐智能体基于性别的比例
     */
    public static final double GENDER_BASED_RECOMMENDATION_RATIO = 0.3;

    /**
     * 推荐智能体基于标签的比例
     */
    public static final double TAG_BASED_RECOMMENDATION_RATIO = 0.5;

    /**
     * 默认推荐数量
     */
    public static final int DEFAULT_RECOMMENDATION_LIMIT = 300;

    /**
     * 候选智能体扩大倍数
     */
    public static final int CANDIDATE_MULTIPLIER = 2;

    /**
     * Redis推荐智能体缓存key前缀
     */
    public static final String AGENT_RECOMMEND_IDS_KEY_PREFIX = "ids:";

    /**
     * Redis推荐智能体分页缓存key前缀
     */
    public static final String AGENT_RECOMMEND_PAGE_KEY_PREFIX = "ids:page:";

    /**
     * Redis公开智能体缓存key
     */
    public static final String AGENT_PUBLIC_KEY = "agent:public:list";

    /**
     * Redis缓存过期时间(1天)
     */
    public static final long REDIS_CACHE_EXPIRE_SECONDS = 24 * 60 * 60;

    /**
     * Redis分页缓存过期时间(2小时)
     */
    public static final long REDIS_PAGE_CACHE_EXPIRE_SECONDS = 2 * 60 * 60;

    private AgentRecommendationConstants() {
        // 防止实例化
    }
}
