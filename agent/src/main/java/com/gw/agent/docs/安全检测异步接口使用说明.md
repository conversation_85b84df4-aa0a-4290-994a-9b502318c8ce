# 微信小程序图片异步安全检测接口使用说明

## 概述

本文档介绍如何使用微信小程序的 `mediaCheckAsync` 接口进行图片异步安全检测，以及对应的回调推送处理机制。

## 主要组件

### 1. SecurityCheckService

负责调用微信的异步图片检测接口。

**主要方法:**

- `checkImage(String type, File file, AgentEntity entity)`: 提交图片进行异步检测

**关键特性:**

- 使用 `mediaCheckAsync` 接口进行异步检测
- 支持 2.0 版本的检测规范
- 自动处理 trace_id 的记录和跟踪

### 2. WxCallbackController

处理微信安全检测的回调推送。

**接口路径:** `POST /api/v1/wx/callback/security-check`

**功能:**

- 接收微信推送的检测结果
- 验证回调数据格式
- 调用业务服务处理结果

### 3. SecurityCheckCallbackService

处理安全检测回调的业务逻辑。

**主要功能:**

- 解析微信回调数据
- 根据 trace_id 查找对应的智能体
- 更新检测状态和结果
- 发布检测通过事件

## 工作流程

### 1. 异步检测提交

```java
// 在 SecurityCheckService.checkImage() 中
WxMaMediaAsyncCheckRequest request = WxMaMediaAsyncCheckRequest.builder()
    .mediaUrl(file.getAbsolutePath()) // 图片URL
    .mediaType(2) // 2表示图片
    .version(2) // 2.0版本
    .scene(4) // 4表示社交日志
    .openid(entity.getWxOpenId())
    .build();

WxMaMediaAsyncCheckResponse response = wxMaService.getSecurityService().mediaCheckAsync(request);
```

### 2. 回调接收和处理

微信检测完成后，会向配置的回调URL推送结果：

```json
{
    "ToUserName": "gh_9df7d78a1234",
    "FromUserName": "o4_t144jTUSEoxydysUA2E234_tc",
    "CreateTime": 1626959646,
    "MsgType": "event",
    "Event": "wxa_media_check",
    "appid": "wx8f16a5be77871234",
    "trace_id": "60f96f1d-3845297a-1976a3ae",
    "version": 2,
    "detail": [{
        "strategy": "content_model",
        "errcode": 0,
        "suggest": "pass",
        "label": 100,
        "prob": 90
    }],
    "errcode": 0,
    "errmsg": "ok",
    "result": {
        "suggest": "pass",
        "label": 100
    }
}
```

### 3. 结果处理逻辑

- **检测通过 (suggest="pass")**: 更新状态为通过，发布检测通过事件
- **检测未通过**: 更新状态为失败，记录详细错误信息
- **检测失败**: 记录系统错误信息

## 配置要求

### 1. 微信小程序配置

在微信小程序后台配置消息推送URL：

```
https://your-domain.com/api/v1/wx/callback/security-check
```

### 2. 应用配置

```yaml
wx:
  miniapp:
    appid: your_appid
    secret: your_secret
```

## 数据库设计

### Agent表字段

- `security_check_result`: 存储检测结果和trace_id
- `status`: 智能体状态（检测中、通过、失败等）

### trace_id查找机制

通过 `AgentMapper.findAgentIdByTraceId()` 方法，从 `security_check_result` 字段中匹配 trace_id 来找到对应的智能体。

## 错误处理

### 常见错误码

- `-1008`: 下载错误，检查媒体链接是否有效
- `其他错误码`: 参考微信官方文档

### 业务异常处理

- 网络超时：记录错误，支持重试机制
- 格式错误：验证输入参数
- 系统异常：记录完整错误堆栈

## 使用示例

### 提交图片检测

```java
@Autowired
private SecurityCheckService securityCheckService;

// 在需要检测图片的地方调用
File imageFile = new File("/path/to/image.jpg");
AgentEntity entity = getAgentEntity();
securityCheckService.checkImage("头像图片", imageFile, entity);
```

### 处理检测结果

系统会自动处理回调结果，无需手动干预。检测通过后会自动发布 `SecurityCheckPassedEvent` 事件。

## 注意事项

1. **频率限制**: 单个 appId 调用上限为 2000 次/分钟，200,000 次/天
2. **文件大小**: 单个文件大小不超过 10M
3. **URL要求**: media_url 需要保证可以被检测服务器下载
4. **回调超时**: 异步检测结果在 30 分钟内推送
5. **重复推送**: 如果回调处理失败，微信可能会重复推送，需要做好幂等性处理

## 监控和日志

### 关键日志点

- 检测提交成功/失败
- 回调接收和处理
- trace_id 匹配结果
- 最终检测状态更新

### 监控指标

- 检测提交成功率
- 回调处理成功率
- 检测通过率
- 平均处理时间 