DROP TABLE IF EXISTS "public"."t_agent_story_comment";
DROP SEQUENCE IF EXISTS t_agent_story_comment_id_seq;

-- 创建序列
CREATE SEQUENCE t_agent_story_comment_id_seq START 1 CACHE 1;

CREATE TABLE "public"."t_agent_story_comment"
(
    "id"          int8                                        NOT NULL DEFAULT nextval('t_agent_story_comment_id_seq'::regclass),
    "story_id"    int8                                        NOT NULL,
    "username"    varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
    "content"     text COLLATE "pg_catalog"."default"         NOT NULL,
    "parent_id"   int8,
    "status"      int4                                        NOT NULL DEFAULT 1,
    "deleted"     int4                                                 DEFAULT 0,
    "create_time" timestamp(6)                                         DEFAULT CURRENT_TIMESTAMP,
    "update_time" timestamp(6)                                         DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "t_agent_story_comment_pkey" PRIMARY KEY ("id"),
    CONSTRAINT "fk_agent_story_comment_parent" FOREIGN KEY ("parent_id") REFERENCES "public"."t_agent_story_comment" ("id") ON DELETE CASCADE ON UPDATE NO ACTION,
    CONSTRAINT "fk_agent_story_comment_story" FOREIGN KEY ("story_id") REFERENCES "public"."t_agent_story" ("id") ON DELETE CASCADE ON UPDATE NO ACTION
)
;


COMMENT ON COLUMN "public"."t_agent_story_comment"."id" IS '主键ID';

COMMENT ON COLUMN "public"."t_agent_story_comment"."story_id" IS '剧情ID';

COMMENT ON COLUMN "public"."t_agent_story_comment"."username" IS '用户名';

COMMENT ON COLUMN "public"."t_agent_story_comment"."content" IS '评论内容';

COMMENT ON COLUMN "public"."t_agent_story_comment"."parent_id" IS '父评论ID，顶级评论为null';

COMMENT ON COLUMN "public"."t_agent_story_comment"."status" IS '评论状态：1-正常，0-隐藏';

COMMENT ON COLUMN "public"."t_agent_story_comment"."deleted" IS '逻辑删除标志';

COMMENT ON COLUMN "public"."t_agent_story_comment"."create_time" IS '创建时间';

COMMENT ON COLUMN "public"."t_agent_story_comment"."update_time" IS '更新时间';

COMMENT ON TABLE "public"."t_agent_story_comment" IS '智能体剧情评论表';

DROP TABLE IF EXISTS "public"."t_agent_story_daily_usage_record";
DROP SEQUENCE IF EXISTS t_agent_story_daily_usage_record_id_seq;

-- 创建序列
CREATE SEQUENCE t_agent_story_daily_usage_record_id_seq START 1 CACHE 1;
CREATE TABLE "public"."t_agent_story_daily_usage_record"
(
    "id"          int8                                        NOT NULL DEFAULT nextval('t_agent_story_daily_usage_record_id_seq'::regclass),
    "story_id"    int8                                        NOT NULL,
    "username"    varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
    "use_date"    date                                        NOT NULL,
    "use_count"   int4                                                 DEFAULT 1,
    "create_time" timestamp(6)                                         DEFAULT CURRENT_TIMESTAMP,
    "update_time" timestamp(6)                                         DEFAULT CURRENT_TIMESTAMP,
    "deleted"     int4                                                 DEFAULT 0,
    "status"      int4                                                 DEFAULT 1,
    CONSTRAINT "t_agent_story_daily_usage_record_pkey" PRIMARY KEY ("id"),
    CONSTRAINT "fk_agent_daily_usage_story" FOREIGN KEY ("story_id") REFERENCES "public"."t_agent_story" ("id") ON DELETE CASCADE ON UPDATE NO ACTION
)
;


COMMENT ON COLUMN "public"."t_agent_story_daily_usage_record"."id" IS '主键ID';

COMMENT ON COLUMN "public"."t_agent_story_daily_usage_record"."story_id" IS '智能体ID';

COMMENT ON COLUMN "public"."t_agent_story_daily_usage_record"."username" IS '用户名';

COMMENT ON COLUMN "public"."t_agent_story_daily_usage_record"."use_date" IS '使用日期';

COMMENT ON COLUMN "public"."t_agent_story_daily_usage_record"."use_count" IS '当日使用次数';

COMMENT ON COLUMN "public"."t_agent_story_daily_usage_record"."deleted" IS '逻辑删除标志';

COMMENT ON COLUMN "public"."t_agent_story_daily_usage_record"."status" IS '状态：1-正常，0-禁用';

COMMENT ON TABLE "public"."t_agent_story_daily_usage_record" IS '用户每日使用智能体记录表';

DROP TABLE IF EXISTS "public"."t_agent_story_favorite";
DROP SEQUENCE IF EXISTS t_agent_story_favorite_id_seq;

-- 创建序列
CREATE SEQUENCE t_agent_story_favorite_id_seq START 1 CACHE 1;
CREATE TABLE "public"."t_agent_story_favorite"
(
    "id"          int8                                        NOT NULL DEFAULT nextval('t_agent_story_favorite_id_seq'::regclass),
    "story_id"    int8                                        NOT NULL,
    "username"    varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
    "status"      int4                                        NOT NULL DEFAULT 1,
    "deleted"     int4                                                 DEFAULT 0,
    "create_time" timestamp(6)                                         DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "t_agent_story_favorite_pkey" PRIMARY KEY ("id"),
    CONSTRAINT "fk_agent_story_favorite_story" FOREIGN KEY ("story_id") REFERENCES "public"."t_agent_story" ("id") ON DELETE CASCADE ON UPDATE NO ACTION
)
;


COMMENT ON COLUMN "public"."t_agent_story_favorite"."id" IS '主键ID';

COMMENT ON COLUMN "public"."t_agent_story_favorite"."story_id" IS '剧情ID';

COMMENT ON COLUMN "public"."t_agent_story_favorite"."username" IS '用户名';

COMMENT ON COLUMN "public"."t_agent_story_favorite"."status" IS '状态：1-收藏，0-取消收藏';

COMMENT ON COLUMN "public"."t_agent_story_favorite"."deleted" IS '逻辑删除标志';

COMMENT ON TABLE "public"."t_agent_story_favorite" IS '智能体剧情收藏表';


DROP TABLE IF EXISTS "public"."t_agent_story_interest";
DROP SEQUENCE IF EXISTS t_agent_story_interest_id_seq;

-- 创建序列
CREATE SEQUENCE t_agent_story_interest_id_seq START 1 CACHE 1;
CREATE TABLE "public"."t_agent_story_interest"
(
    "id"             int8                                        NOT NULL DEFAULT nextval('t_agent_story_interest_id_seq'::regclass),
    "username"       varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
    "story_id"       int8                                        NOT NULL,
    "interest_level" int4                                        NOT NULL,
    "source"         int4                                        NOT NULL,
    "marked"         int4                                                 DEFAULT 0,
    "update_time"    timestamp(6)                                         DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "t_agent_story_interest_pkey" PRIMARY KEY ("id"),
    CONSTRAINT "fk_agent_interest_story" FOREIGN KEY ("story_id") REFERENCES "public"."t_agent_story" ("id") ON DELETE CASCADE ON UPDATE NO ACTION
)
;


COMMENT ON COLUMN "public"."t_agent_story_interest"."id" IS '主键ID';

COMMENT ON COLUMN "public"."t_agent_story_interest"."username" IS '用户名';

COMMENT ON COLUMN "public"."t_agent_story_interest"."story_id" IS '智能体ID';

COMMENT ON COLUMN "public"."t_agent_story_interest"."interest_level" IS '兴趣程度：1-轻度兴趣，2-中度兴趣，3-高度兴趣';

COMMENT ON COLUMN "public"."t_agent_story_interest"."source" IS '来源：1-浏览, 2-搜索, 3-推荐, 4-历史行为';

COMMENT ON COLUMN "public"."t_agent_story_interest"."marked" IS '标记：0-未标记，1-已标记';

COMMENT ON TABLE "public"."t_agent_story_interest" IS '用户对智能体兴趣关系表';

DROP TABLE IF EXISTS "public"."t_agent_story_like";
DROP SEQUENCE IF EXISTS t_agent_story_like_id_seq;

-- 创建序列
CREATE SEQUENCE t_agent_story_like_id_seq START 1 CACHE 1;
CREATE TABLE "public"."t_agent_story_like"
(
    "id"          int8                                        NOT NULL DEFAULT nextval('t_agent_story_like_id_seq'::regclass),
    "story_id"    int8                                        NOT NULL,
    "username"    varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
    "status"      int4                                        NOT NULL DEFAULT 1,
    "deleted"     int4                                                 DEFAULT 0,
    "create_time" timestamp(6)                                         DEFAULT CURRENT_TIMESTAMP,
    "update_time" timestamp(6)                                         DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "t_agent_story_like_pkey" PRIMARY KEY ("id"),
    CONSTRAINT "fk_agent_story_like_story" FOREIGN KEY ("story_id") REFERENCES "public"."t_agent_story" ("id") ON DELETE CASCADE ON UPDATE NO ACTION
)
;


COMMENT ON COLUMN "public"."t_agent_story_like"."id" IS '主键ID';

COMMENT ON COLUMN "public"."t_agent_story_like"."story_id" IS '剧情ID';

COMMENT ON COLUMN "public"."t_agent_story_like"."username" IS '用户名';

COMMENT ON COLUMN "public"."t_agent_story_like"."status" IS '状态：1-点赞，0-取消点赞';

COMMENT ON COLUMN "public"."t_agent_story_like"."deleted" IS '逻辑删除标志';

COMMENT ON TABLE "public"."t_agent_story_like" IS '智能体剧情点赞表';

DROP TABLE IF EXISTS "public"."t_agent_story_tag";
DROP SEQUENCE IF EXISTS t_agent_story_tag_id_seq;

-- 创建序列
CREATE SEQUENCE t_agent_story_tag_id_seq START 1 CACHE 1;
CREATE TABLE "public"."t_agent_story_tag"
(
    "id"          int8                                        NOT NULL DEFAULT nextval('t_agent_story_tag_id_seq'::regclass),
    "create_time" timestamp(6)                                         DEFAULT CURRENT_TIMESTAMP,
    "update_time" timestamp(6)                                         DEFAULT CURRENT_TIMESTAMP,
    "deleted"     int4                                                 DEFAULT 0,
    "creator"     varchar(255) COLLATE "pg_catalog"."default",
    "updater"     varchar(255) COLLATE "pg_catalog"."default",
    "name"        varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
    "type"        int4                                        NOT NULL DEFAULT 1,
    "category"    int4                                        NOT NULL DEFAULT 1,
    "description" text COLLATE "pg_catalog"."default",
    "use_count"   int4                                                 DEFAULT 0,
    CONSTRAINT "t_agent_story_tag_pkey" PRIMARY KEY ("id")
)
;



COMMENT ON COLUMN "public"."t_agent_story_tag"."id" IS '主键ID';

COMMENT ON COLUMN "public"."t_agent_story_tag"."name" IS '标签名称';

COMMENT ON COLUMN "public"."t_agent_story_tag"."type" IS '标签类型：1-系统标签，2-用户自定义标签';

COMMENT ON COLUMN "public"."t_agent_story_tag"."category" IS '标签分类：1-背景标签，2-性格标签';

COMMENT ON COLUMN "public"."t_agent_story_tag"."description" IS '标签描述';

COMMENT ON COLUMN "public"."t_agent_story_tag"."use_count" IS '使用次数';

COMMENT ON TABLE "public"."t_agent_story_tag" IS '智能体剧情标签表';

DROP TABLE IF EXISTS "public"."t_agent_story_tag_relation";
DROP SEQUENCE IF EXISTS t_agent_story_tag_relation_id_seq;

-- 创建序列
CREATE SEQUENCE t_agent_story_tag_relation_id_seq START 1 CACHE 1;
CREATE TABLE "public"."t_agent_story_tag_relation"
(
    "id"       int8 NOT NULL DEFAULT nextval('t_agent_story_tag_relation_id_seq'::regclass),
    "story_id" int8 NOT NULL,
    "tag_id"   int8 NOT NULL,
    CONSTRAINT "t_agent_story_tag_relation_pkey" PRIMARY KEY ("id"),
    CONSTRAINT "fk_agent_story_tag_relation_story" FOREIGN KEY ("story_id") REFERENCES "public"."t_agent_story" ("id") ON DELETE CASCADE ON UPDATE NO ACTION,
    CONSTRAINT "fk_agent_story_tag_relation_tag" FOREIGN KEY ("tag_id") REFERENCES "public"."t_agent_story_tag" ("id") ON DELETE CASCADE ON UPDATE NO ACTION
)
;



COMMENT ON COLUMN "public"."t_agent_story_tag_relation"."id" IS '主键ID';

COMMENT ON COLUMN "public"."t_agent_story_tag_relation"."story_id" IS '智能体故事ID';

COMMENT ON COLUMN "public"."t_agent_story_tag_relation"."tag_id" IS '标签ID';

COMMENT ON TABLE "public"."t_agent_story_tag_relation" IS '智能体剧情标签关联表';

DROP TABLE IF EXISTS "public"."t_agent_story_type";
DROP SEQUENCE IF EXISTS t_agent_story_type_id_seq;

-- 创建序列
CREATE SEQUENCE t_agent_story_type_id_seq START 1 CACHE 1;
CREATE TABLE "public"."t_agent_story_type"
(
    "id"          int8                                        NOT NULL DEFAULT nextval('t_agent_story_type_id_seq'::regclass),
    "create_time" timestamp(6)                                         DEFAULT CURRENT_TIMESTAMP,
    "update_time" timestamp(6)                                         DEFAULT CURRENT_TIMESTAMP,
    "deleted"     int4                                                 DEFAULT 0,
    "creator"     varchar(255) COLLATE "pg_catalog"."default",
    "updater"     varchar(255) COLLATE "pg_catalog"."default",
    "name"        varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
    "description" text COLLATE "pg_catalog"."default",
    "use_count"   int4                                                 DEFAULT 0,
    "sequence"    int4                                                 DEFAULT 0,
    CONSTRAINT "t_agent_story_type_pkey" PRIMARY KEY ("id")
)
;


COMMENT ON COLUMN "public"."t_agent_story_type"."id" IS '主键ID';

COMMENT ON COLUMN "public"."t_agent_story_type"."name" IS '类型名称';

COMMENT ON COLUMN "public"."t_agent_story_type"."description" IS '类型描述';

COMMENT ON COLUMN "public"."t_agent_story_type"."use_count" IS '使用次数';

COMMENT ON COLUMN "public"."t_agent_story_type"."sequence" IS '排序';

COMMENT ON TABLE "public"."t_agent_story_type" IS '智能体剧情类型表';

DROP TABLE IF EXISTS "public"."t_agent_story_usage_record";
DROP SEQUENCE IF EXISTS t_agent_story_usage_record_id_seq;

-- 创建序列
CREATE SEQUENCE t_agent_story_usage_record_id_seq START 1 CACHE 1;
CREATE TABLE "public"."t_agent_story_usage_record"
(
    "id"          int8                                        NOT NULL DEFAULT nextval('t_agent_story_usage_record_id_seq'::regclass),
    "story_id"    int8                                        NOT NULL,
    "username"    varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
    "use_count"   int4                                                 DEFAULT 1,
    "update_time" timestamp(6)                                         DEFAULT CURRENT_TIMESTAMP,
    "deleted"     int4                                                 DEFAULT 0,
    "status"      int4                                                 DEFAULT 1,
    CONSTRAINT "t_agent_story_usage_record_pkey" PRIMARY KEY ("id"),
    CONSTRAINT "fk_agent_story_usage_record_story" FOREIGN KEY ("story_id") REFERENCES "public"."t_agent_story" ("id") ON DELETE CASCADE ON UPDATE NO ACTION
)
;


COMMENT ON COLUMN "public"."t_agent_story_usage_record"."id" IS '主键ID';

COMMENT ON COLUMN "public"."t_agent_story_usage_record"."story_id" IS '剧情ID';

COMMENT ON COLUMN "public"."t_agent_story_usage_record"."username" IS '用户名';

COMMENT ON COLUMN "public"."t_agent_story_usage_record"."use_count" IS '使用次数';

COMMENT ON COLUMN "public"."t_agent_story_usage_record"."update_time" IS '更新时间';

COMMENT ON COLUMN "public"."t_agent_story_usage_record"."deleted" IS '逻辑删除标志';

COMMENT ON COLUMN "public"."t_agent_story_usage_record"."status" IS '状态：1-正常，0-禁用';

COMMENT ON TABLE "public"."t_agent_story_usage_record" IS '智能体剧情使用记录表';


DROP TABLE IF EXISTS "public"."t_scene_agent_relation";
DROP SEQUENCE IF EXISTS t_scene_agent_relation_id_seq;

-- 创建序列
CREATE SEQUENCE t_scene_agent_relation_id_seq START 1 CACHE 1;
CREATE TABLE "public"."t_scene_agent_relation"
(
    "id"                int8 NOT NULL DEFAULT nextval('t_scene_agent_relation_id_seq'::regclass),
    "create_time"       timestamp(6)  DEFAULT CURRENT_TIMESTAMP,
    "update_time"       timestamp(6)  DEFAULT CURRENT_TIMESTAMP,
    "deleted"           int4          DEFAULT 0,
    "creator"           varchar(255) COLLATE "pg_catalog"."default",
    "updater"           varchar(255) COLLATE "pg_catalog"."default",
    "scene_id"          int8 NOT NULL,
    "agent_id"          int8 NOT NULL,
    "story_id"          int8 NOT NULL,
    "agent_role"        varchar(100) COLLATE "pg_catalog"."default",
    "appearance_order"  int4          DEFAULT 1,
    "is_main_character" bool          DEFAULT true,
    "initial_mood"      varchar(50) COLLATE "pg_catalog"."default",
    "status"            int4          DEFAULT 1,
    CONSTRAINT "t_scene_agent_relation_pkey" PRIMARY KEY ("id"),
    CONSTRAINT "fk_scene_agent_agent" FOREIGN KEY ("agent_id") REFERENCES "public"."t_agent" ("id") ON DELETE CASCADE ON UPDATE NO ACTION,
    CONSTRAINT "fk_scene_agent_scene" FOREIGN KEY ("scene_id") REFERENCES "public"."t_agent_story_scene" ("id") ON DELETE CASCADE ON UPDATE NO ACTION,
    CONSTRAINT "fk_scene_agent_story" FOREIGN KEY ("story_id") REFERENCES "public"."t_agent_story" ("id") ON DELETE CASCADE ON UPDATE NO ACTION
)
;


COMMENT ON COLUMN "public"."t_scene_agent_relation"."id" IS '主键ID';

COMMENT ON COLUMN "public"."t_scene_agent_relation"."create_time" IS '创建时间';

COMMENT ON COLUMN "public"."t_scene_agent_relation"."update_time" IS '更新时间';

COMMENT ON COLUMN "public"."t_scene_agent_relation"."scene_id" IS '场景ID';

COMMENT ON COLUMN "public"."t_scene_agent_relation"."agent_id" IS '智能体ID';

COMMENT ON COLUMN "public"."t_scene_agent_relation"."agent_role" IS '智能体在该场景中的角色';

COMMENT ON COLUMN "public"."t_scene_agent_relation"."appearance_order" IS '出场顺序';

COMMENT ON COLUMN "public"."t_scene_agent_relation"."is_main_character" IS '是否为主要角色';

COMMENT ON COLUMN "public"."t_scene_agent_relation"."initial_mood" IS '初始情绪状态';

COMMENT ON COLUMN "public"."t_scene_agent_relation"."status" IS '关联状态：0-禁用，1-正常';

COMMENT ON TABLE "public"."t_scene_agent_relation" IS '场景智能体关联表';

DROP TABLE IF EXISTS "public"."t_agent_story_statistics";
DROP SEQUENCE IF EXISTS t_agent_story_statistics_id_seq;

-- 创建序列
CREATE SEQUENCE t_agent_story_statistics_id_seq START 1 CACHE 1;

CREATE TABLE "public"."t_agent_story_statistics"
(
    "id"             int8 NOT NULL DEFAULT nextval('t_agent_story_statistics_id_seq'::regclass),
    "story_id"       int8 NOT NULL,
    "favorite_count" int4          DEFAULT 0,
    "comment_count"  int4          DEFAULT 0,
    "like_count"     int4          DEFAULT 0,
    CONSTRAINT "t_agent_story_statistics_pkey" PRIMARY KEY ("id"),
    CONSTRAINT "fk_agent_story_statistics_story" FOREIGN KEY ("story_id") REFERENCES "public"."t_agent_story" ("id") ON DELETE CASCADE ON UPDATE NO ACTION
)
;

COMMENT ON COLUMN "public"."t_agent_story_statistics"."id" IS '主键ID';

COMMENT ON COLUMN "public"."t_agent_story_statistics"."story_id" IS '剧情ID';

COMMENT ON COLUMN "public"."t_agent_story_statistics"."favorite_count" IS '收藏数量';

COMMENT ON COLUMN "public"."t_agent_story_statistics"."comment_count" IS '评论数量';

COMMENT ON COLUMN "public"."t_agent_story_statistics"."like_count" IS '点赞数量';

COMMENT ON TABLE "public"."t_agent_story_statistics" IS '智能体剧情统计表';